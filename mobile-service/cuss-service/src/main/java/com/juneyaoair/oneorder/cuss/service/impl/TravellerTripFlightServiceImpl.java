package com.juneyaoair.oneorder.cuss.service.impl;

import com.alibaba.fastjson2.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import com.juneyaoair.common.dto.base.BaseResultDTO;
import com.juneyaoair.cuss.fegin.CussBookingClient;
import com.juneyaoair.cuss.param.trip.MemberTravellerTripParam;
import com.juneyaoair.cuss.response.trip.MemberTravellerTicketInfo;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.request.SelectFlightCabinReq;
import com.juneyaoair.flightbasic.request.flightInfo.FlightInfoReqDTO;
import com.juneyaoair.flightbasic.response.CabinData;
import com.juneyaoair.flightbasic.response.SelectFlightCabinDto;
import com.juneyaoair.flightbasic.response.api.ApiAirPortInfoDto;
import com.juneyaoair.flightbasic.response.flightInfo.FlightInfoDTO;
import com.juneyaoair.flightbasic.utils.BaseRequestUtil;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.oneorder.api.basic.service.IBasicService;
import com.juneyaoair.oneorder.api.common.CacheService;
import com.juneyaoair.oneorder.api.crm.config.CrmConfig;
import com.juneyaoair.oneorder.api.order.service.IOrderService;
import com.juneyaoair.oneorder.common.common.UnifiedOrderResultEnum;
import com.juneyaoair.oneorder.common.constant.EmdCouponStatusEnum;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.common.dto.enums.InterFlagEnum;
import com.juneyaoair.oneorder.common.dto.enums.RouteTypeEnum;
import com.juneyaoair.oneorder.core.context.SecurityContextHolder;
import com.juneyaoair.oneorder.cuss.service.TravellerTripFlightService;
import com.juneyaoair.oneorder.flight.dto.*;
import com.juneyaoair.oneorder.order.constant.AirCompanyEnum;
import com.juneyaoair.oneorder.order.dto.FlightInfo;
import com.juneyaoair.oneorder.order.dto.PtPassengerInfo;
import com.juneyaoair.oneorder.order.dto.SegmentShow;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import com.juneyaoair.oneorder.tools.utils.EncoderHandler;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class TravellerTripFlightServiceImpl implements TravellerTripFlightService {

    private static final Logger log = LoggerFactory.getLogger(TravellerTripFlightServiceImpl.class);
    @Autowired
    private IBasicService basicService;

    @ApolloJsonValue("${juneyaoair.oneorder.cabinTypeMap:{'I':'I','D':'D'}}")
    private Map<String, String> cabinTypeMap;


    @ApolloJsonValue("${juneyaoair.oneorder.travellerCabinTypeMap:{'I':'I','D':'D,A,J'}}")
    private Map<String, String> travellerCabinTypeMap;

    @Autowired
    private CussBookingClient cussBookingClient;

    @Resource
    private CacheService cacheService;

    /**
     * 升舱券可用座位库存查询
     *
     * @param flightParam
     * @return
     */
    @Override
    public List<FlightTravellerTrip> selectTravellerTripFlight(FlightParam flightParam) {
        List<FlightTravellerTrip> travellerTripFlightInfoList = Lists.newArrayList();
        FlightInfoReqDTO flightInfoQuery = new FlightInfoReqDTO();
        flightInfoQuery.setFlightDate(flightParam.getFlightDate());
        flightInfoQuery.setFlightNo(flightParam.getFlightNo());
        flightInfoQuery.setDepCity(flightParam.getDepCity());
        flightInfoQuery.setArrCity(flightParam.getArrCity());
        BaseRequestDTO<FlightInfoReqDTO> baseRequest = BaseRequestUtil.createRequest(flightInfoQuery, ChannelCodeEnum.MOBILE.getChannelCode());
        List<FlightInfoDTO> flightInfoList = basicService.searchFlightInfo(baseRequest);
        if (CollectionUtils.isEmpty(flightInfoList)) {
            return travellerTripFlightInfoList;
        }
        for (FlightInfoDTO flightInfo : flightInfoList) {
            FlightTravellerTrip flightTravellerTrip = new FlightTravellerTrip();
            flightTravellerTrip.setFlightNo(flightInfo.getFlightNo());
            flightTravellerTrip.setDepCity(flightInfo.getDepCity());
            flightTravellerTrip.setArrCity(flightInfo.getArrCity());
            flightTravellerTrip.setFlightDate(flightInfo.getFlightDate());
            flightTravellerTrip.setArrAirport(flightInfo.getArrAirport());
            flightTravellerTrip.setDepAirport(flightInfo.getDepAirport());
            flightTravellerTrip.setStopAirport(flightInfo.getStopAirport());
            flightTravellerTrip.setDepDateTime(DateUtil.timeFormat(flightInfo.getDepDateTime()));
            flightTravellerTrip.setArrDateTime(DateUtil.timeFormat(flightInfo.getArrDateTime()));
            ApiAirPortInfoDto depAirPortInfo = cacheService.getLocalAirport(flightInfo.getDepAirport());
            ApiAirPortInfoDto arrAirportInfo = cacheService.getLocalAirport(flightInfo.getArrAirport());
            if (StringUtils.isNotBlank(flightInfo.getStopAirport())) {
                ApiAirPortInfoDto stopAirport = cacheService.getLocalAirport(flightInfo.getStopAirport());
                flightTravellerTrip.setStopAirportName(stopAirport.getAirPortName());
            }
            Long duration = DateUtil.calDuration(flightInfo.getDepDateChinaTime(),
                    "8",
                    flightInfo.getArrDateChinaTime(),
                    "8");
            flightTravellerTrip.setDuration(duration);
            String planeTypeName = basicService.getPlaneTypeName(flightInfo.getPlanType(), SecurityContextHolder.getLanguage().name());
            flightTravellerTrip.setPlaneTypeName(planeTypeName);
            flightTravellerTrip.setPlaneType(flightInfo.getPlanType());
            flightTravellerTrip.setDepCityName(depAirPortInfo.getCityName());
            flightTravellerTrip.setArrCityName(arrAirportInfo.getCityName());
            flightTravellerTrip.setDepAirportName(depAirPortInfo.getAirPortName());
            flightTravellerTrip.setArrAirportName(arrAirportInfo.getAirPortName());
            String cabinType = cabinTypeMap.get(flightInfo.getInterFlag());
            CabinData cabinData = selectFlightCabin(cabinType, flightInfo.getFlightNo(), flightInfo.getDepCity(), flightInfo.getArrCity(), flightInfo.getFlightDate());
            if (cabinData != null) {
                flightTravellerTrip.setCabinCode(cabinData.getCabinCode());
                flightTravellerTrip.setCabinNum(cabinData.getNum());
            } else {
                flightTravellerTrip.setCabinCode(cabinType);
                flightTravellerTrip.setCabinNum("0");
            }
            travellerTripFlightInfoList.add(flightTravellerTrip);
        }
        return travellerTripFlightInfoList;
    }

    private CabinData selectFlightCabin(String cabinType, String flightNo, String depCity, String arrCity, String flightDate) {
        SelectFlightCabinReq selectFlightCabinReq = new SelectFlightCabinReq();
        selectFlightCabinReq.setCabinType(cabinType);
        selectFlightCabinReq.setFlightNo(flightNo);
        selectFlightCabinReq.setDepCity(depCity);
        selectFlightCabinReq.setArrCity(arrCity);
        selectFlightCabinReq.setStartDate(flightDate);
        selectFlightCabinReq.setEndDate(flightDate);
        List<SelectFlightCabinDto> selectFlightCabins = basicService.selectFlightCabin(selectFlightCabinReq);
        if (CollectionUtils.isNotEmpty(selectFlightCabins)) {
            //获取舱位数量
            SelectFlightCabinDto selectFlightCabinDto = selectFlightCabins.stream()
                    .filter(flightCabin -> selectFlightCabinReq.getFlightNo().equals(flightCabin.getFlightNo())).findFirst().orElse(null);
            if (selectFlightCabinDto != null) {
                if (CollectionUtils.isNotEmpty(selectFlightCabinDto.getCabinList())) {
                    CabinData cabinData = selectFlightCabinDto.getCabinList().stream().findFirst().orElse(null);
                    return cabinData;
                }
            }
        }
        return null;
    }

    @Override
    public List<MemberTravellerFlightResp> getMemberTraveller(MemberTravellerTripParam memberTravellerTripParam) {
        List<MemberTravellerFlightInfoDto> memberTravellerFlightInfos = getMemberTravellerTrip(memberTravellerTripParam);
        List<MemberTravellerFlightResp> memberTravellerFlightRespList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(memberTravellerFlightInfos)) {
            Map<String, List<MemberTravellerFlightInfoDto>> listMap = memberTravellerFlightInfos.stream().collect(Collectors.groupingBy(MemberTravellerFlightInfoDto::getTicketNo));
            List<String> keysToRemove = new ArrayList<>();
            for (Map.Entry<String, List<MemberTravellerFlightInfoDto>> memberTravellerFlightInfo : listMap.entrySet()) {
                int segCount = 0;
                MemberTravellerFlightResp memberTravellerFlightResp = new MemberTravellerFlightResp();
                memberTravellerFlightResp.setTransfer(false);
                if (CollectionUtils.isNotEmpty(keysToRemove)&&keysToRemove.contains(memberTravellerFlightInfo.getKey())) {
                    continue;
                }
                memberTravellerFlightResp.setTicketNo(memberTravellerFlightInfo.getKey());
                List<SegmentShow> segmentShowList = new ArrayList<>();
                List<PtPassengerInfo> passengerInfoList = new ArrayList<>();
                List<FlightInfo> flightInfoList = new ArrayList<>();
                List<MemberTravellerFlightInfoDto> memberTravellerFlightInfoDtos = memberTravellerFlightInfo.getValue();
                //处理中转 和往返 两程票号不一样的情况
                if (CollectionUtils.isNotEmpty(memberTravellerFlightInfoDtos)) {
                    MemberTravellerFlightInfoDto  memberTravellerFlight=   memberTravellerFlightInfoDtos.stream()
                            .filter(memberTravellerFlightInfoDto ->
                                    StringUtils.isNotBlank(memberTravellerFlightInfoDto.getPriorTicket())||
                                    StringUtils.isNotBlank(memberTravellerFlightInfoDto.getNextTicket()))
                            .findFirst().orElse(null);
                    if (memberTravellerFlight != null) {
                       String  ticket;
                       if (StringUtils.isNotBlank(memberTravellerFlight.getPriorTicket())){
                           ticket=memberTravellerFlight.getPriorTicket();
                       }else  if (StringUtils.isNotBlank(memberTravellerFlight.getNextTicket())){
                           ticket=memberTravellerFlight.getNextTicket();
                       } else {
                           ticket = "";
                       }
                       List<MemberTravellerFlightInfoDto> memberTravellerFlightInfoDtoList= listMap.get(ticket);
                       if (CollectionUtils.isNotEmpty(memberTravellerFlightInfoDtoList)) {
                           memberTravellerFlightInfoDtos.addAll(memberTravellerFlightInfoDtoList);
                           keysToRemove.add(ticket);
                       }
                    }
                }

                 //过滤 出发城市三字码  到达日期 机型 国内国际标志为空的航班数据
                if (memberTravellerFlightInfoDtos.stream()
                        .anyMatch(memberTraveller ->
                                StringUtils.isBlank(memberTraveller.getArrDate())
                                        || StringUtils.isBlank(memberTraveller.getDepCityCode())
                                        || StringUtils.isBlank(memberTraveller.getPlaneType())
                                        || StringUtils.isBlank(memberTraveller.getInterFlag()))){
                    continue;
                }
                    if (CollectionUtils.isNotEmpty(memberTravellerFlightInfoDtos)) {
                        boolean interFlag = memberTravellerFlightInfoDtos.stream()
                                .anyMatch(memberTravellerFlightInfoDto -> InterFlagEnum.I.getCode().equals(memberTravellerFlightInfoDto.getInterFlag()));
                        memberTravellerFlightResp.setInterFlag(interFlag ? InterFlagEnum.I.getCode() : InterFlagEnum.D.getCode());
                        memberTravellerFlightResp.setRouteType(memberTravellerFlightInfoDtos.size() == 1 ? RouteTypeEnum.OW.getCode() : RouteTypeEnum.RT.getCode());
                        //按行程序号排序
                        memberTravellerFlightInfoDtos = memberTravellerFlightInfoDtos.stream()
                                .sorted(Comparator.comparing(memberTravellerFlightInfoDto ->memberTravellerFlightInfoDto.getFlightDate()+" "+memberTravellerFlightInfoDto.getDepTime()))
                                .collect(Collectors.toList());
                        for (MemberTravellerFlightInfoDto memberTravellerFlightInfoDto : memberTravellerFlightInfoDtos) {
                            segCount++;
                            if (segCount == 2) {
                                if (!memberTravellerFlightInfoDtos.get(segCount - 2).getDepCityCode()
                                        .equals(memberTravellerFlightInfoDtos.get(segCount - 1).getArrCityCode())) {
                                    memberTravellerFlightInfoDto.setFlightDirection("G");
                                    memberTravellerFlightResp.setRouteType("CT");
                                    memberTravellerFlightResp.setTransfer(true);
                                } else {
                                    memberTravellerFlightInfoDto.setFlightDirection("B");
                                    memberTravellerFlightResp.setRouteType(RouteTypeEnum.RT.getCode());
                                }
                            }
                            if (segCount == 1) {
                                memberTravellerFlightInfoDto.setFlightDirection("G");
                            }
                            if (segCount > 2) {
                                memberTravellerFlightInfoDto.setFlightDirection("B");
                                memberTravellerFlightResp.setRouteType(RouteTypeEnum.RT.getCode());
                            }
                            if (segCount == 4) {
                                memberTravellerFlightInfoDto.setFlightDirection("B");
                                memberTravellerFlightResp.setRouteType(RouteTypeEnum.RT.getCode());
                                memberTravellerFlightResp.setTransfer(true);
                            }
                        }
                        memberTravellerFlightInfoDtos.forEach(memberTravellerFlightInfoDto -> {
                            setsegmentShowList(memberTravellerFlightInfoDto, segmentShowList,
                                    memberTravellerFlightResp.isTransfer(), memberTravellerFlightResp.getInterFlag());
                            setpassengerInfoList(memberTravellerFlightInfoDto, passengerInfoList);
                            setflightInfoList(memberTravellerFlightInfoDto, flightInfoList);
                        });
                    }
                //去重
                List<PtPassengerInfo> passengerInfos = passengerInfoList.stream()
                        .collect(Collectors.toMap(
                                p -> p.getPassengerName(), // 以ID作为去重依据
                                Function.identity(),
                                (existing, replacement) -> existing // 保留先出现的元素
                        ))
                        .values()
                        .stream()
                        .collect(Collectors.toList());

                memberTravellerFlightResp.setFlightDate(segmentShowList.get(0).getFlightDate());
                memberTravellerFlightResp.setIfUp(segmentShowList.stream().allMatch(SegmentShow::isIfUp));
                memberTravellerFlightResp.setSegmentShowList(segmentShowList);
                memberTravellerFlightResp.setPassengerInfoList(passengerInfos);
                memberTravellerFlightResp.setFlightInfoList(flightInfoList);
                String sign = EncoderHandler.encodeBySHA1(memberTravellerFlightResp.createSinaParam() + CrmConfig.DEFAULT_TOKEN);
                memberTravellerFlightResp.setSign(sign);
                memberTravellerFlightRespList.add(memberTravellerFlightResp);
            }
            List<MemberTravellerFlightResp> upFlightRespList = memberTravellerFlightRespList.stream().filter(MemberTravellerFlightResp::isIfUp).collect(Collectors.toList());
            List<MemberTravellerFlightResp> notupFlightRespList = memberTravellerFlightRespList.stream().filter(memberTravellerFlightResp -> !memberTravellerFlightResp.isIfUp()).collect(Collectors.toList());

            upFlightRespList = upFlightRespList.
                    stream()
                    .sorted(Comparator.comparing(MemberTravellerFlightResp::getFlightDate))
                    .collect(Collectors.toList());
            upFlightRespList.addAll(notupFlightRespList);
            return upFlightRespList;
        }
        return Lists.newArrayList();
    }

    private void setflightInfoList(MemberTravellerFlightInfoDto memberTravellerFlightInfoDto, List<FlightInfo> flightInfoList) {
        FlightInfo flightInfo = new FlightInfo();
        flightInfo.setArrAirport(memberTravellerFlightInfoDto.getArrAirportCode());
        flightInfo.setArrAirportName(memberTravellerFlightInfoDto.getArrAirportName());
        flightInfo.setArrDateTime(memberTravellerFlightInfoDto.getArrTime());
        flightInfo.setFlightDate(memberTravellerFlightInfoDto.getFlightDate());
        flightInfo.setFlightNo(memberTravellerFlightInfoDto.getMarketingFlightNo());
        flightInfo.setDepAirport(memberTravellerFlightInfoDto.getDepAirportCode());
        flightInfo.setDepDateTime(memberTravellerFlightInfoDto.getDepTime());
        flightInfo.setArrDateTime(memberTravellerFlightInfoDto.getArrTime());
        flightInfo.setArrCity(memberTravellerFlightInfoDto.getArrCityCode());
        flightInfo.setDepAirportName(memberTravellerFlightInfoDto.getDepAirportName());
        flightInfo.setArrCityName(memberTravellerFlightInfoDto.getArrCityName());
        flightInfo.setDepCityName(memberTravellerFlightInfoDto.getDepCityName());
        flightInfo.setArrCity(memberTravellerFlightInfoDto.getArrCityCode());
        flightInfo.setDepCity(memberTravellerFlightInfoDto.getDepCityCode());
        flightInfo.setDuration(Double.valueOf(memberTravellerFlightInfoDto.getDuration()));
        flightInfo.setFlightDirection(memberTravellerFlightInfoDto.getFlightDirection());
        flightInfo.setCabinCode(memberTravellerFlightInfoDto.getMarketingCabin());
        flightInfo.setFType(memberTravellerFlightInfoDto.getPlaneType());
        flightInfo.setTicketStatus(EmdCouponStatusEnum.checkEmdCouponStatusEnum(memberTravellerFlightInfoDto.getCouponStatus()) == null ?
                "" : EmdCouponStatusEnum.checkEmdCouponStatusEnum(memberTravellerFlightInfoDto.getCouponStatus()).getDescription());
        flightInfoList.add(flightInfo);
    }

    private void setsegmentShowList(MemberTravellerFlightInfoDto memberTravellerFlightInfoDto, List<SegmentShow> segmentShowList, boolean isTransfer, String interFlag) {
        SegmentShow segmentShow = new SegmentShow();
        segmentShow.setFlightNo(memberTravellerFlightInfoDto.getMarketingFlightNo());
        segmentShow.setArrAirPortCode(memberTravellerFlightInfoDto.getArrAirportCode());
        segmentShow.setArrCityName(memberTravellerFlightInfoDto.getArrCityName());
        segmentShow.setArrAirPortName(memberTravellerFlightInfoDto.getArrAirportName());
        segmentShow.setFlightDate(memberTravellerFlightInfoDto.getFlightDate());
        segmentShow.setDeptAirPortCode(memberTravellerFlightInfoDto.getDepAirportCode());
        segmentShow.setDeptCityName(memberTravellerFlightInfoDto.getDepCityName());
        segmentShow.setDeptAirPortName(memberTravellerFlightInfoDto.getDepAirportName());
        segmentShow.setArrTime(memberTravellerFlightInfoDto.getArrTime());
        segmentShow.setDeptTime(memberTravellerFlightInfoDto.getDepTime());
        segmentShow.setArrTerminal(memberTravellerFlightInfoDto.getArrTerminal());
        segmentShow.setDeptCityCode(memberTravellerFlightInfoDto.getDepCityCode());
        segmentShow.setArrCityCode(memberTravellerFlightInfoDto.getArrCityCode());
        segmentShow.setDeptTerminal(memberTravellerFlightInfoDto.getDepTerminal());
        segmentShow.setCabin(memberTravellerFlightInfoDto.getMarketingCabin());
        segmentShow.setCabinClassName(memberTravellerFlightInfoDto.getCabinName());
        segmentShow.setFlightDirection(memberTravellerFlightInfoDto.getFlightDirection());
        segmentShow.setStopCity(memberTravellerFlightInfoDto.getStopCityName());
        segmentShow.setStopCityName(memberTravellerFlightInfoDto.getStopCityName());
        segmentShow.setFlightTime(Long.valueOf(memberTravellerFlightInfoDto.getDuration()));
        segmentShow.setPlaneType(memberTravellerFlightInfoDto.getPlaneType());
        segmentShow.setTicketStatus(EmdCouponStatusEnum.checkEmdCouponStatusEnum(memberTravellerFlightInfoDto.getCouponStatus()) == null ?
                "" : EmdCouponStatusEnum.checkEmdCouponStatusEnum(memberTravellerFlightInfoDto.getCouponStatus()).getDescription());

        if (isTransfer) {
            segmentShow.setIfUp(false);
            segmentShow.setNotUpReason("该行程为中转联程航班，暂不支持升舱");
        } else if (!memberTravellerFlightInfoDto.getOperationFlightNo().startsWith(AirCompanyEnum.HO.getAirCompanyCode())) {
            segmentShow.setIfUp(false);
            segmentShow.setNotUpReason("该行程非吉祥航空实际承运航班，暂不支持升舱");
        } else if (!memberTravellerFlightInfoDto.getCouponStatus().equals("O")) {
            segmentShow.setIfUp(false);
            segmentShow.setNotUpReason("该行程已办理值机，若需升舱请取消值机");
        } else if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(memberTravellerFlightInfoDto.getPsmSpecialSet())) {
            segmentShow.setIfUp(false);
            segmentShow.setNotUpReason("该行程已申请无陪儿童、轮椅、担架等特殊服务，暂不支持升舱");
        } else if ("X".equalsIgnoreCase(memberTravellerFlightInfoDto.getOperationCabin())
                || "I".equalsIgnoreCase(memberTravellerFlightInfoDto.getOperationCabin())
                || "N".equalsIgnoreCase(memberTravellerFlightInfoDto.getOperationCabin())) {   //团队客票（E舱）、中转联程客票（G舱）
            segmentShow.setIfUp(false);
            segmentShow.setNotUpReason("该行程为免票/积分兑换/团队客票/携宠客票，暂时不支持升舱");
        } else {
            String cabinType = travellerCabinTypeMap.get(interFlag);
            CabinData cabinData = selectFlightCabin(cabinType, memberTravellerFlightInfoDto.getOperationFlightNo(),
                    memberTravellerFlightInfoDto.getDepCityCode(), memberTravellerFlightInfoDto.getArrCityCode(), memberTravellerFlightInfoDto.getFlightDate());
            if (cabinData != null) {
                segmentShow.setCabinSoldOut(false);
                String num = cabinData.getNum();
                num = "A".equals(num) ? "9" : num;
                segmentShow.setIfUp(Integer.parseInt(num) > 0);
            } else {
                segmentShow.setCabinSoldOut(true);
                segmentShow.setIfUp(false);
                segmentShow.setNotUpReason("舱位已售罄");
            }

        }
        segmentShowList.add(segmentShow);
    }

    private void setpassengerInfoList(MemberTravellerFlightInfoDto memberTravellerFlightInfoDto, List<PtPassengerInfo> passengerInfoList) {
        PtPassengerInfo ptPassengerInfo = new PtPassengerInfo();
        ptPassengerInfo.setBirthdate(memberTravellerFlightInfoDto.getBirthdate());
        ptPassengerInfo.setPassengerName(memberTravellerFlightInfoDto.getTravellerName());
        ptPassengerInfo.setPassengerType(memberTravellerFlightInfoDto.getPassengerType());
        ptPassengerInfo.setPassengerNO(0);
        passengerInfoList.add(ptPassengerInfo);
    }


    private List<MemberTravellerFlightInfoDto> getMemberTravellerTrip(MemberTravellerTripParam memberTravellerTripParam) {

        com.juneyaoair.common.dto.base.BaseRequestDTO<MemberTravellerTripParam> travellerTripParamBase = new com.juneyaoair.common.dto.base.BaseRequestDTO<>();
        memberTravellerTripParam.setPsmSpecial(true);
        travellerTripParamBase.setIp(SecurityContextHolder.getOriginIp());
        travellerTripParamBase.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        travellerTripParamBase.setVersion("10");
        travellerTripParamBase.setRequest(memberTravellerTripParam);
        String uuid = UUID.randomUUID().toString();

        log.info("调用CUSS行程接口开始，请求ID：{} 请求参数：{}", uuid, JSON.toJSONString(travellerTripParamBase));
        BaseResultDTO<com.juneyaoair.cuss.response.trip.MemberTravellerTripResult> baseResult = cussBookingClient.getMemberTravellerTrip(travellerTripParamBase, uuid);
        log.info("调用CUSS行程接口结束，请求ID：{} 返回结果：{}", uuid, JSON.toJSONString(baseResult));
        System.out.println("返回结果:"+JSON.toJSONString(baseResult));
        if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(baseResult.getResultCode())) {
            List<MemberTravellerFlightInfoDto> memberTravellerFlightInfos = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(baseResult.getResult().getTravellerTripInfoList())) {
                baseResult.getResult().getTravellerTripInfoList().forEach(TravellerTripInfoDto -> {
                    MemberTravellerFlightInfoDto memberTravellerFlightInfoDto = new MemberTravellerFlightInfoDto();
                    BeanUtils.copyProperties(TravellerTripInfoDto, memberTravellerFlightInfoDto);
                    if (CollectionUtils.isNotEmpty(TravellerTripInfoDto.getTravellerList())) {
                        MemberTravellerTicketInfo memberTravellerTicketInfo = TravellerTripInfoDto.getTravellerList().get(0);
                        memberTravellerFlightInfoDto.setTravellerName(memberTravellerTicketInfo.getTravellerName());
                        memberTravellerFlightInfoDto.setTicketNo(memberTravellerTicketInfo.getTicketNo());
                        memberTravellerFlightInfoDto.setSegNo(memberTravellerTicketInfo.getSegNo());
                        memberTravellerFlightInfoDto.setMarketingCabin(memberTravellerTicketInfo.getMarketingCabin());
                        memberTravellerFlightInfoDto.setOperationCabin(memberTravellerTicketInfo.getOperationCabin());
                        memberTravellerFlightInfoDto.setCabinName(memberTravellerTicketInfo.getCabinName());
                        memberTravellerFlightInfoDto.setSeatNo(memberTravellerTicketInfo.getSeatNo());
                        memberTravellerFlightInfoDto.setCouponStatus(memberTravellerTicketInfo.getCouponStatus());
                        memberTravellerFlightInfoDto.setBaggageAllowance(memberTravellerTicketInfo.getBaggageAllowance());
                        memberTravellerFlightInfoDto.setBirthdate(memberTravellerTicketInfo.getBirthdate());
                        memberTravellerFlightInfoDto.setPassengerType(memberTravellerTicketInfo.getPassengerType());
                        memberTravellerFlightInfoDto.setSsrCodeSet(memberTravellerTicketInfo.getSsrCodeSet());
                        memberTravellerFlightInfoDto.setPsmSpecialSet(memberTravellerTicketInfo.getPsmSpecialSet());
                        memberTravellerFlightInfoDto.setNextTicket(memberTravellerTicketInfo.getNextTicket());
                        memberTravellerFlightInfoDto.setPriorTicket(memberTravellerTicketInfo.getPriorTicket());
                    }
                    memberTravellerFlightInfos.add(memberTravellerFlightInfoDto);
                });


            }
            return memberTravellerFlightInfos;
        } else {
            log.info("获取会员行程信息,报错{}", JSON.toJSONString(baseResult));
            throw MultiLangServiceException.fail("请求远程服务异常" + JSON.toJSONString(baseResult));
        }
    }
}
