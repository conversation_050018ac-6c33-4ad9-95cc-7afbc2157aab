package com.juneyaoair.oneorder.cuss.bean.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description 客票基础信息(基础极验)
 * @created 2023/11/16 13:48
 */
@Data
@ToString(callSuper = true)
public class GeeTestTicketVerifyParam {

    @NotBlank(message = "票号不能为空")
    @ApiModelProperty(value = "票号", required = true)
    private String ticketNo;

    @NotBlank(message = "乘客姓名不能为空")
    @ApiModelProperty(value = "乘客姓名", required = true)
    private String passengerName;

    @NotBlank(message = "航班号不能为空")
    @ApiModelProperty(value = "航班号", required = true)
    private String flightNo;


    private String flightDate;

    //@NotBlank(message = "出发城市三字码不能为空")
    //@ApiModelProperty(value = "出发城市三字码", required = true)
    //private String depCityCode;

    //@NotBlank(message = "到达城市三字码不能为空")
    //@ApiModelProperty(value = "到达城市三字码", required = true)
    //private String arrCityCode;

}
