package com.juneyaoair.oneorder.cuss.bean.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 16:07 2018/12/11
 * @Modified by:
 */
@Data
public class CheckInCountInfo {

    @NotBlank(message = "旅客姓名不允许为空")
    @ApiModelProperty(value = "旅客姓名")
    private String psrName;

    @NotBlank(message = "票号不允许为空")
    @ApiModelProperty(value = "票号")
    private String etCode;

    @NotBlank(message = "行程序号不允许为空")
    @ApiModelProperty(value = "行程序号")
    private Integer segNo;

}
