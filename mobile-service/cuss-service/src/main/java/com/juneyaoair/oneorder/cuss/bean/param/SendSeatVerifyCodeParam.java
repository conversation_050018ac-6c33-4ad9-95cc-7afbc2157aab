package com.juneyaoair.oneorder.cuss.bean.param;

import com.juneyaoair.oneorder.common.dto.GeetestInterface;
import com.juneyaoair.oneorder.cuss.bean.intf.SeatOrderBase;
import com.juneyaoair.oneorder.cuss.bean.intf.TicketBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description 客票基础信息(基础极验)
 * @created 2023/11/16 13:48
 */
@Data
public class SendSeatVerifyCodeParam implements TicketBase, SeatOrderBase, GeetestInterface {

    @NotBlank(message = "票号不能为空")
    @ApiModelProperty(value = "票号", required = true)
    private String tktNo;

    @NotBlank(message = "航班日期不能为空")
    @ApiModelProperty(value = "航班日期 格式：yyyy-MM-dd", required = true)
    private String flightDate;

    @NotBlank(message = "航班号不能为空")
    @ApiModelProperty(value = "航班号", required = true)
    private String flightNo;

    @NotBlank(message = "出发机场不能为空")
    @ApiModelProperty(value = "出发机场", required = true)
    private String depAirportCode;

    @NotBlank(message = "到达机场不能为空")
    @ApiModelProperty(value = "到达机场", required = true)
    private String arrAirportCode;

    @ApiModelProperty(value = "订单号（优先座位必须）")
    private String orderNo;

    @ApiModelProperty(value = "渠道订单号（优先座位必须）")
    private String channelOrderNo;

    @ApiModelProperty(value = "权益券码（优先座位必须）")
    private String couponCode;

    @ApiModelProperty(value="极验流水号")
    private String geetest_challenge;

    @ApiModelProperty(value="极验验证串")
    private String geetest_validate;

    @ApiModelProperty(value="极验时间戳")
    private String geetest_seccode;

    @ApiModelProperty(value = "极验使用CLIENT_TYPE,web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式", allowableValues = "web,h5,native",
            notes = "web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式",
            example = "web,h5,native")
    private String client_type;

}
