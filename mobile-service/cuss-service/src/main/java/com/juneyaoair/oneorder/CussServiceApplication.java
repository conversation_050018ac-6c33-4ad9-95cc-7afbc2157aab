package com.juneyaoair.oneorder;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class},
        scanBasePackages = "com.juneyaoair")
@EnableDiscoveryClient
@EnableApolloConfig(value = {"cuss", "application"})
@EnableFeignClients(basePackages = {"com.juneyaoair"})
public class CussServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(CussServiceApplication.class);
    }
}
