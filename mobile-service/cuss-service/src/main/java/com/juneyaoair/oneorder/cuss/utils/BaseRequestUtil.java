package com.juneyaoair.oneorder.cuss.utils;

import com.juneyaoair.common.dto.base.BaseRequestDTO;
import com.juneyaoair.cuss.enums.LanguageEnum;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.common.dto.RequestInterface;

/**
 * @Author: caolei
 * @Description: BaseRequest工具
 * @Date: 2022/10/12 9:15
 * @Modified by:
 */
public class BaseRequestUtil {

    private BaseRequestUtil(){}

    /**
     * 生成基础请求参数
     * @param requestData
     * @return
     * @param <T>
     */
    public static <T> BaseRequestDTO<T> createRequest(RequestDataDto<T> requestData){
        return createRequest(requestData, requestData.getData());
    }

    /**
     * 生成基础请求参数
     * @param requestData
     * @param t
     * @return
     * @param <T>
     */
    public static <T> BaseRequestDTO<T> createRequest(RequestInterface requestData, T t) {
        return createRequest(requestData, null, t);
    }

    /**
     * 生成基础请求参数
     * @param requestData
     * @param t
     * @return
     * @param <T>
     */
    public static <T> BaseRequestDTO<T> createRequest(RequestInterface requestData, String userNo, T t){
        BaseRequestDTO<T> baseRequest = new BaseRequestDTO<>();
        baseRequest.setRequest(t);
        baseRequest.setIp(requestData.getOriginIp());
        baseRequest.setVersion("1.0");
        baseRequest.setUserNo(userNo);
        baseRequest.setLanguage(LanguageEnum.valueOf(requestData.getLanguage().name()));
        baseRequest.setChannelCode(requestData.getChannelNo());
        return baseRequest;
    }

}