package com.juneyaoair.oneorder.cuss.service;


import com.juneyaoair.cuss.param.trip.MemberTravellerTripParam;
import com.juneyaoair.oneorder.flight.dto.*;

import java.util.List;

public interface TravellerTripFlightService {

    /**
     * 升舱券可用座位库存查询
     * @param flightParam
     * @return
     */
    List<FlightTravellerTrip>  selectTravellerTripFlight(FlightParam flightParam);

    /**
     * 升舱券我的行程
     * @param memberTravellerTripParam
     * @return
     */
    List<MemberTravellerFlightResp> getMemberTraveller(MemberTravellerTripParam memberTravellerTripParam);
}
