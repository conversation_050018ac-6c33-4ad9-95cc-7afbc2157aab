package com.juneyaoair.oneorder.cuss.controller;

import com.juneyaoair.cuss.request.travel.FlightBoardQueryBeanDTO;
import com.juneyaoair.cuss.response.travel.BoardInfoResultDTO;
import com.juneyaoair.cuss.service.CussCustomerService;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.cuss.utils.BaseRequestUtil;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description 航信服务
 * @created 2024/07/26 17:24
 */
@Slf4j
@Api(value = "航信服务")
@RestController
public class TravelSkyController {

    @Autowired
    private CussCustomerService cussCustomerService;

    @ApiOperation(value = "查询航班登机口、登机时间、出发时间信息")
    @PostMapping("/travelSky/queryFlightBoardInAirport")
    public ResponseData<BoardInfoResultDTO> queryFlightBoardInAirport(@RequestBody @Validated RequestDataDto<FlightBoardQueryBeanDTO> requestData) {
        BoardInfoResultDTO boardInfoResult = cussCustomerService.queryFlightBoardInAirport(BaseRequestUtil.createRequest(requestData));
        return ResponseData.success(boardInfoResult);
    }

}
