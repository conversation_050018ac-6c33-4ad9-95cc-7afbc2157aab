package com.juneyaoair.oneorder.cuss.bean.result;

import com.juneyaoair.cuss.dto.booking.response.seat.v2.SeatMapInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: caolei
 * @Description: 值机选座座位图V3
 * @Date: 2024/08/05 10:32
 * @Modified by:
 */
@Data
public class SeatMapInfoBff {

    @ApiModelProperty(value = "座位图相关信息")
    private SeatMapInfo seatMapInfo;

    /** 2024-08-30 新版本升级后全部使用数组返回 保留一段时间兼容老版本 未来可删除 */
    @Deprecated
    @ApiModelProperty(value = "预留选座数据", hidden = true)
    private SeatSelectInfo selectSeatInfo;

    @ApiModelProperty(value = "预留选座数据")
    private List<SeatSelectInfo> selectSeatInfoList;

}
