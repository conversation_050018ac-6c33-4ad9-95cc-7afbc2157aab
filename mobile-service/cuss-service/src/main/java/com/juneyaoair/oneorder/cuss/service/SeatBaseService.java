package com.juneyaoair.oneorder.cuss.service;

import com.juneyaoair.common.dto.base.BaseRequestDTO;
import com.juneyaoair.cuss.dto.booking.request.seat.v2.SeatMapRequest;
import com.juneyaoair.cuss.dto.booking.response.seat.v2.SeatMapInfo;
import com.juneyaoair.cuss.param.seat.AddPeerParam;
import com.juneyaoair.cuss.request.ticket.TicketInfoParam;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.cuss.bean.result.SeatSelectInfo;

import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @Description 值机选座服务
 * @created 2024/08/28 17:24
 */
public interface SeatBaseService {

    /**
     * 获取票号选座信息
     * @param requestData
     * @param traceId
     * @param ticketInfoParam
     * @return
     */

    Future<SeatSelectInfo> getSelectSeatInfo(RequestDataDto requestData, String traceId, TicketInfoParam ticketInfoParam);

    /**
     * 查询座位图
     * @param traceId
     * @param baseRequest
     * @return
     */
    Future<SeatMapInfo> querySeatMapV3(String traceId, BaseRequestDTO<SeatMapRequest> baseRequest);

    /**
     * 添加同行人
     * @param requestData
     * @return
     */
    Future<SeatSelectInfo> addPeer(RequestDataDto requestData, String traceId, AddPeerParam addPeerParam);

}
