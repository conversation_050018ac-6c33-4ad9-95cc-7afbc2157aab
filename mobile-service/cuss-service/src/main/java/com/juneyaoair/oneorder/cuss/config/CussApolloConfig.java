package com.juneyaoair.oneorder.cuss.config;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.juneyaoair.oneorder.cuss.bean.result.InsureApolloInfo;
import com.juneyaoair.oneorder.cuss.bean.result.InsurePopMessage;
import lombok.Data;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025/01/09 10:10
 */
@Data
@Configuration
public class CussApolloConfig {

    /** 选座保险弹窗信息 */
    @ApolloJsonValue("${cuss.insurePopMessage:{}}")
    private Map<String, InsurePopMessage> insurePopMessageMap;

    /** 可购买保险清单 */
    @ApolloJsonValue("${cuss.insure:{}}")
    private Map<String, List<InsureApolloInfo>> insureMap;

    /** 是否启用首页行程 */
    @ApolloJsonValue("${cuss.firstTravellerTrip.flag:true}")
    private boolean firstTravellerTripFlag;

}
