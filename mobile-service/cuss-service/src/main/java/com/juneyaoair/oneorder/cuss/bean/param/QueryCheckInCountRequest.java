package com.juneyaoair.oneorder.cuss.bean.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 16:02 2018/12/11
 * @Modified by:
 */
@Data
public class QueryCheckInCountRequest {

    @NotBlank(message = "航班号不允许为空")
    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @NotBlank(message = "航班日期不允许为空")
    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @NotBlank(message = "出发机场三字码不允许为空")
    @ApiModelProperty(value = "出发机场三字码")
    private String depAirportCode;

    @NotBlank(message = "到达机场三字码不允许为空")
    @ApiModelProperty(value = "到达机场三字码")
    private String arrAirportCode;

    @NotNull(message = "旅客列表不能为空")
    private List<CheckInCountInfo> checkInCountInfos;

}
