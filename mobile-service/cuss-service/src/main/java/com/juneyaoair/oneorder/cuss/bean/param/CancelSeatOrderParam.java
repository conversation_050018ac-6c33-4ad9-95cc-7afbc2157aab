package com.juneyaoair.oneorder.cuss.bean.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: caolei
 * @Description: 取消选座订单
 * @Date: 2024/12/18 18:34
 * @Modified by:
 */
@Data
public class CancelSeatOrderParam {

    @NotBlank(message = "订单号不能为空")
    @ApiModelProperty(value = "订单号", required = true)
    private String orderNo;

    @NotBlank(message = "渠道订单号不能为空")
    @ApiModelProperty(value = "渠道订单号", required = true)
    private String channelOrderNo;

    @ApiModelProperty(value = "退订说明")
    private String cancelReason;

}
