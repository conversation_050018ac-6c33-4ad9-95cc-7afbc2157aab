package com.juneyaoair.oneorder.cuss.bean.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SeatSelectInfo {

    @ApiModelProperty(value = "旅客姓名")
    private String psrName;

    @ApiModelProperty(value = "票号")
    private String ticketNo;

    @ApiModelProperty(value = "pnrNo")
    private String pnrNo;

    @ApiModelProperty(value = "行程序号")
    private String segNo;

    @ApiModelProperty(value = "舱位")
    private String cabin;

    @ApiModelProperty(value = "子舱位所对应的舱位等级")
    private String cabinClass;

    @ApiModelProperty(value = "座位号")
    private String seatNo;

    /***
     * 国际官网下线后可删除 原本用于本人判断 使用self识别
     */
    @Deprecated
    @ApiModelProperty(value = "会员卡号")
    private String ffpCardNo;

    @ApiModelProperty(value = "是否本人 true:本人 false:非本人")
    private boolean self;

    @ApiModelProperty(value = "选座会员等级code")
    private Integer seatMemberCode;

    @ApiModelProperty(value = "航班日年龄")
    private Integer flightAge;

    @ApiModelProperty(value = "乘客类型 ADT：成人 CHD：儿童 UCCHD/UM：无陪儿童 INF：婴儿")
    private String passengerType;

}
