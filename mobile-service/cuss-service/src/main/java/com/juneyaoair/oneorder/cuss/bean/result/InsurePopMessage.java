package com.juneyaoair.oneorder.cuss.bean.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName InsurePopMessage
 * @Description
 * @createTime 2025年01月09日 14:20
 */
@Data
public class InsurePopMessage {

    @ApiModelProperty(value = "主标题")
    private String mainTitle;

    @ApiModelProperty(value = "副标题")
    private String subTitle;

    @ApiModelProperty(value = "文案标题")
    private String paperworkTitle;

    @ApiModelProperty(value = "图片地址")
    private String picUrl;

    @ApiModelProperty(value = "pdf地址")
    private String pdfUrl;

    @ApiModelProperty(value = "文案内容")
    private List<PaperworkEntity> paperworkContent;

}
