package com.juneyaoair.oneorder.cuss.service.impl;

import com.alibaba.fastjson.JSON;
import com.juneyaoair.common.dto.base.BaseRequestDTO;
import com.juneyaoair.common.dto.base.BaseResultDTO;
import com.juneyaoair.cuss.dto.booking.request.member.MemberRequest;
import com.juneyaoair.cuss.dto.booking.request.seat.v2.SeatMapRequest;
import com.juneyaoair.cuss.dto.booking.response.member.MemberResponse;
import com.juneyaoair.cuss.dto.booking.response.seat.v2.SeatMapInfo;
import com.juneyaoair.cuss.enums.ENUM_RESPONSE_STATUS;
import com.juneyaoair.cuss.exception.CommonException;
import com.juneyaoair.cuss.fegin.CussBookingClient;
import com.juneyaoair.cuss.param.seat.AddPeerParam;
import com.juneyaoair.cuss.request.ticket.TicketInfoParam;
import com.juneyaoair.cuss.response.seat.SeatSelectInfoResult;
import com.juneyaoair.cuss.response.ticket.TicketInfoResult;
import com.juneyaoair.cuss.util.CussClientUtil;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.cuss.bean.result.SeatSelectInfo;
import com.juneyaoair.oneorder.cuss.service.SeatBaseService;
import com.juneyaoair.oneorder.cuss.utils.BaseRequestUtil;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @Description 值机选座服务
 * @created 2024/08/28 17:24
 */
@Slf4j
@Service
public class SeatBaseServiceImpl implements SeatBaseService {

    @Autowired
    private CussBookingClient cussBookingClient;

    @Async("taskExecutor")
    @Override
    public Future<SeatSelectInfo> getSelectSeatInfo(RequestDataDto requestData, String traceId, TicketInfoParam ticketInfoParam) {
        String uuid = UUID.randomUUID().toString();
        BaseRequestDTO<TicketInfoParam> ticketInfoParamBase = BaseRequestUtil.createRequest(requestData, ticketInfoParam);
        log.info("调用CUSS获取客票相关信息开始，traceId:{} 请求ID：{} 请求参数：{}", traceId, uuid, JSON.toJSONString(ticketInfoParamBase));
        BaseResultDTO<TicketInfoResult> ticketInfoParamBaseResult = cussBookingClient.getTicketInfo(ticketInfoParamBase, uuid);
        log.info("调用CUSS获取客票相关信息结束，traceId:{} 请求ID：{} 返回结果：{}", traceId, uuid, JSON.toJSONString(ticketInfoParamBaseResult));
        TicketInfoResult ticketInfoResult = CussClientUtil.getResult(ticketInfoParamBaseResult);
        SeatSelectInfo selectSeatInfo = new SeatSelectInfo();
        selectSeatInfo.setTicketNo(ticketInfoParam.getTicketNo());
        if (null != ticketInfoResult) {
            selectSeatInfo.setPsrName(ticketInfoResult.getTravellerName());
            selectSeatInfo.setSeatNo(ticketInfoResult.getSeatNo());
            selectSeatInfo.setPnrNo(ticketInfoResult.getPnrNo());
            selectSeatInfo.setSegNo(ticketInfoResult.getSegNo());
            selectSeatInfo.setCabin(ticketInfoResult.getCabin());
            selectSeatInfo.setCabinClass(ticketInfoResult.getCabinClass());
        }
        MemberRequest memberRequest = new MemberRequest();
        memberRequest.setTicketNumber(ticketInfoParam.getTicketNo());
        BaseRequestDTO<MemberRequest> memberRequestBase = BaseRequestUtil.createRequest(requestData, memberRequest);
        log.info("调用CUSS获取会员相关信息（基于票号或证件号）开始，traceId:{} 请求ID：{} 请求参数：{}", traceId, uuid, JSON.toJSONString(memberRequestBase));
        BaseResultDTO<MemberResponse> memberBaseResult = cussBookingClient.getMemberInfo(memberRequestBase, uuid);
        log.info("调用CUSS获取会员相关信息（基于票号或证件号）结束，traceId:{} 请求ID：{} 返回结果：{}", traceId, uuid, JSON.toJSONString(memberBaseResult));
        MemberResponse memberInfo = CussClientUtil.getResult(memberBaseResult);
        if (null != memberInfo) {
            int flightAge = DateUtil.getAgeByBirth(memberInfo.getTravellerBirthdate(), ticketInfoParam.getFlightDate(), DateUtil.YYYY_MM_DD_PATTERN);
            selectSeatInfo.setFlightAge(flightAge < 0 ? null : flightAge);
            selectSeatInfo.setSeatMemberCode(memberInfo.getSeatMemberCode());
            selectSeatInfo.setSelf(null != requestData.getFfpNo() && requestData.getFfpNo().equals(memberInfo.getMemberCardNo()));
            if (ChannelCodeEnum.G_B2C.getChannelCode().equals(requestData.getChannelNo())) {
                selectSeatInfo.setFfpCardNo(memberInfo.getMemberCardNo());
            }
        }
        return new AsyncResult<>(selectSeatInfo);
    }

    @Async("taskExecutor")
    @Override
    public Future<SeatMapInfo> querySeatMapV3(String traceId, BaseRequestDTO<SeatMapRequest> baseRequest) {
        String uuid = UUID.randomUUID().toString();
        log.info("调用CUSS查询航班座位图开始，traceId:{} 请求ID：{} 请求参数：{}", traceId, uuid, JSON.toJSONString(baseRequest));
        BaseResultDTO<SeatMapInfo> baseResult = cussBookingClient.querySeatMapV3(baseRequest, uuid);
        log.info("调用CUSS查询航班座位图结束，traceId:{} 请求ID：{} 返回结果：{}", traceId, uuid, JSON.toJSONString(baseResult));
        SeatMapInfo seatMapInfo = CussClientUtil.getResult(baseResult);
        return new AsyncResult<>(seatMapInfo);
    }

    @Async("taskExecutor")
    @Override
    public Future<SeatSelectInfo> addPeer(RequestDataDto requestData, String traceId, AddPeerParam addPeerParam) {
        try {
            BaseRequestDTO<AddPeerParam> addPeerParamBase = BaseRequestUtil.createRequest(requestData, addPeerParam);
            String uuid = UUID.randomUUID().toString();
            log.info("调用CUSS添加同行人开始，traceId:{} 请求ID：{} 请求参数：{}", traceId, uuid, JSON.toJSONString(addPeerParamBase));
            BaseResultDTO<SeatSelectInfoResult> baseResult = cussBookingClient.addPeer(addPeerParamBase, uuid);
            log.info("调用CUSS添加同行人结束，traceId:{} 请求ID：{} 返回结果：{}", traceId, uuid, JSON.toJSONString(baseResult));
            SeatSelectInfoResult seatSelectInfoResult = CussClientUtil.getResult(baseResult);
            SeatSelectInfo seatSelectInfo = new SeatSelectInfo();
            BeanUtils.copyProperties(seatSelectInfoResult, seatSelectInfo);
            seatSelectInfo.setSelf(requestData.getFfpNo().equals(seatSelectInfoResult.getFfpCardNo()));
            if (!ChannelCodeEnum.G_B2C.getChannelCode().equals(requestData.getChannelNo())) {
                seatSelectInfo.setFfpCardNo(null);
            }
            return new AsyncResult<>(seatSelectInfo);
        } catch (CommonException ce) {
            throw new CommonException(ce.getResultCode(), ce.getErrorMsg());
        } catch (Exception e) {
            throw new CommonException(ENUM_RESPONSE_STATUS.SYS_EX.code(), "【" + addPeerParam.getPeerName() + "】行程信息获取失败，请稍后再试");
        }
    }

}
