package com.juneyaoair.oneorder.cuss.bean.param;

import com.juneyaoair.cuss.request.FlightSeatStatusParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description 查询航班多旅客行程信息
 * @created 2024/08/07 9:52
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class FlightTravellerTripParam extends FlightSeatStatusParam {

    @Valid
    @NotEmpty(message = "旅客清单不能为空")
    @ApiModelProperty(value="旅客清单", required = true)
    private Set<TravellerInfo> travellerSet;

    @Data
    public static class TravellerInfo {

        @NotBlank(message = "旅客姓名号不能为空")
        @ApiModelProperty(value="旅客姓名", required = true)
        private String travellerName;

        @NotBlank(message = "票号不能为空")
        @ApiModelProperty(value="票号", required = true)
        private String ticketNo;

    }
}
