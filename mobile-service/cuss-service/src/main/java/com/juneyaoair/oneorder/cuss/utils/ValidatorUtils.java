package com.juneyaoair.oneorder.cuss.utils;

import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description: 校驗工具類
 * @Version: V1.0
 */
public class ValidatorUtils {

    private static final Validator VALIDATOR;
    static {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        VALIDATOR = factory.getValidator();
    }

    public static <T> Set<ConstraintViolation<T>> validate(T obj){
        return VALIDATOR.validate(obj);
    }

    public static <T> void check(T obj){
        if (null == obj) {
            throw new ServiceException(CommonErrorCode.REQUEST_VALIDATION_FAILED, CommonErrorCode.REQUEST_VALIDATION_FAILED.getMessage());
        }
        Set<ConstraintViolation<T>> resultSet = validate(obj);
        if (null == resultSet || resultSet.isEmpty()) {
            return;
        }
        String message = resultSet.iterator().next().getMessage();
        throw new ServiceException(CommonErrorCode.REQUEST_VALIDATION_FAILED, null == message ? CommonErrorCode.REQUEST_VALIDATION_FAILED.getMessage() : message);
    }

}
