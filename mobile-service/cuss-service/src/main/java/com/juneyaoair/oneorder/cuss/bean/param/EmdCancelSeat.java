package com.juneyaoair.oneorder.cuss.bean.param;

import com.juneyaoair.oneorder.cuss.bean.intf.TicketBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description EMD取消免费座位
 * @created 2023/11/16 13:48
 */
@Data
public class EmdCancelSeat implements TicketBase {

    @NotBlank(message = "票号不能为空")
    @ApiModelProperty(value = "票号", required = true)
    private String tktNo;

    @NotBlank(message = "航班日期不能为空")
    @ApiModelProperty(value = "航班日期 格式：yyyy-MM-dd", required = true)
    private String flightDate;

    @NotBlank(message = "航班号不能为空")
    @ApiModelProperty(value = "航班号", required = true)
    private String flightNo;

    @NotBlank(message = "出发机场不能为空")
    @ApiModelProperty(value = "出发机场", required = true)
    private String depAirportCode;

    @NotBlank(message = "到达机场不能为空")
    @ApiModelProperty(value = "到达机场", required = true)
    private String arrAirportCode;

    @NotBlank(message = "座位号不能为空")
    @ApiModelProperty(value = "座位号", required = true)
    private String seatNo;
    
    @ApiModelProperty(value = "短信验证码")
    @NotBlank(message = "短信验证码不能为空")
    private String verifyCode;

}
