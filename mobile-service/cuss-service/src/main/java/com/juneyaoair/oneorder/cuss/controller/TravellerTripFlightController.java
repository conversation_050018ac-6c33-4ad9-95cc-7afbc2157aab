package com.juneyaoair.oneorder.cuss.controller;



import com.juneyaoair.cuss.param.trip.MemberTravellerTripParam;
import com.juneyaoair.mobile.exception.ArgumentCheckFailException;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.oneorder.cuss.service.TravellerTripFlightService;
import com.juneyaoair.oneorder.flight.dto.*;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.util.List;


@Slf4j
@Api(value = "旅客行程航班接口")
@RestController
public class TravellerTripFlightController {

    @Resource
    private   TravellerTripFlightService travellerTripFlightService;


    @ApiOperation(value = "升舱券可用座位库存查询")
    @PostMapping("/travellerTripFlight/selectTravellerTripFlight")
    public ResponseData<List<FlightTravellerTrip>> selectTravellerTripFlight(@RequestBody @Validated RequestData<FlightParam> requestDataDto, BindingResult bindingResult) {
        FlightParam  flightParam=  requestDataDto.getData();
        if (flightParam == null) {
            throw MultiLangServiceException.fail("请求参数不完整");
        }
        //验证必要的请求参数
        if (bindingResult.hasErrors()) {
            throw new ArgumentCheckFailException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        List<FlightTravellerTrip>   flightTravellerTrips=travellerTripFlightService.selectTravellerTripFlight(flightParam);

        return ResponseData.success(flightTravellerTrips);
    }


    @ApiOperation(value = "升舱券我的行程查询")
    @PostMapping("/travellerTripFlight/getMemberTraveller")
    public ResponseData<List<MemberTravellerFlightResp>> getMemberTraveller(@RequestBody @Validated RequestData requestDataDto, BindingResult bindingResult) {
        //验证必要的请求参数
        if (bindingResult.hasErrors()) {
            throw new ArgumentCheckFailException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }

        MemberTravellerTripParam newMemberTravellerTripParam=new MemberTravellerTripParam();
        newMemberTravellerTripParam.setFfpCardNo(requestDataDto.getFfpNo());

        List<MemberTravellerFlightResp> flightTravellerTrips=travellerTripFlightService.getMemberTraveller(newMemberTravellerTripParam);

        return ResponseData.success(flightTravellerTrips);
    }
}
