package com.juneyaoair.oneorder.cuss.service;

import com.juneyaoair.common.dto.base.BaseRequestDTO;
import com.juneyaoair.cuss.dto.booking.response.seat.v2.ReserveSeatResult;
import com.juneyaoair.cuss.param.seat.AddPeerParam;
import com.juneyaoair.cuss.request.seat.SelectCheckInfoParam;
import com.juneyaoair.cuss.response.seat.SeatSelectInfoResult;
import com.juneyaoair.cuss.response.seat.SelectCheckInfoResult;
import com.juneyaoair.oneorder.api.order.dto.coupon.QueryIsBuyInsuranceReq;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.cuss.bean.intf.SeatOrderBase;
import com.juneyaoair.oneorder.cuss.bean.intf.TicketBase;
import com.juneyaoair.oneorder.cuss.bean.param.CancelSeatOrderParam;
import com.juneyaoair.oneorder.cuss.bean.param.EmdCancelSeat;
import com.juneyaoair.oneorder.cuss.bean.param.GetSeatContact;
import com.juneyaoair.oneorder.cuss.bean.param.QueryCheckInCountRequest;
import com.juneyaoair.oneorder.cuss.bean.param.RefundSeatOrderParam;
import com.juneyaoair.oneorder.cuss.bean.param.ReserveSeatParam;
import com.juneyaoair.oneorder.cuss.bean.param.SeatMapInfoParam;
import com.juneyaoair.oneorder.cuss.bean.result.CheckInCountCheckResult;
import com.juneyaoair.oneorder.cuss.bean.result.SeatContactInfo;
import com.juneyaoair.oneorder.cuss.bean.result.SeatInsureResult;
import com.juneyaoair.oneorder.cuss.bean.result.SeatMapInfoBff;
import com.juneyaoair.oneorder.cuss.bean.result.SeatSelectInfo;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Description 值机选座服务
 * @created 2024/11/28 17:24
 */
public interface SeatService {

    /**
     * 查询航班座位图
     * @param seatRequestRequestData
     * @return
     */
    SeatMapInfoBff querySeatMap(RequestDataDto<SeatMapInfoParam> seatRequestRequestData);

    /**
     * 值机选座操作
     * @param requestData
     * @return
     */
    ReserveSeatResult reserveSeat(RequestDataDto<ReserveSeatParam> requestData);

    /**
     * 获取选座联系方式信息
     * @param requestData
     * @return
     */
    SeatContactInfo getSeatContactInfo(RequestDataDto<GetSeatContact> requestData);

    /**
     * 发送取消值机选座验证码
     *
     * @param requestData
     * @param ticketBase
     * @param seatOrderBase
     * @param request
     * @return
     */
    SeatContactInfo sendVerifyCode(RequestDataDto requestData, TicketBase ticketBase, SeatOrderBase seatOrderBase, HttpServletRequest request);

    /**
     * 取消选座适用于免费座位
     * @param requestData
     */
    void cancelSeat(RequestDataDto<EmdCancelSeat> requestData);

    /**
     * 优选座位退单
     * @param requestData
     */
    void refundSeatOrder(RequestDataDto<RefundSeatOrderParam> requestData);

    /**
     * 取消优选座位订单
     * @param requestData
     */
    void cancelSeatOrder(RequestDataDto<CancelSeatOrderParam> requestData);

    /**
     * 查询旅客值机次数并进行校验
     * @param requestData
     * @return
     */
    CheckInCountCheckResult getCheckInCountCheckResult(RequestDataDto<QueryCheckInCountRequest> requestData);

    /**
     * 添加同行人
     * @param requestData
     * @return
     */
    SeatSelectInfo addPeer(RequestDataDto<AddPeerParam> requestData);

    /**
     * 查询选座值机信息
     * @param requestData
     * @return
     */
    SelectCheckInfoResult getSeatCheckInfo(BaseRequestDTO<SelectCheckInfoParam> requestData);

    /**
     * 选座保险
     * @param requestData
     * @return
     */
    SeatInsureResult getSeatInsure(RequestDataDto<QueryIsBuyInsuranceReq> requestData);
    
}
