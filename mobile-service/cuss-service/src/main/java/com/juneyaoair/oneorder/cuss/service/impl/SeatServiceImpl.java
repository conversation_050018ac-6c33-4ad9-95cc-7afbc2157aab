package com.juneyaoair.oneorder.cuss.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.juneyaoair.common.dto.base.BaseRequestDTO;
import com.juneyaoair.cuss.dto.FlightTicketSimpleDTO;
import com.juneyaoair.cuss.dto.booking.request.mail.MailUserInfo;
import com.juneyaoair.cuss.dto.booking.request.mail.SendMailRequest;
import com.juneyaoair.cuss.dto.booking.request.seat.v2.CancelSeatOrderRequest;
import com.juneyaoair.cuss.dto.booking.request.seat.v2.CancelSeatRequest;
import com.juneyaoair.cuss.dto.booking.request.seat.v2.RefundSeatOrderRequest;
import com.juneyaoair.cuss.dto.booking.request.seat.v2.ReserveSeatRequest;
import com.juneyaoair.cuss.dto.booking.response.seat.v2.ReserveSeatResult;
import com.juneyaoair.cuss.dto.booking.response.seat.v2.SeatMapInfo;
import com.juneyaoair.cuss.enums.ENUM_MESSAGE_TYPE;
import com.juneyaoair.cuss.enums.ENUM_RESPONSE_STATUS;
import com.juneyaoair.cuss.exception.CommonException;
import com.juneyaoair.cuss.param.seat.AddPeerParam;
import com.juneyaoair.cuss.param.seat.LastSelectSeatParam;
import com.juneyaoair.cuss.param.seat.PeerInfo;
import com.juneyaoair.cuss.request.checkin.CheckInCountQueryRequestDTO;
import com.juneyaoair.cuss.request.seat.SeatOrderParam;
import com.juneyaoair.cuss.request.seat.SelectCheckInfoParam;
import com.juneyaoair.cuss.request.ticket.TicketInfoParam;
import com.juneyaoair.cuss.response.checkin.CheckInCountQueryResponseDTO;
import com.juneyaoair.cuss.response.checkin.CheckInCountQueryResultDTO;
import com.juneyaoair.cuss.response.seat.SeatOrderResult;
import com.juneyaoair.cuss.response.seat.SeatOrderSingle;
import com.juneyaoair.cuss.response.seat.SeatSelectInfoResult;
import com.juneyaoair.cuss.response.seat.SeatTicketInfo;
import com.juneyaoair.cuss.response.seat.SelectCheckInfoResult;
import com.juneyaoair.cuss.service.CussBookingService;
import com.juneyaoair.cuss.service.CussCustomerService;
import com.juneyaoair.flightbasic.response.api.ApiAirPortInfoDto;
import com.juneyaoair.i18n.LocaleUtil;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.api.basic.service.IBasicService;
import com.juneyaoair.oneorder.api.common.CacheService;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.constant.TripTypeEnum;
import com.juneyaoair.oneorder.api.crm.service.ICaptchaService;
import com.juneyaoair.oneorder.api.crm.service.IMemberService;
import com.juneyaoair.oneorder.api.order.dto.coupon.BuyInsuranceInfo;
import com.juneyaoair.oneorder.api.order.dto.coupon.NewBasicOrderDetailResponse;
import com.juneyaoair.oneorder.api.order.dto.coupon.NewCouponOrderDetailReq;
import com.juneyaoair.oneorder.api.order.dto.coupon.QueryIsBuyInsuranceReq;
import com.juneyaoair.oneorder.api.order.service.IOrderService;
import com.juneyaoair.oneorder.common.common.SmsRemarkEnum;
import com.juneyaoair.oneorder.common.dto.CheckDayLicense;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.common.dto.enums.CheckLicenseFuncEnum;
import com.juneyaoair.oneorder.common.util.BeanUtils;
import com.juneyaoair.oneorder.common.util.TraceUtil;
import com.juneyaoair.oneorder.crm.dto.common.SourceType;
import com.juneyaoair.oneorder.cuss.bean.intf.SeatOrderBase;
import com.juneyaoair.oneorder.cuss.bean.intf.TicketBase;
import com.juneyaoair.oneorder.cuss.bean.param.CancelSeatOrderParam;
import com.juneyaoair.oneorder.cuss.bean.param.CheckInCountInfo;
import com.juneyaoair.oneorder.cuss.bean.param.EmdCancelSeat;
import com.juneyaoair.oneorder.cuss.bean.param.GetSeatContact;
import com.juneyaoair.oneorder.cuss.bean.param.QueryCheckInCountRequest;
import com.juneyaoair.oneorder.cuss.bean.param.RefundSeatOrderParam;
import com.juneyaoair.oneorder.cuss.bean.param.ReserveSeatParam;
import com.juneyaoair.oneorder.cuss.bean.param.SeatMapInfoParam;
import com.juneyaoair.oneorder.cuss.bean.result.CheckInCountCheckResult;
import com.juneyaoair.oneorder.cuss.bean.result.InsureApolloInfo;
import com.juneyaoair.oneorder.cuss.bean.result.InsureInfo;
import com.juneyaoair.oneorder.cuss.bean.result.InsurePopMessage;
import com.juneyaoair.oneorder.cuss.bean.result.SeatContactInfo;
import com.juneyaoair.oneorder.cuss.bean.result.SeatInsureResult;
import com.juneyaoair.oneorder.cuss.bean.result.SeatMapInfoBff;
import com.juneyaoair.oneorder.cuss.bean.result.SeatSelectInfo;
import com.juneyaoair.oneorder.cuss.config.CussApolloConfig;
import com.juneyaoair.oneorder.cuss.mapstruct.SeatMapper;
import com.juneyaoair.oneorder.cuss.service.SeatBaseService;
import com.juneyaoair.oneorder.cuss.service.SeatService;
import com.juneyaoair.oneorder.cuss.utils.BaseRequestUtil;
import com.juneyaoair.oneorder.mobile.config.RedisConstantConfig;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.util.MetricLogUtils;
import com.juneyaoair.mobile.exception.util.HoAirIpUtil;
import com.juneyaoair.oneorder.utils.SensitiveInfoHider;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @Description 值机选座服务
 * @created 2024/11/28 17:24
 */
@Slf4j
@Service
public class SeatServiceImpl implements SeatService {

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private LocaleUtil localeUtil;

    @Autowired
    private CussApolloConfig cussApolloConfig;

    @Autowired
    private CommonService commonService;
    @Autowired
    private IBasicService basicService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private SeatBaseService seatBaseService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private ICaptchaService captchaService;
    @Autowired
    private IOrderService orderService;

    @Autowired
    private CussBookingService cussBookingService;
    @Autowired
    private CussCustomerService cussCustomerService;

    @Override
    public SeatMapInfoBff querySeatMap(RequestDataDto<SeatMapInfoParam> seatRequestRequestData) {
        SeatMapInfoParam seatMapInfoParam = seatRequestRequestData.getData();
        String traceId = TraceUtil.getTraceId();
        Future<SeatSelectInfo> selectSeatInfoFuture = null;
        SeatMapInfoBff seatMapInfoBff = new SeatMapInfoBff();
        // 缓存ID不为空 直接获取缓存的客票信息
        if (StringUtils.isNotBlank(seatMapInfoParam.getCacheId())) {
            final String redisKey = RedisConstantConfig.SELECT_SEAT_INFO_CACHE + seatRequestRequestData.getFfpNo() + ":" + seatMapInfoParam.getCacheId();
            List<SeatSelectInfo> selectSeatInfoList = redisUtil.getObject(redisKey, new TypeReference<List<SeatSelectInfo>>() {});
            if (CollectionUtils.isEmpty(selectSeatInfoList)) {
                log.info("基于缓存ID查询客票缓存信息失败，缓存ID：{} 操作人：{}", seatMapInfoParam.getCacheId(), seatRequestRequestData.getFfpNo());
                throw new MultiLangServiceException(CommonErrorCode.CACHE_FAIL, "座位已更新，请重新查询！");
            }
            seatMapInfoBff.setSelectSeatInfoList(selectSeatInfoList);
        }
        // 缓存ID为空 且 票号不为空 获取当前票号选座信息
        else if (StringUtils.isNotEmpty(seatMapInfoParam.getTicketNo())) {
            if (StringUtils.isBlank(seatMapInfoParam.getPassengerName())) {
                log.info("姓名不能为空，票号：{} 操作人：{}", seatMapInfoParam.getTicketNo(), seatRequestRequestData.getFfpNo());
                throw new MultiLangServiceException(CommonErrorCode.REQUEST_VALIDATION_FAILED, "姓名不能为空！");
            }
            TicketInfoParam ticketInfoParam = SeatMapper.MAPPER.getTicketInfoParam(seatMapInfoParam);
            selectSeatInfoFuture = seatBaseService.getSelectSeatInfo(seatRequestRequestData, traceId, ticketInfoParam);
            MetricLogUtils.saveMetricLog("航班座位图V3-客票提取", seatRequestRequestData);
        }
        Future<SeatMapInfo> seatMapInfoFuture = seatBaseService.querySeatMapV3(traceId, BaseRequestUtil.createRequest(seatRequestRequestData, seatMapInfoParam));
        // 获取座位图执行结果
        try {
            SeatMapInfo seatMapInfo = seatMapInfoFuture.get();
            seatMapInfoBff.setSeatMapInfo(seatMapInfo);
        } catch (InterruptedException ie) {
            log.info("1.获取航班座位图信息异常，请求参数：{} 异常信息：", JSON.toJSONString(seatMapInfoParam), ie);
            Thread.currentThread().interrupt();
            throw new MultiLangServiceException(CommonErrorCode.SEAT_MAP_FAIL, "获取航班座位图异常！");
        } catch (Exception e) {
            log.info("2.获取航班座位图信息异常，请求参数：{} 异常信息：", JSON.toJSONString(seatMapInfoParam), e);
            if (e.getCause() instanceof CommonException) {
                throw (CommonException) e.getCause();
            }
            throw new MultiLangServiceException(CommonErrorCode.SEAT_MAP_FAIL, "获取航班座位图异常！");
        }
        // 获取客票信息执行结果
        if (null != selectSeatInfoFuture) {
            try {
                SeatSelectInfo selectSeatInfo = selectSeatInfoFuture.get();
                /** 2024-08-30 新版本升级后全部使用数组返回 保留一段时间兼容老版本 未来可删除 */
                seatMapInfoBff.setSelectSeatInfo(selectSeatInfo);
                seatMapInfoBff.setSelectSeatInfoList(Lists.newArrayList(selectSeatInfo));
            } catch (InterruptedException ie) {
                log.info("1.获取票号选座信息异常，请求参数：{} 异常信息：", JSON.toJSONString(seatMapInfoParam), ie);
                Thread.currentThread().interrupt();
                throw new MultiLangServiceException(CommonErrorCode.SEAT_TICKET_INFO_FAIL, "获取客票信息异常！");
            } catch (Exception e) {
                log.info("2.获取票号选座信息异常，请求参数：{} 异常信息：", JSON.toJSONString(seatMapInfoParam), e);
                if (e.getCause() instanceof CommonException) {
                    throw (CommonException) e.getCause();
                }
                throw new MultiLangServiceException(CommonErrorCode.SEAT_TICKET_INFO_FAIL, "获取客票信息异常！");
            }
        }
        return seatMapInfoBff;
    }

    @Override
    public ReserveSeatResult reserveSeat(RequestDataDto<ReserveSeatParam> requestData) {
        ReserveSeatParam reserveSeatParam = requestData.getData();
        if (reserveSeatParam.getUseTotalScore().compareTo(BigDecimal.ZERO) > 0) {
            if (StringUtils.isBlank(reserveSeatParam.getSalePwd())) {
                throw new MultiLangServiceException(CommonErrorCode.REQUEST_VALIDATION_FAILED, "消费密码不能为空");
            }
            // 积分策略验证
            basicService.checkScoreRule(requestData.getChannelNo(), requestData.getOriginIp(), requestData.getFfpId(), requestData.getFfpNo());
            // 验证消费密码
            memberService.verifyPayPassword(requestData.getChannelNo(), requestData.getFfpId(), reserveSeatParam.getSalePwd());
        }
        MetricLogUtils.saveMetricLog("值机选座操作-客票提取", requestData);
        ReserveSeatRequest reserveSeatRequest = new ReserveSeatRequest();
        BeanUtils.copyProperties(reserveSeatParam, reserveSeatRequest);
        reserveSeatRequest.setFfpId(Long.parseLong(requestData.getFfpId()));
        reserveSeatRequest.setFfpCardNo(requestData.getFfpNo());
        try {
            return cussBookingService.reserveSeat(BaseRequestUtil.createRequest(requestData, getUserNo(requestData.getChannelNo()), reserveSeatRequest));
        } catch (CommonException ce) {
            throw new MultiLangServiceException(CommonErrorCode.RESERVE_SEAT_FAIL, ce.getMessage());
        }
    }

    @Override
    public SeatContactInfo getSeatContactInfo(RequestDataDto<GetSeatContact> requestData) {
        GetSeatContact getSeatContact = requestData.getData();
        // 查询选座联系方式
        SeatContactInfo seatContactInfo = getSeatContactInfo(requestData, StringUtils.isBlank(getSeatContact.getCouponCode()) ? "freeSeat" : "paySeat", getSeatContact, getSeatContact);
        seatContactInfo.setPhone(SensitiveInfoHider.hidePhone(seatContactInfo.getPhone()));
        seatContactInfo.setEmail(SensitiveInfoHider.hideMail(seatContactInfo.getEmail()));
        return seatContactInfo;
    }

    @Override
    public SeatContactInfo sendVerifyCode(RequestDataDto requestData, TicketBase ticketBase, SeatOrderBase seatOrderBase, HttpServletRequest request) {
        // 查询选座联系方式
        SeatContactInfo seatContactInfo = getSeatContactInfo(requestData, StringUtils.isBlank(seatOrderBase.getCouponCode()) ? "freeSeat" : "paySeat", ticketBase, seatOrderBase);
        String ipAddr = HoAirIpUtil.getIpAddr(request);
        // IP校验
        CheckDayLicense ipCheckDayLicense = new CheckDayLicense(ipAddr, CheckLicenseFuncEnum.EMD_CANCEL_SEAT_IP, "验证码发送失败，达到单日发送上限");
        // 联系方式次数校验 存在手机号校验手机号 否则校验有效
        String contact = StringUtils.isBlank(seatContactInfo.getPhone()) ? seatContactInfo.getEmail() : seatContactInfo.getPhone();
        CheckDayLicense contactCheckDayLicense = new CheckDayLicense(contact, CheckLicenseFuncEnum.EMD_CANCEL_SEAT_CONTACT, "验证码发送失败，达到单日发送上限");
        commonService.checkDayLicense(false, ipCheckDayLicense, contactCheckDayLicense);
        // 生成并发送验证码
        String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisConstantConfig.SMS_SEAT + contact + SourceType.EMD_CANCEL_SEAT.value);
        String sendCode = commonService.getChkCode();
        redisUtil.set(redisKey, sendCode, 200);
        Map<String, String> extras = Maps.newHashMap();
        // 发送结果
        boolean flag;
        // 存在手机号 发送短信验证码 否则发送邮件验证码
        if (StringUtils.isNotBlank(seatContactInfo.getPhone())) {
            extras.put("sendCode", sendCode);
            flag = captchaService.commonSmsSend(SmsRemarkEnum.APP00010.name(), seatContactInfo.getAreaId(), seatContactInfo.getPhone(), extras);
        } else {
            extras.put("captcha", sendCode);
            SendMailRequest sendMailRequest = new SendMailRequest();
            sendMailRequest.setMessageType(ENUM_MESSAGE_TYPE.CANCEL_SEAT_CAPTCHA);
            sendMailRequest.setUserList(MailUserInfo.getMailUserInfoList(seatContactInfo.getEmail(), extras));
            flag = cussBookingService.sendMail(BaseRequestUtil.createRequest(requestData, sendMailRequest));
        }
        if (!flag) {
            throw new MultiLangServiceException(CommonErrorCode.CAPTCHA_SEND_FAIL, "验证码发送失败，请稍后再试");
        }
        // 增加计数
        commonService.addDayLicense(ipCheckDayLicense, contactCheckDayLicense);
        seatContactInfo.setPhone(SensitiveInfoHider.hidePhone(seatContactInfo.getPhone()));
        seatContactInfo.setEmail(SensitiveInfoHider.hideMail(seatContactInfo.getEmail()));
        return seatContactInfo;
    }

    @Override
    public void cancelSeat(RequestDataDto<EmdCancelSeat> requestData) {
        EmdCancelSeat emdCancelSeat = requestData.getData();
        // 查询选座联系方式
        SeatContactInfo seatContactInfo = getSeatContactInfo(requestData, "freeSeat", emdCancelSeat, null);
        // 联系方式 存在手机号校验手机号 否则校验有效
        String contact = StringUtils.isBlank(seatContactInfo.getPhone()) ? seatContactInfo.getEmail() : seatContactInfo.getPhone();
        // 校验验证码
        String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisConstantConfig.SMS_SEAT + contact + SourceType.EMD_CANCEL_SEAT.value);
        commonService.checkVerifyCode(redisKey, emdCancelSeat.getVerifyCode());

        // 取消选座、值机
        try {
            CancelSeatRequest cancelSeatRequest = SeatMapper.MAPPER.getCancelSeatRequest(emdCancelSeat);
            cancelSeatRequest.setFfpCardNo(requestData.getFfpNo());
            cussBookingService.cancelSeat(BaseRequestUtil.createRequest(requestData, getUserNo(requestData.getChannelNo()), cancelSeatRequest));
        } catch (CommonException ce) {
            // 31004 值机取消次数超过限制
            if (ENUM_RESPONSE_STATUS.CHECKIN_CANCEL_COUNT_LIMIT.code().equals(ce.getResultCode())) {
                throw new MultiLangServiceException(CommonErrorCode.CHECK_IN_CANCEL_COUNT_LIMIT, ce.getMessage());
            }
            log.error("系统取消选座、值机失败, 请求参数：{} 异常原因：{}", JSON.toJSONString(requestData), ce.getMessage());
            throw new MultiLangServiceException(CommonErrorCode.CANCEL_SEAT_FAIL, "取消失败");
        } catch (Exception ex) {
            log.error("系统取消选座、值机失败, 请求参数：{} 异常原因：", JSON.toJSONString(requestData), ex);
            throw new MultiLangServiceException(CommonErrorCode.CANCEL_SEAT_FAIL, "取消失败");
        }
    }

    @Override
    public void refundSeatOrder(RequestDataDto<RefundSeatOrderParam> requestData) {
        RefundSeatOrderParam refundSeatOrderParam = requestData.getData();
        // 查询订单信息
        NewCouponOrderDetailReq couponOrderDetailReq = new NewCouponOrderDetailReq();
        couponOrderDetailReq.setRequestIp(requestData.getOriginIp());
        ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
        couponOrderDetailReq.setChannelCode(channelInfo.getOrderChannelCode());
        couponOrderDetailReq.setUserNo(channelInfo.getUserNo());
        couponOrderDetailReq.setOrderNo(refundSeatOrderParam.getOrderNo());
        couponOrderDetailReq.setChannelOrderNo(refundSeatOrderParam.getChannelOrderNo());
        couponOrderDetailReq.setCouponCodeList(Lists.newArrayList(refundSeatOrderParam.getCouponCode()));
        NewBasicOrderDetailResponse newBasicOrderDetailResponse = orderService.queryCouponOrderDetail(couponOrderDetailReq);
        if (!"Y".equals(newBasicOrderDetailResponse.getIsSelf())) {
            throw new MultiLangServiceException(CommonErrorCode.OTHER_CHANNEL_SEAT_ORDER, "外部渠道订单，请去原渠道取消！");
        }
        // 查询选座联系方式
        SeatContactInfo seatContactInfo = getSeatContactInfo(requestData, "paySeat", null, refundSeatOrderParam);
        // 联系方式 存在手机号校验手机号 否则校验有效
        String contact = StringUtils.isBlank(seatContactInfo.getPhone()) ? seatContactInfo.getEmail() : seatContactInfo.getPhone();
        // 校验验证码
        String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisConstantConfig.SMS_SEAT + contact + SourceType.EMD_CANCEL_SEAT.value);
        commonService.checkVerifyCode(redisKey, refundSeatOrderParam.getVerifyCode());

        // 选座订单退款（适用于付费座位已付款取消选座）
        try {
            RefundSeatOrderRequest refundSeatOrderRequest = SeatMapper.MAPPER.getRefundSeatOrderRequest(refundSeatOrderParam);
            refundSeatOrderRequest.setFfpCardNo(requestData.getFfpNo());
            cussBookingService.refundSeatOrder(BaseRequestUtil.createRequest(requestData, getUserNo(requestData.getChannelNo()), refundSeatOrderRequest));
        } catch (CommonException ce) {
            log.error("选座订单退款失败, 请求参数：{} 异常原因：{}", JSON.toJSONString(requestData), ce.getMessage());
            throw new MultiLangServiceException(CommonErrorCode.REFUND_SEAT_ORDER_FAIL, "选座订单退单申请失败");
        } catch (Exception ex) {
            log.error("选座订单退款失败, 请求参数：{} 异常原因：", JSON.toJSONString(requestData), ex);
            throw new MultiLangServiceException(CommonErrorCode.REFUND_SEAT_ORDER_FAIL, "选座订单退单申请失败");
        }
    }

    @Override
    public void cancelSeatOrder(RequestDataDto<CancelSeatOrderParam> requestData) {
        CancelSeatOrderParam cancelSeatOrderParam = requestData.getData();
        // 查询订单信息
        NewCouponOrderDetailReq couponOrderDetailReq = new NewCouponOrderDetailReq();
        couponOrderDetailReq.setRequestIp(requestData.getOriginIp());
        ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
        couponOrderDetailReq.setChannelCode(channelInfo.getOrderChannelCode());
        couponOrderDetailReq.setUserNo(channelInfo.getUserNo());
        couponOrderDetailReq.setOrderNo(cancelSeatOrderParam.getOrderNo());
        couponOrderDetailReq.setChannelOrderNo(cancelSeatOrderParam.getChannelOrderNo());
        NewBasicOrderDetailResponse newBasicOrderDetailResponse = orderService.queryCouponOrderDetail(couponOrderDetailReq);
        if (!"Y".equals(newBasicOrderDetailResponse.getIsSelf())) {
            throw new MultiLangServiceException(CommonErrorCode.OTHER_CHANNEL_SEAT_ORDER, "外部渠道订单，请去原渠道取消！");
        }
        try {
            CancelSeatOrderRequest cancelSeatOrderRequest = new CancelSeatOrderRequest();
            cancelSeatOrderRequest.setOrderNo(cancelSeatOrderParam.getOrderNo());
            cancelSeatOrderRequest.setChannelOrderNo(cancelSeatOrderParam.getChannelOrderNo());
            cancelSeatOrderRequest.setCancelReason(cancelSeatOrderParam.getCancelReason());
            cancelSeatOrderRequest.setFfpCardNo(requestData.getFfpNo());
            cussBookingService.cancelSeatOrder(BaseRequestUtil.createRequest(requestData, getUserNo(requestData.getChannelNo()), cancelSeatOrderRequest));
        } catch (CommonException ce) {
            throw new MultiLangServiceException(CommonErrorCode.CANCEL_SEAT_ORDER_FAIL, CommonErrorCode.CANCEL_SEAT_ORDER_FAIL.getMessage());
        }
    }

    @Override
    public CheckInCountCheckResult getCheckInCountCheckResult(RequestDataDto<QueryCheckInCountRequest> requestData) {
        List<FlightTicketSimpleDTO> flightTicketSimples = Lists.newArrayList();
        QueryCheckInCountRequest queryCheckInCountRequest = requestData.getData();
        for (CheckInCountInfo checkInCountInfo : queryCheckInCountRequest.getCheckInCountInfos()) {
            FlightTicketSimpleDTO flightTicketSimple = new FlightTicketSimpleDTO();
            BeanUtils.copyNotNullProperties(checkInCountInfo, flightTicketSimple);
            BeanUtils.copyNotNullProperties(queryCheckInCountRequest, flightTicketSimple);
            flightTicketSimples.add(flightTicketSimple);
        }
        CheckInCountQueryRequestDTO checkInCountQueryRequest = new CheckInCountQueryRequestDTO();
        checkInCountQueryRequest.setTickets(flightTicketSimples);
        BaseRequestDTO<CheckInCountQueryRequestDTO> baseRequest = BaseRequestUtil.createRequest(requestData, checkInCountQueryRequest);
        CheckInCountQueryResponseDTO checkInCountQueryResponse = cussCustomerService.queryCheckInCount(baseRequest);
        List<String> checkedInList = Lists.newArrayList();
        List<String> checkedInMaxList = Lists.newArrayList();
        for (CheckInCountQueryResultDTO checkInCountQueryResult : checkInCountQueryResponse.getItems()) {
            if (checkInCountQueryResult.getCount() >= 3) {
                checkedInMaxList.add(checkInCountQueryResult.getTicket().getPsrName());
            } else if (checkInCountQueryResult.getCount() != 0) {
                checkedInList.add(checkInCountQueryResult.getTicket().getPsrName());
            }
        }
        CheckInCountCheckResult checkInCountCheckResult = new CheckInCountCheckResult();
        // 默认可值机
        checkInCountCheckResult.setCanCheck(true);
        if (CollectionUtils.isNotEmpty(checkedInList)) {
            String tips = localeUtil.getTips(CommonErrorCode.SEAT_SELECT_TIP.name());
            checkInCountCheckResult.setTip(checkedInList + tips);
        }
        if (CollectionUtils.isNotEmpty(checkedInMaxList)) {
            checkInCountCheckResult.setCanCheck(false);
            String tips = localeUtil.getTips(CommonErrorCode.SEAT_SELECT_MAX_TIP.name());
            checkInCountCheckResult.setTip(checkedInMaxList + tips);
        }
        return checkInCountCheckResult;
    }

    @Override
    public SeatSelectInfo addPeer(RequestDataDto<AddPeerParam> requestData) {
        List<PeerInfo> peerInfoList = requestData.getData().getPeerInfoList();
        if (CollectionUtils.isEmpty(peerInfoList)) {
            throw new MultiLangServiceException(CommonErrorCode.REQUEST_VALIDATION_FAILED, "同行人列表不能为空！");
        }
        if (peerInfoList.size() > 4) {
            throw new MultiLangServiceException(CommonErrorCode.REQUEST_VALIDATION_FAILED, "对不起，一次值机最多只能添加4名乘机人！");
        }
        MetricLogUtils.saveMetricLog("添加同行人操作-客票提取", requestData);
        try {
            SeatSelectInfoResult seatSelectInfoResult = cussBookingService.addPeer(BaseRequestUtil.createRequest(requestData));
            SeatSelectInfo seatSelectInfo = new SeatSelectInfo();
            BeanUtils.copyProperties(seatSelectInfoResult, seatSelectInfo);
            seatSelectInfo.setSelf(requestData.getFfpNo().equals(seatSelectInfoResult.getFfpCardNo()));
            if (!ChannelCodeEnum.G_B2C.getChannelCode().equals(requestData.getChannelNo())) {
                seatSelectInfo.setFfpCardNo(null);
            }
            return seatSelectInfo;
        } catch (CommonException ce) {
            throw new MultiLangServiceException(CommonErrorCode.SEAT_ADD_PEER_FAIL, ce.getMessage());
        }
    }

    @Override
    public SelectCheckInfoResult getSeatCheckInfo(BaseRequestDTO<SelectCheckInfoParam> requestData) {
        try {
            return cussBookingService.getSeatCheckInfo(requestData);
        } catch (CommonException ce) {
            throw new MultiLangServiceException(CommonErrorCode.GET_SEAT_CHECK_INFO_FAIL, ce.getMessage());
        }
    }

    @Override
    public SeatInsureResult getSeatInsure(RequestDataDto<QueryIsBuyInsuranceReq> requestData) {
        QueryIsBuyInsuranceReq queryIsBuyInsuranceReq = requestData.getData();
        try {
            ApiAirPortInfoDto depAirportInfo = cacheService.getLocalAirport(queryIsBuyInsuranceReq.getDepAirport());
            if (null == depAirportInfo) {
                log.error("出发机场信息获取失败,请求参数：{}", JSON.toJSONString(queryIsBuyInsuranceReq));
                return null;
            }
            // 出发机场非国内航线直接返回
            if (!"CN".equalsIgnoreCase(depAirportInfo.getCountryNo())) {
                return null;
            }
            ApiAirPortInfoDto arrAirportInfo = cacheService.getLocalAirport(queryIsBuyInsuranceReq.getArrAirport());
            if (null == arrAirportInfo) {
                log.error("到达机场信息获取失败,请求参数：{}", JSON.toJSONString(queryIsBuyInsuranceReq));
                return null;
            }
            // 出发机场 或 到达机场 非国内机场认为是国际航线
            boolean isInternal = !TripTypeEnum.TRIP_TYPE_D.getCode().equalsIgnoreCase(depAirportInfo.getIsInternational()) ||
                    !TripTypeEnum.TRIP_TYPE_D.getCode().equalsIgnoreCase(arrAirportInfo.getIsInternational());
            TripTypeEnum tripTypeEnum = isInternal ? TripTypeEnum.TRIP_TYPE_I : TripTypeEnum.TRIP_TYPE_D;
            InsurePopMessage insurePopMessage = cussApolloConfig.getInsurePopMessageMap().get(tripTypeEnum.getCode());
            if (null == insurePopMessage) {
                log.error("未配置弹窗信息,请求参数：{} 航线类型：{}", JSON.toJSONString(queryIsBuyInsuranceReq), tripTypeEnum);
                return null;
            }
            SeatInsureResult seatInsureResult = new SeatInsureResult();
            seatInsureResult.setInsurePopMessage(insurePopMessage);
            List<InsureApolloInfo> insureInfoApolloList = cussApolloConfig.getInsureMap().get(tripTypeEnum.getCode());
            if (CollectionUtils.isEmpty(insureInfoApolloList)) {
                log.error("未配置可购买保险信息,请求参数：{} 航线类型：{}", JSON.toJSONString(queryIsBuyInsuranceReq), tripTypeEnum);
                return null;
            }
            Date now = new Date();
            List<InsureInfo> insureInfoList = Lists.newArrayList();
            for (InsureApolloInfo insureApolloInfo : insureInfoApolloList) {
                // 配置了销售开始时间 且 销售开始时间在当前时间之后 处理下一个数据
                if (null != insureApolloInfo.getStartTime() && insureApolloInfo.getStartTime().after(now)) {
                    continue;
                }
                // 配置了销售结束时间 且 销售结束时间在当前时间之前 处理下一个数据
                if (null != insureApolloInfo.getEndTime() && insureApolloInfo.getEndTime().before(now)) {
                    continue;
                }
                insureInfoList.add(insureApolloInfo);
            }
            if (CollectionUtils.isEmpty(insureInfoList)) {
                log.error("配置可购买保险信息均已失效,请求参数：{} 航线类型：{}", JSON.toJSONString(queryIsBuyInsuranceReq), tripTypeEnum);
                return null;
            }
            seatInsureResult.setInsureList(insureInfoList);
            // 查询是否已购买过保险
            queryIsBuyInsuranceReq.setRequestIp(requestData.getOriginIp());
            ChannelInfo channelInfo = commonService.findChannelInfo(requestData.getChannelNo());
            queryIsBuyInsuranceReq.setChannelCode(channelInfo.getOrderChannelCode());
            queryIsBuyInsuranceReq.setUserNo(channelInfo.getUserNo());
            List<BuyInsuranceInfo> buyInsuranceInfoList = orderService.QueryIsBuyInsurance(queryIsBuyInsuranceReq);
            seatInsureResult.setBuyInsuranceInfoList(buyInsuranceInfoList);
            return seatInsureResult;
        } catch (Exception e) {
            log.error("查询是否存在可购买保险异常,请求参数：{} 异常信息：", JSON.toJSONString(queryIsBuyInsuranceReq), e);
            return null;
        }
    }

    /**
     * 查询选座联系方式
     * @param requestData
     * @param seatType
     * @param ticketBase
     * @param seatOrderBase
     * @return
     */
    private SeatContactInfo getSeatContactInfo(RequestDataDto requestData, String seatType, TicketBase ticketBase, SeatOrderBase seatOrderBase) {
        String areaId;
        String phone;
        String email;
        // 空、免费座位 默认免费座位
        if ("freeSeat".equals(seatType)) {
            SeatTicketInfo seatTicketInfo = getLastSeatTicketInfo(requestData, ticketBase);
            areaId = seatTicketInfo.getAreaId();
            phone = seatTicketInfo.getPhone();
            email = seatTicketInfo.getMail();
        } // 优选座位
        else if ("paySeat".equals(seatType)) {
            if (null == seatOrderBase) {
                throw new MultiLangServiceException(CommonErrorCode.REQUEST_VALIDATION_FAILED, "订单信息不能为空");
            }
            if (StringUtils.isBlank(seatOrderBase.getOrderNo())) {
                throw new MultiLangServiceException(CommonErrorCode.REQUEST_VALIDATION_FAILED, "订单号不能为空");
            }
            if (StringUtils.isBlank(seatOrderBase.getChannelOrderNo())) {
                throw new MultiLangServiceException(CommonErrorCode.REQUEST_VALIDATION_FAILED, "渠道订单号不能为空");
            }
            if (StringUtils.isBlank(seatOrderBase.getCouponCode())) {
                throw new MultiLangServiceException(CommonErrorCode.REQUEST_VALIDATION_FAILED, "子订单号不能为空");
            }
            SeatOrderSingle seatOrderSingle = getSeatOrder(requestData, seatOrderBase);
            areaId = seatOrderSingle.getAreaId();
            phone = seatOrderSingle.getMobile();
            email = seatOrderSingle.getMail();
        } else {
            throw new MultiLangServiceException(CommonErrorCode.REQUEST_VALIDATION_FAILED, "暂不支持的座位类型！");
        }
        SeatContactInfo seatContactInfo = new SeatContactInfo();
        seatContactInfo.setAreaId(areaId);
        seatContactInfo.setPhone(phone);
        seatContactInfo.setEmail(email);
        return seatContactInfo;
    }

    /**
     * 查询最后一次选座信息
     * @param requestData
     * @param ticketBase
     * @return
     */
    private SeatTicketInfo getLastSeatTicketInfo(RequestDataDto requestData, TicketBase ticketBase) {
        LastSelectSeatParam lastSelectSeatParam = new LastSelectSeatParam();
        lastSelectSeatParam.setFlightDate(ticketBase.getFlightDate());
        lastSelectSeatParam.setFlightNo(ticketBase.getFlightNo());
        lastSelectSeatParam.setDepAirportCode(ticketBase.getDepAirportCode());
        lastSelectSeatParam.setArrAirportCode(ticketBase.getArrAirportCode());
        lastSelectSeatParam.setTicketNo(ticketBase.getTktNo());
        lastSelectSeatParam.setEffective("Y");
        SeatTicketInfo seatTicketInfo = cussCustomerService.getLastSeatTicketInfo(BaseRequestUtil.createRequest(requestData, lastSelectSeatParam));
        if (null == seatTicketInfo) {
            throw new MultiLangServiceException("选座信息获取失败，请去原渠道取消！");
        }
        return seatTicketInfo;
    }


    /**
     * 获取优先座位订单信息
     * @param requestData
     * @param seatOrderBase
     * @return
     */
    private SeatOrderSingle getSeatOrder(RequestDataDto requestData, SeatOrderBase seatOrderBase) {
        SeatOrderParam seatOrderParam = new SeatOrderParam();
        seatOrderParam.setChannelOrderNo(seatOrderBase.getChannelOrderNo());
        seatOrderParam.setCouponCode(seatOrderBase.getCouponCode());
        SeatOrderResult seatOrderResult = cussCustomerService.getSeatOrder(BaseRequestUtil.createRequest(requestData, seatOrderParam));
        SeatOrderSingle seatOrderSingle = null == seatOrderResult || CollectionUtils.isEmpty(seatOrderResult.getSeatOrderList()) ? null : seatOrderResult.getSeatOrderList().get(0);
        if (null == seatOrderSingle) {
            throw new MultiLangServiceException("优选座位订单信息获取失败");
        }
        return seatOrderSingle;
    }

    /**
     * 根据渠道号获取渠道用户
     * @param channelNo
     * @return
     */
    private String getUserNo(String channelNo) {
        ChannelInfo channelInfo = commonService.findChannelInfo(channelNo);
        return channelInfo.getUserNo();
    }

}
