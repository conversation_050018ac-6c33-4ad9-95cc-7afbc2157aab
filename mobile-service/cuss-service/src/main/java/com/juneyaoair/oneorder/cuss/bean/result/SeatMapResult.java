package com.juneyaoair.oneorder.cuss.bean.result;

import com.juneyaoair.cuss.dto.booking.response.seat.v2.SeatChartResponseV2;
import com.juneyaoair.cuss.dto.booking.response.seat.v2.SeatDiscount;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: caolei
 * @Description: 值机选座座位图
 * @Date: 2021/6/29 10:32
 * @Modified by:
 */
@Data
public class SeatMapResult {

    @ApiModelProperty(value = "座位图")
    private SeatChartResponseV2 seatCharts;

    @ApiModelProperty(value = "选座折扣率信息")
    private SeatDiscount seatDiscount;

    @ApiModelProperty(value = "预留选座数据")
    private SeatSelectInfo selectSeatInfo;

}