package com.juneyaoair.oneorder.cuss.bean.param;

import com.juneyaoair.cuss.dto.booking.common.CussRegexCommon;
import com.juneyaoair.cuss.dto.booking.request.seat.v2.ReserveSeatDetail;
import com.juneyaoair.cuss.dto.booking.request.seat.v2.SeatInsurance;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @created 2023/11/16 11:05
 */
@Data
public class ReserveSeatParam {

    @ApiModelProperty(value = "市场方航班号")
    private String marketingFlightNo;

    @NotBlank(message = "航班号不能为空")
    @ApiModelProperty(value = "航班号", required = true)
    private String flightNo;

    @NotBlank(message = "航班日期不能为空")
    @ApiModelProperty(value = "航班日期", required = true)
    private String flightDate;

    @NotBlank(message = "出发机场不能为空")
    @ApiModelProperty(value = "出发机场", required = true)
    private String depAirportCode;

    @NotBlank(message = "到达机场不能为空")
    @ApiModelProperty(value = "到达机场", required = true)
    private String arrAirportCode;

    @NotBlank(message = "币种不能为空")
    @ApiModelProperty(value = "币种 默认传值CNY", required = true)
    private String currency;

    @NotBlank(message = "渠道订单号不能为空")
    @ApiModelProperty(value = "渠道订单号", required = true)
    private String channelOrderNo;

    @Pattern(regexp = "^\\d{6}$", message = "消费密码必须为六位数字")
    @ApiModelProperty(value = "消费密码(使用积分必须)")
    private String salePwd;

    @Min(value = 0, message = "订单总金额不能低于0")
    @NotNull(message = "订单总金额不能为空")
    @ApiModelProperty(value = "订单总金额(积分+现金) 单位:元", required = true)
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "订单现金支付金额 单位:元", hidden = true)
    private BigDecimal cashTotalAmount;

    @Min(value = 0, message = "订单总金额不能低于0")
    @NotNull(message = "订单使用总积分不能为空")
    @ApiModelProperty(value = "订单使用总积分 0或正整数", required = true)
    private BigDecimal useTotalScore;

    @ApiModelProperty(value = "积分抵扣金额(币种非CNY必须)")
    private BigDecimal pointsPrice;

    @ApiModelProperty(value = "短信区域ID（应用于国际短信，国内短信可为）")
    private String areaId;

    @ApiModelProperty(value = "联系人手机号")
    private String phoneNo;

    @ApiModelProperty(value = "邮箱")
    @Pattern(regexp = CussRegexCommon.EMAIL, message = "请输入正确的邮箱")
    private String mail;

    @NotBlank(message = "来源不能为空")
    @ApiModelProperty(value = "来源", required = true)
    private String source;

    @ApiModelProperty(value = "来源SID")
    private String sid;

    @Valid
    @ApiModelProperty(value = "旅客信息", required = true)
    @NotEmpty(message = "旅客信息不能为空")
    @Size(max = 4, message = "最多提交4个旅客")
    private List<ReserveSeatDetail> reserveSeats;

    @Valid
    @Size(max = 4,message = "购买保险清单不能超过4个")
    @ApiModelProperty(value = "购买保险清单")
    private List<SeatInsurance> insuranceList;

}
