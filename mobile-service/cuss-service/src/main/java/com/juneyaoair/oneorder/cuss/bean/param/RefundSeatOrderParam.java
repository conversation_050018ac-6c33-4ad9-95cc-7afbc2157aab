package com.juneyaoair.oneorder.cuss.bean.param;

import com.juneyaoair.cuss.dto.booking.request.order.RefundOrderParam;
import com.juneyaoair.oneorder.cuss.bean.intf.SeatOrderBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description 付费选座退单请求参数
 * @created 2024/09/13 9:52
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class RefundSeatOrderParam extends RefundOrderParam implements SeatOrderBase {

    @ApiModelProperty(value = "短信验证码")
    @NotBlank(message = "短信验证码不能为空")
    private String verifyCode;

}
