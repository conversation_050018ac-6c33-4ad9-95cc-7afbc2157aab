package com.juneyaoair.oneorder.cuss.bean.param;

import com.juneyaoair.cuss.dto.booking.request.seat.v2.SeatMapRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Author: caolei
 * @Description: 值机选座座位图V3
 * @Date: 2024/08/05 10:32
 * @Modified by:
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SeatMapInfoParam extends SeatMapRequest {

    @ApiModelProperty(value = "缓存ID")
    private String cacheId;

}
