package com.juneyaoair.oneorder.cuss.bean.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description
 * @created 2023/11/16 11:05
 */
@Data
public class SelectSeatInfoParam {

    @NotBlank(message = "航班号不能为空")
    @ApiModelProperty(value="航班号", required = true)
    private String flightNo;

    @NotBlank(message = "航班日期不能为空")
    @ApiModelProperty(value="航班日期", required = true)
    private String flightDate;

    @NotBlank(message = "出发城市三字码不能为空")
    @ApiModelProperty(value="出发城市三字码", required = true)
    private String depAirportCode;

    @NotBlank(message = "到达城市三字码不能为空")
    @ApiModelProperty(value="到达城市三字码", required = true)
    private String arrAirportCode;

    @Valid
    @NotEmpty(message = "旅客信息不能为空")
    @ApiModelProperty(value="旅客信息清单", required = true)
    private Set<Traveller> travellerList;

    @Data
    public static class Traveller {

        @NotBlank(message = "票号不能为空")
        @ApiModelProperty(value="票号", required = true)
        private String ticketNo;

        @NotBlank(message = "旅客姓名不能为空")
        @ApiModelProperty(value="旅客姓名", required = true)
        private String travellerName;

    }
}
