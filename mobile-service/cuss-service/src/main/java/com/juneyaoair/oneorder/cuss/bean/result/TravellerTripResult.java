package com.juneyaoair.oneorder.cuss.bean.result;

import com.juneyaoair.cuss.response.trip.TravellerTripInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 旅客行程信息
 * @created 2024/08/05 14:11
 */
@Data
public class TravellerTripResult {

    @ApiModelProperty(value = "旅客行程信息")
    private List<TravellerTripInfo> travellerTripInfoList;

    @ApiModelProperty(value = "不支持值机选座旅客行程信息")
    private List<TravellerTripInfo> unTravellerTripInfoList;

}
