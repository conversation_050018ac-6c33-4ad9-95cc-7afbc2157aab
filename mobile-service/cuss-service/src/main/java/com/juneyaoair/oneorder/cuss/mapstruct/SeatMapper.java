package com.juneyaoair.oneorder.cuss.mapstruct;

import com.juneyaoair.cuss.dto.booking.request.seat.v2.CancelSeatRequest;
import com.juneyaoair.cuss.dto.booking.request.seat.v2.RefundSeatOrderRequest;
import com.juneyaoair.cuss.dto.booking.request.seat.v2.SeatRequest;
import com.juneyaoair.cuss.request.ticket.TicketInfoParam;
import com.juneyaoair.oneorder.cuss.bean.param.EmdCancelSeat;
import com.juneyaoair.oneorder.cuss.bean.param.RefundSeatOrderParam;
import com.juneyaoair.oneorder.cuss.bean.param.SeatMapInfoParam;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Description
 * @created 2023/11/16 14:50
 */
@Mapper
public interface SeatMapper {

    SeatMapper MAPPER = Mappers.getMapper(SeatMapper.class);

    /**
     * EmdCancelSeat to CancelSeatRequest
     * @param emdCancelSeat
     * @return
     */
    CancelSeatRequest getCancelSeatRequest(EmdCancelSeat emdCancelSeat);

    /**
     * SeatRequest to TicketInfoParam
     * @param seatRequest
     * @return
     */
    TicketInfoParam getTicketInfoParam(SeatRequest seatRequest);

    /**
     * SeatMapInfoParam to TicketInfoParam
     * @param seatMapInfoParam
     * @return
     */
    TicketInfoParam getTicketInfoParam(SeatMapInfoParam seatMapInfoParam);

    /**
     * RefundSeatOrderParam to RefundSeatOrderRequest
     * @param refundSeatOrderParam
     * @return
     */
    RefundSeatOrderRequest getRefundSeatOrderRequest(RefundSeatOrderParam refundSeatOrderParam);

}
