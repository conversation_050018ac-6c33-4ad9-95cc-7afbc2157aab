package com.juneyaoair.oneorder.cuss.bean.result;

import com.juneyaoair.cuss.response.trip.TravellerFlightInfo;
import com.juneyaoair.cuss.response.trip.TravellerTicketInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BffTravellerTripInfo extends TravellerFlightInfo {

    @ApiModelProperty(value = "航线是否可值机")
    private boolean segmentCanCheckIn;

    @ApiModelProperty(value = "登机时间")
    private String boardTime;

    @ApiModelProperty("旅客清单")
    private List<TravellerTicketInfo> travellerList;

}
