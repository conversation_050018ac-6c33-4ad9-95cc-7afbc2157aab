package com.juneyaoair.oneorder.cuss.bean.param;

import com.juneyaoair.oneorder.cuss.bean.intf.SeatOrderBase;
import com.juneyaoair.oneorder.cuss.bean.intf.TicketBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description 客票基础信息
 * @created 2023/11/16 13:48
 */
@Data
public class GetSeatContact implements TicketBase, SeatOrderBase {

    @NotBlank(message = "票号不能为空")
    @ApiModelProperty(value = "票号", required = true)
    private String tktNo;

    @NotBlank(message = "航班日期不能为空")
    @ApiModelProperty(value = "航班日期 格式：yyyy-MM-dd", required = true)
    private String flightDate;

    @NotBlank(message = "航班号不能为空")
    @ApiModelProperty(value = "航班号", required = true)
    private String flightNo;

    @NotBlank(message = "出发机场不能为空")
    @ApiModelProperty(value = "出发机场", required = true)
    private String depAirportCode;

    @NotBlank(message = "到达机场不能为空")
    @ApiModelProperty(value = "到达机场", required = true)
    private String arrAirportCode;

    @ApiModelProperty(value = "订单号（优先座位必须）")
    private String orderNo;

    @ApiModelProperty(value = "渠道订单号（优先座位必须）")
    private String channelOrderNo;

    @ApiModelProperty(value = "权益券码（优先座位必须）")
    private String couponCode;

}
