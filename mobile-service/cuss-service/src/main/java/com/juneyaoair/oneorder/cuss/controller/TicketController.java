package com.juneyaoair.oneorder.cuss.controller;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.juneyaoair.cuss.request.ticket.TicketVerifyParam;
import com.juneyaoair.cuss.service.CussCustomerService;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.SceneEnum;
import com.juneyaoair.oneorder.api.geetest.service.IGeetestService;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.CheckDayLicense;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.common.dto.enums.CheckLicenseFuncEnum;
import com.juneyaoair.oneorder.common.service.TongDunService;
import com.juneyaoair.oneorder.controller.BaseController;
import com.juneyaoair.oneorder.cuss.aggr.TicketServiceAggr;
import com.juneyaoair.oneorder.cuss.bean.param.GeeTestTicketVerifyParam;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.ticket.TicketVerifyResultDto;
import com.juneyaoair.oneorder.util.MetricLogUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 客票相关服务
 * @created 2024/07/26 17:24
 */
@Slf4j
@Api(value = "客票相关服务")
@RestController
public class TicketController extends BaseController {

    @Autowired
    private IGeetestService geetestService;
    @Autowired
    private CussCustomerService cussCustomerService;
    @Autowired
    private TongDunService tongDunService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private TicketServiceAggr ticketServiceAggr;

    @ApolloJsonValue("${whiteList:[]}")
    private List<String> whiteList;

    @ApiOperation(value = "客票验真V2")
    @PostMapping("/ticket/verifyTicket/v2")
    public ResponseData<TicketVerifyResultDto> verifyTicketV2(@RequestBody @Validated RequestDataDto<GeeTestTicketVerifyParam> requestData, HttpServletRequest request) {
        GeeTestTicketVerifyParam geeTestTicketVerifyParam = requestData.getData();
        geetestService.validate(SceneEnum.VERIFY_TICKET, requestData);
        MetricLogUtils.saveMetricLog("客票验真V2-客票提取", requestData);
        BizDto bizDto = initBizDto(request);
        //白名单账户
        if (CollectionUtils.isNotEmpty(whiteList) && whiteList.contains(requestData.getFfpNo())) {
            CheckDayLicense ffpCheckDayLicense = new CheckDayLicense(requestData.getFfpNo(), CheckLicenseFuncEnum.VERIFY_TICKET_FFP_WHITE, "已超出每日查询上限，请明日再查");
            commonService.checkDayLicense(true, ffpCheckDayLicense);
        } else {
            CheckDayLicense ipCheckDayLicense = new CheckDayLicense(bizDto.getIp(), CheckLicenseFuncEnum.VERIFY_TICKET_IP, "已超出每日查询上限，请明日再查");
            CheckDayLicense ffpCheckDayLicense = new CheckDayLicense(requestData.getFfpNo(), CheckLicenseFuncEnum.VERIFY_TICKET_FFP, "已超出每日查询上限，请明日再查");
            commonService.checkDayLicense(true, ipCheckDayLicense, ffpCheckDayLicense);
        }
        TicketVerifyParam ticketVerifyParam = new TicketVerifyParam();
        BeanUtils.copyProperties(geeTestTicketVerifyParam, ticketVerifyParam);
        TicketVerifyResultDto ticketVerifyResult = ticketServiceAggr.verifyTicketV2(requestData.getFfpId(),requestData.getFfpNo(),geeTestTicketVerifyParam,bizDto,requestData.getLanguage());
        return ResponseData.success(ticketVerifyResult);
    }

}
