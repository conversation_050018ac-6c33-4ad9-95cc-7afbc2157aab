package com.juneyaoair.oneorder.cuss.service;

import com.juneyaoair.cuss.param.trip.TravellerTripLimit;
import com.juneyaoair.cuss.param.trip.TravellerTripParam;
import com.juneyaoair.cuss.response.trip.TravellerTripInfo;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.cuss.bean.param.QueryTravellerTrip;
import com.juneyaoair.oneorder.cuss.bean.result.TravellerTripResult;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 值机选座行程服务
 * @created 2024/11/28 17:24
 */
public interface TravellerTripService {

    /**
     * 查询行程信息
     *
     * @param requestData
     * @param queryTravellerTrip
     * @param travellerTripParam
     * @return
     */
    TravellerTripResult getTravellerTripResult(RequestDataDto requestData, QueryTravellerTrip queryTravellerTrip, TravellerTripParam travellerTripParam);

    /**
     * 基于会员卡号查询行程信息
     * @param requestData
     * @return
     */
    List<TravellerTripInfo> getTravellerTrip(RequestDataDto requestData, TravellerTripLimit travellerTripLimit);

}
