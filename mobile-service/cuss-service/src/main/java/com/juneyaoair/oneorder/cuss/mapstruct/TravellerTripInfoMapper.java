package com.juneyaoair.oneorder.cuss.mapstruct;

import com.google.common.collect.Lists;
import com.juneyaoair.cuss.response.trip.TravellerFlightInfo;
import com.juneyaoair.cuss.response.trip.TravellerTicketInfo;
import com.juneyaoair.cuss.response.trip.TravellerTripInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TravellerTripInfoMapper
 * @created 2024/08/06 14:50
 */
@Mapper
public interface TravellerTripInfoMapper {

    TravellerTripInfoMapper MAPPER = Mappers.getMapper(TravellerTripInfoMapper.class);

    TravellerTripInfo toTravellerTripInfo(TravellerFlightInfo travellerFlightInfo);

    /**
     * 行程排序
     * @param travellerTripInfoList
     * @return
     */
    static List<TravellerTripInfo> sortList(Collection<TravellerTripInfo> travellerTripInfoList) {
        if (CollectionUtils.isEmpty(travellerTripInfoList)) {
            return Lists.newArrayList();
        }
        return travellerTripInfoList.stream().sorted(
                Comparator.comparing(TravellerTripInfo::getFlightDate, Comparator.nullsLast(String::compareTo))
                        .thenComparing(TravellerTripInfo::getDepTime, Comparator.nullsLast(String::compareTo))
        ).collect(Collectors.toList());
    }

    /**
     * 生成MAP数据
     * @param dateMap
     * @param travellerFlightInfo
     * @param travellerTicketInfo
     */
    static void setMapValue(Map<String, TravellerTripInfo> dateMap, TravellerFlightInfo travellerFlightInfo, TravellerTicketInfo travellerTicketInfo) {
        if (null == travellerTicketInfo) {
            return;
        }
        final String mapKey = travellerFlightInfo.getOperationFlightNo() + "_" + travellerFlightInfo.getFlightNo() + "_" + travellerFlightInfo.getFlightDate() + "_" + travellerFlightInfo.getDepAirportCode() + "_" + travellerFlightInfo.getArrAirportCode();
        TravellerTripInfo travellerInfo = dateMap.get(mapKey);
        if (null == travellerInfo) {
            travellerInfo = TravellerTripInfoMapper.MAPPER.toTravellerTripInfo(travellerFlightInfo);
        }
        List<TravellerTicketInfo> travellerMapList = null == travellerInfo.getTravellerList() ? Lists.newArrayList() : travellerInfo.getTravellerList();
        travellerMapList.add(travellerTicketInfo);
        travellerInfo.setTravellerList(travellerMapList);
        dateMap.put(mapKey, travellerInfo);
    }
}
