package com.juneyaoair.oneorder.cuss.config;

import com.google.common.collect.ImmutableMap;
import com.juneyaoair.cuss.enums.ENUM_RESPONSE_STATUS;
import com.juneyaoair.cuss.exception.CommonException;
import com.juneyaoair.flightbasic.utils.MdcUtils;
import com.juneyaoair.i18n.LocaleUtil;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.mobile.exception.errorcode.ErrorCode;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Locale;

/**
 *
 * <AUTHOR>
 * @date 2018/3/22
 * 全局自定义异常类处理  返回JSON
 */
@Slf4j
@ControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
public class CussGlobalExceptionResolver {

    @Autowired
    private LocaleUtil localeUtil;

    @ResponseBody
    @ExceptionHandler(CommonException.class)
    public ResponseData<Object> commonException(CommonException ce) {
        log.error("【1.CommonException服务请求异常信息】,请求ID：{},错误信息：", MdcUtils.getRequestId(), ce);
        if (ENUM_RESPONSE_STATUS.NO_REAL_NAME.code().equalsIgnoreCase(ce.getResultCode())) {
            return createResponseData(CommonErrorCode.NO_REAL_NAME, ce.getMessage());
        }
        return createResponseData(CommonErrorCode.SYSTEM_ERROR, ce.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(com.juneyaoair.flightbasic.exception.CommonException.class)
    public ResponseData<Object> commonException(com.juneyaoair.flightbasic.exception.CommonException ce) {
        log.error("【2.CommonException服务请求异常信息】,请求ID：{},错误信息：", MdcUtils.getRequestId(), ce);
        return createResponseData(CommonErrorCode.SYSTEM_ERROR, ce.getMessage());
    }

    /**
     * 创建返回对象
     * @param errorMsg
     * @param errorCode
     * @return
     */
    private ResponseData<Object> createResponseData(ErrorCode errorCode, String errorMsg){
        // 语言为简体中文 message使用原始值 否则使用翻译值
        String message = StringUtils.isNotBlank(errorMsg) && Locale.SIMPLIFIED_CHINESE.equals(LocaleContextHolder.getLocale()) ? errorMsg : localeUtil.getTips(errorCode.getCode());
        return ResponseData.success(errorCode.getCode(), errorCode.getStatus(), message, ImmutableMap.of("detail", errorMsg));
    }

}
