package com.juneyaoair.oneorder.cuss.bean.result;

import com.juneyaoair.oneorder.api.order.dto.coupon.BuyInsuranceInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 选座保险信息
 * @created 2025/01/09 10:42
 */
@Data
public class SeatInsureResult {

    @ApiModelProperty(value = "弹窗")
    private InsurePopMessage insurePopMessage;

    @ApiModelProperty(value = "保险信息")
    private List<InsureInfo> insureList;

    @ApiModelProperty(value = "是否购买保险")
    private List<BuyInsuranceInfo> buyInsuranceInfoList;

}
