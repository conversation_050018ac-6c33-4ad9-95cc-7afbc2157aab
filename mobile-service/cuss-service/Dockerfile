# 构建镜像，执行命令：【docker build -t call-coupon:2.0 .】
FROM harbor.hoair.cn/library/openjdk:8-jre
MAINTAINER Arber C.

# 时区问题
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone

ADD target/cuss-service.jar /app/app.jar

ENV JAVA_OPTS="-Xms2048M -Xmx2048M -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=256m"
ENTRYPOINT ["sh","-c","exec java -server ${JAVA_OPTS} -Dfile.encoding=UTF-8 -XX:+HeapDumpOnOutOfMemoryError -jar /app/app.jar"]


