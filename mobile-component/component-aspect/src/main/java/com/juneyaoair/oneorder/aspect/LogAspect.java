package com.juneyaoair.oneorder.aspect;

import brave.propagation.TraceContext;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.ImmutableMap;
import com.juneyaoair.bff.util.HoLogUtil;
import com.juneyaoair.mobile.exception.AppException;
import com.juneyaoair.oneorder.log.dto.BffLogDTO;
import com.juneyaoair.mobile.exception.dto.ResponseData;
import com.juneyaoair.mobile.exception.errorcode.ErrorCode;
import com.juneyaoair.mobile.exception.util.HoAirIpUtil;
import com.juneyaoair.oneorder.common.security.utils.ServletUtils;
import com.juneyaoair.oneorder.constant.SecurityConstants;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import com.juneyaoair.oneorder.tools.utils.HoAirUuidUtil;
import com.juneyaoair.oneorder.tools.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @description 接口层日志切面
 * @date 2023/7/11 10:08
 */
@Aspect
@Order(value = 1000)
@Component
@Slf4j
public class LogAspect {

    private static final String TIME_KEY = "@timestamp";
    private static final String METHOD_POST = "POST";

    /**
     * 不进行日志打印的请求
     */
    @ApolloJsonValue("${juneyaoair.noLogPath.patterns:[\"/actuator/info\",\"/actuator/health\",\"/actuator\"]}")
    private List<String> excludePathPatterns;

    @Value("${spring.application.name}")
    private String serverName;

    /**
     * execution (* com.sample.service.impl..*.*(..))
     * 整个表达式可以分为五个部分：
     * 1、execution(): 表达式主体。
     * 2、第一个*号：表示返回类型，*号表示所有的类型。
     * 3、包名：表示需要拦截的包名，后面的两个句点表示当前包和当前包的所有子包，com.sample.service.impl包、子孙包下所有类的方法。
     * 4、第二个*号：表示类名，*号表示所有的类。
     * 5、*(..):最后这个星号表示方法名，*号表示所有的方法，后面括弧里面表示方法的参数，两个句点表示任何参数。
     */
    @Pointcut("execution(public * com.juneyaoair.horder.controller..*.*(..))")
    public void controllerAspectAop() {
    }

    @Pointcut("execution(public * com.juneyaoair.oneorder.cuss.controller..*.*(..))")
    public void cussAspectAop() {
    }

    @Pointcut("execution(public * com.juneyaoair.oneorder.accountbff.controller..*.*(..))")
    public void controllerAccountAspectAop() {
    }

    @Pointcut("execution(public * com.juneyaoair.oneorder.*.controller..*.*(..))")
    public void controllerAop() {
    }

    /**
     * 基于注解打印日志
     */
    @Pointcut("@annotation(com.juneyaoair.oneorder.annotation.ApiLog)")
    public void annotationAop() {
    }

    @Pointcut("controllerAspectAop()||cussAspectAop()||controllerAccountAspectAop()||annotationAop()||controllerAop()")
    private void orOperation() {
    }

    @Around(value = "orOperation()")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        BffLogDTO commonLog = null;
        boolean hasLog = false;
        try {
            String traceId = null;
            long startTime = System.currentTimeMillis();
            Class<?> returnType = ((MethodSignature) proceedingJoinPoint.getSignature()).getReturnType();
            //返回值为boolean则会报错，这些接口不做切面
            if (StringUtils.equals("java.lang.Boolean", returnType.getName())) {
                return proceedingJoinPoint.proceed();
            }
            //打印日志操作，出现错误不影响后面流程，优化异常后发邮件
            ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            // 获取request信息
            HttpServletRequest request = sra.getRequest();
            // 日志链路
            TraceContext traceContext = (TraceContext) request.getAttribute(TraceContext.class.getName());
            if (null != traceContext) {
                traceId = StringUtils.isNotBlank(traceContext.traceIdString()) ? traceContext.traceIdString() : null;
            }
            String uuid = StringUtils.isNotBlank(traceContext.traceIdString()) ? traceContext.traceIdString() : HoAirUuidUtil.randomUUID8();
            //请求方法
            String method = request.getMethod();
            //渠道信息
            String channelCode = ServletUtils.getChannelCode(request);
            // 版本号
            String clientVersion = ServletUtils.getHeader(request, SecurityConstants.HEAD_CLIENT_VERSION);
            //请求参数
            String params = "no params";
            //请求方法地址
            String requestURL = request.getRequestURI();
            //请求ip
            String clientIp = "no clientIp";
            //请求持续时间
            long interval = 0L;
            //服务器地址
            String hostAddress = "no host";
            try {
                clientIp = HoAirIpUtil.getIpAddr(request);
                hostAddress = HoAirIpUtil.getLocalIp();
                if (METHOD_POST.equals(method)) {
                    Object[] paramsArray = proceedingJoinPoint.getArgs();
                    //获取请求参数
                    params = argsArrayToString(paramsArray);
                    Object param = paramsArray[0];
                    //获取渠道
                    if (param instanceof RequestData) {
                        channelCode = ((RequestData) param).getChannelNo();
                        if (StringUtils.isBlank(channelCode)) {
                            // 支付回调时传递的渠道参数
                            channelCode = ((RequestData) param).getChannelNo();
                        }
                    }
                }
                //是否记日志
                hasLog = isPrintLog(requestURL);
                if (hasLog) {
                    BffLogDTO reqBffLogDTO = getLogDTO("Request", uuid, traceId, channelCode, clientVersion, method,
                            interval, requestURL, clientIp, hostAddress, serverName, params);
                    HoLogUtil.accdetailLogger.info(JSON.toJSONString(reqBffLogDTO));
                }
            } catch (Exception e) {
                log.error("API接口请求异常error,捕获异常信息:", e);
            }
            commonLog = getLogDTO("Request", uuid, traceId, channelCode, clientVersion, method,
                    interval, requestURL, clientIp, hostAddress, serverName, params);
            Object result = proceedingJoinPoint.proceed();
            try {
                String resultStr;
                String errorCode;
                String errorMess;
                interval = System.currentTimeMillis() - startTime;
                if (!(result instanceof String)) {
                    resultStr = HoAirGsonUtil.objectToJson(result);
                    JSONObject resJson = JSON.parseObject(resultStr);
                    errorCode = resJson.getString("code");
                    errorMess = resJson.getString("message");
                } else {
                    resultStr = (String) result;
                    errorCode = (String) result;
                    errorMess = (String) result;
                }
                // 返回信息
                if (hasLog) {
                    BffLogDTO resBffLogDTO = getLogDTO("Response", uuid, traceId, channelCode, clientVersion, method,
                            interval, requestURL, clientIp, hostAddress, serverName, resultStr);
                    resBffLogDTO.setErrorCode(errorCode);
                    resBffLogDTO.setErrorMess(errorMess);
                    HoLogUtil.accdetailLogger.info(JSON.toJSONString(resBffLogDTO));
                    saveMetricLog(clientIp, channelCode, requestURL, errorCode, errorMess);
                }
            } catch (Exception e) {
                log.debug("API接口返回异常error,{},用时：{}秒", uuid, DateUtil.secondDiff(startTime));
                log.error("API接口请求异常error,捕获异常信息:", e);
            }
            return result;
        } catch (AppException ae) {
            if (hasLog) {
                ResponseData representation = createResponseData(ae.getError(), ae.getMessage());
                if (commonLog != null) {
                    commonLog.setMessage(JSON.toJSONString(representation));
                }
                BffLogDTO resBffLogDTO = createResLogDTO(commonLog, ae.getError());
                HoLogUtil.accdetailLogger.info(JSON.toJSONString(resBffLogDTO));
                saveMetricLog(resBffLogDTO.getClientIp(), resBffLogDTO.getChannelCode(), resBffLogDTO.getPath(), resBffLogDTO.getErrorCode(), resBffLogDTO.getErrorMess());
            }
            throw ae;
        } catch (Throwable e) {
            log.error("WebLogAspect切面异常,异常信息：", e);
            throw e;
        }
    }

    private BffLogDTO getLogDTO(String type, String group, String traceId, String channelCode, String clientVersion,
                                String method, long interval, String path, String clientIp, String host, String serverName, String message) {
        BffLogDTO bffLogDTO = new BffLogDTO();
        bffLogDTO.setType(type);
        bffLogDTO.setGroup(group);
        bffLogDTO.setTraceId(traceId);
        bffLogDTO.setChannelCode(channelCode);
        bffLogDTO.setClientVersion(clientVersion);
        bffLogDTO.setMethod(method);
        bffLogDTO.setInterval(interval);
        bffLogDTO.setPath(path);
        bffLogDTO.setClientIp(clientIp);
        bffLogDTO.setHost(host);
        bffLogDTO.setServiceName(serverName);
        bffLogDTO.setTimestamp(DateUtil.getCurrentDateStr(DateUtil.FORMAT_DATE_TIME_ELK));
        bffLogDTO.setMessage(message);
        return bffLogDTO;
    }

    /**
     * 保存接口日志
     */
    private void saveMetricLog(String ip, String chnnelNo, String path, String code, String errorInfo) {
        try {
            JSONObject json = new JSONObject();
            JSONObject para = new JSONObject();
            para.put("渠道", chnnelNo);
            para.put("请求源IP", ip);
            para.put("请求路径", path);
            para.put("返回码", code);
            para.put("返回中文信息", errorInfo);
            json.put("Name", "接口返回状态");
            json.put("Tags", para);
            json.put("Value", 1.0);
            json.put(TIME_KEY, DateUtil.getCurrentDateStr(DateUtil.FORMAT_DATE_TIME_ELK));
            HoLogUtil.metricLogger.info(json.toJSONString());
        } catch (Exception ex) {
            log.error("记录度量日志错误:", ex);
        }
    }

    /**
     * 获取请求参数
     *
     * @param paramsArray 请求参数
     */
    private String argsArrayToString(Object[] paramsArray) {
        String params = "";
        try {
            if (paramsArray != null && paramsArray.length > 0) {
                for (int i = 0; i < paramsArray.length; i++) {
                    Object obj = paramsArray[i];
                    if (!(obj instanceof HttpServletRequest)) {
                        params = HoAirGsonUtil.objectToJson(obj);
                        break;
                    }
                }
            }
            return params.trim();
        } catch (Exception e) {
            return params;
        }
    }


    /**
     * 是否打印日志 默认是开启日志
     *
     * @return
     */
    private boolean isPrintLog(String path) {
        AntPathMatcher antPathMatcher = new AntPathMatcher();
        if (CollectionUtils.isEmpty(excludePathPatterns)) {
            return true;
        }
        // 表示不进行日志输出
        if (excludePathPatterns.stream().anyMatch(url -> antPathMatcher.match(url, path))) {
            return false;
        }
        return true;
    }

    private BffLogDTO createResLogDTO(BffLogDTO commonDTO, ErrorCode error) {
        BffLogDTO bffLogDTO = new BffLogDTO();
        bffLogDTO.setInterval(0);
        BeanUtils.copyProperties(commonDTO, bffLogDTO);
        bffLogDTO.setType("Response");
        bffLogDTO.setErrorCode(error.getCode());
        bffLogDTO.setErrorMess(error.getMessage());
        return bffLogDTO;
    }

    /**
     * 创建返回对象
     * @param errorMsg
     * @param errorCode
     * @return
     */
    private ResponseData<Object> createResponseData(ErrorCode errorCode, String errorMsg){
        // 语言为简体中文 message使用原始值 否则使用翻译值
        ResponseData responseData = new ResponseData(errorCode.getCode(), errorCode.getStatus(), errorMsg, null, ImmutableMap.of("detail", errorMsg));
        return responseData;
    }

}
