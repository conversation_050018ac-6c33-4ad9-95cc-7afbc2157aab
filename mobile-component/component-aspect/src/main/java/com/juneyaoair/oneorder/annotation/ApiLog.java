package com.juneyaoair.oneorder.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.METHOD;

/**
 * <AUTHOR>
 * @description api接口日志注解
 * @date 2023/7/11 11:08
 */
@Target({METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ApiLog {
    LogLevel level() default LogLevel.INFO;
    enum LogLevel {
        TRACE,
        DEBUG,
        INFO,
        WARN,
        ERROR;
        LogLevel() {
        }
    }
}
