package com.juneyaoair.oneorder.utils;

import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/11 18:57
 */
@Slf4j
class SensitiveInfoHiderTest {

    @Test
    void hidePhone() {
        String r = SensitiveInfoHider.hidePhone("123");
        log.error("手机号脱敏结果:{}",r);
        Assert.assertNotNull(r);
    }

    @Test
    void hidePhone2() {
        String r = SensitiveInfoHider.hidePhone("123456789");
        log.error("手机号脱敏结果:{}",r);
        Assert.assertNotNull(r);
    }
}