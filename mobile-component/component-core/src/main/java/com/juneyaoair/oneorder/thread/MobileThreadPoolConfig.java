package com.juneyaoair.oneorder.thread;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/9 18:33
 */
@Configuration
public class MobileThreadPoolConfig {
    /**
     *   默认情况下，在创建了线程池后，线程池中的线程数为0，当有任务来之后，就会创建一个线程去执行任务，
     *    当线程池中的线程数目达到corePoolSize后，就会把到达的任务放到缓存队列当中；
     *  当队列满了，就继续创建线程，当线程数量大于等于maxPoolSize后，开始使用拒绝策略拒绝
     */
    /**
     * 核心线程数（默认线程数）
     */
    @Value("${threadPool.corePoolSize:5}")
    private int corePoolSize;
    //private static final int corePoolSize = Runtime.getRuntime().availableProcessors() + 1;
    /**
     * 最大线程数
     */
    @Value("${threadPool.maxPoolSize:15}")
    private int maxPoolSize;
    /**
     * 允许线程空闲时间（单位：默认为秒）
     */
    @Value("${threadPool.keepAliveTime:30}")
    private int keepAliveTime;
    /**
     * 缓冲队列大小
     */
    @Value("${threadPool.queueCapacity:200}")
    private int queueCapacity;

    @Bean("mobileThreadPoolExecutor")
    public MobileThreadPoolExecutor customThreadPoolExecutor() {
        return new MobileThreadPoolExecutor(corePoolSize,maxPoolSize,queueCapacity,keepAliveTime);
    }
}
