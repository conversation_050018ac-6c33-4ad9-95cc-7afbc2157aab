package com.juneyaoair.oneorder.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description 敏感信息隐藏方法
 * @date 2024/9/3 0:30
 */
public class SensitiveInfoHider {

    /**
     * 中文姓名组
     */
    private static final Pattern CN_PATTERN = Pattern.compile("(\\w*[\\u4E00-\\u9FA5\\uF900-\\uFA2D]{1,24}\\w*)");

    /**
     * 手机号脱敏
     * 前3后2展示，其他展示为*，若少于等于5位则全部展示
     *
     * @param phone
     * @return
     */
    public static String hidePhone(String phone) {
        if (null == phone) {
            return null;
        }
        return hideMiddleFixedSensitiveInfo(phone, 3, 2);
    }

    /**
     * 电子邮件脱敏
     * 前面字符显示前3位，@后面完整显示；若少于等于三位，直接展示
     *
     * @param mail
     * @return
     */
    public static String hideMail(String mail) {
        if (null == mail) {
            return null;
        }
        if (!mail.contains("@")) {
            return mail;
        }
        String[] arr = mail.split("@");
        if (arr.length != 2) {
            return mail;
        }
        String address = arr[0];
        int length = address.length();
        // 少于等于3位 则全部展示
        if (length <= 3) {
            return mail;
        }
        return hideMiddleSensitiveInfo(address, 3, length - 3) + "@" + arr[1];
    }

    /**
     * 姓名脱敏
     * ①中文姓名：
     * 三个字以下（含）、隐藏第一个字；
     * 六个字以下（含）、显示最后两个字；
     * 六个字以上，显示前一后二；
     * ②英文姓名：
     * 姓：全部隐藏，名全部展示；
     *
     * @param name
     * @return
     */
    public static String hideName(String name) {
        if (null == name || name.isEmpty()) {
            return name;
        }
        // 匹配中文姓名
        Matcher matcher = CN_PATTERN.matcher(name);
        if (matcher.find()) {
            if (name.length() < 4) {
                return hideMiddleSensitiveInfo(name, 0, 1);
            }
            if (name.length() < 7) {
                return hideMiddleSensitiveInfo(name, 0, name.length() - 2);
            }
            return hideMiddleSensitiveInfo(name, 1, name.length() - 3);
        } else {
            String[] arr = name.split("/");
            String first = arr[0];
            return hideMiddleSensitiveInfo(first, 0, first.length()) + (arr.length == 1 ? "" : "/" + arr[1]);
        }
    }



    /**
     * 证件号脱敏
     * 隐藏中间部分内容
     *
     * @param certNo
     * @return
     */
    public static String hideAllCertNo(String certNo) {
        return "******";
    }

    /**
     * 证件号脱敏
     * 隐藏中间部分内容
     *
     * @param certNo
     * @return
     */
    public static String hidePartCertNo(String certNo) {
        return hideMiddleFixedSensitiveInfo(certNo,2,2);
    }

    /**
     * 指定保留数据位数
     *
     * @param sensitiveInfo 信息
     * @param start         保留的前几位
     * @param end           保留的后几位
     * @return 隐藏后的证件号码字符串
     */
    private static String hideMiddleFixedSensitiveInfo(String sensitiveInfo, int start, int end) {
        if (StringUtils.isBlank(sensitiveInfo) || sensitiveInfo.length() <= 2 || start < 0 || end <= 0) {
            // 如果输入无效，返回原始字符串或者抛出异常
            return sensitiveInfo;
        }
        //*号的数量
        int starLength = 4;
        //如果基础位数不够情况下，只保留首位
        if (start + end >= sensitiveInfo.length()) {
            start = 1;
            end = 1;
        }
        StringBuilder hiddenInfo = new StringBuilder();
        // 保留信息的前部分
        hiddenInfo.append(sensitiveInfo, 0, start);
        // 用星号替换指定位置的字符
        for (int i = 0; i < starLength; i++) {
            hiddenInfo.append('*');
        }
        // 追加信息的后部分
        hiddenInfo.append(sensitiveInfo.substring(sensitiveInfo.length() - end));
        return hiddenInfo.toString();
    }

    /**
     * 隐藏中间部分字符
     *
     * @param sensitiveInfo 信息
     * @param start         隐藏开始的位置（从0开始计数）
     * @param length        需要隐藏的字符数
     * @return 隐藏后的证件号码字符串
     */
    private static String hideMiddleSensitiveInfo(String sensitiveInfo, int start, int length) {
        if (sensitiveInfo == null || sensitiveInfo.length() == 0 || start < 0 || length <= 0 || start + length > sensitiveInfo.length()) {
            // 如果输入无效，返回原始字符串或者抛出异常
            return sensitiveInfo;
        }
        StringBuilder hiddenInfo = new StringBuilder();
        // 保留证件号的前部分
        hiddenInfo.append(sensitiveInfo, 0, start);
        // 用星号替换指定位置的字符
        for (int i = 0; i < length; i++) {
            hiddenInfo.append('*');
        }
        // 追加证件号的后部分
        hiddenInfo.append(sensitiveInfo.substring(start + length));
        return hiddenInfo.toString();
    }

    /**
     * 隐藏身份证号的中间部分（根据实际情况调整隐藏位数）
     *
     * @param idCardNumber
     * @return
     */
    private static String hideIdCardMiddle(String idCardNumber) {
        // 通常身份证号为18位，这里假设我们要隐藏中间的12位（实际情况请按需调整）
        int start = 4; // 从第3位开始隐藏
        int length = 12; // 隐藏12位
        return hideMiddleSensitiveInfo(idCardNumber, start, length);
    }

    /**
     * 隐藏中间部分（根据实际情况调整隐藏位数）
     *
     * @param idCardNumber
     * @return
     */
    private static String hideOtherCardMiddle(String idCardNumber) {
        // 通常身份证号为18位，这里假设我们要隐藏中间的12位（实际情况请按需调整）
        int start = 2; // 从第3位开始隐藏
        int length = idCardNumber.length() - 4; // 隐藏除开头两位,结尾2位的其他位数
        return hideMiddleSensitiveInfo(idCardNumber, start, length);
    }

    /**
     * 隐藏敏感信息根据证件号码的长度不同，应用不同的隐藏规则
     * @param certNo 证件号码字符串
     * @return 隐藏后的证件号码字符串
     */
    public static String hideMiddleSensitiveInfo(String certNo) {
        //超出18位的保留前6位隐藏中间10位
        if (certNo.length() >= 18) {
            return hideIdCardMiddle(certNo);
        } else if (certNo.length() == 16) {
            return hideMiddleSensitiveInfo(certNo, 4, 10);
        } else {
            return hideOtherCardMiddle(certNo);
        }
    }
}
