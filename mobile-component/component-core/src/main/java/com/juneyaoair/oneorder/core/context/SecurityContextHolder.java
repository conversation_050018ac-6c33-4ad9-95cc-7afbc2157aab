package com.juneyaoair.oneorder.core.context;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.constant.SecurityConstants;
import com.juneyaoair.oneorder.core.text.Convert;
import com.juneyaoair.oneorder.utils.HoStringUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @description 获取当前线程变量中的channelNo，Token等信息
 * 注意： 必须在网关通过请求头的方法传入，同时在HeaderInterceptor拦截器设置值。 否则这里无法获取
 * @date 2023/7/21 16:29
 */
public class SecurityContextHolder {
    private static final TransmittableThreadLocal<Map<String, Object>> THREAD_LOCAL = new TransmittableThreadLocal<>();
    public static void set(String key, Object value)
    {
        Map<String, Object> map = getLocalMap();
        map.put(key, value == null ? StringUtils.EMPTY : value);
    }

    public static String get(String key)
    {
        Map<String, Object> map = getLocalMap();
        return Convert.toStr(map.getOrDefault(key, StringUtils.EMPTY));
    }

    public static <T> T get(String key, Class<T> clazz)
    {
        Map<String, Object> map = getLocalMap();
        return HoStringUtils.cast(map.getOrDefault(key, null));
    }

    public static Map<String, Object> getLocalMap()
    {
        Map<String, Object> map = THREAD_LOCAL.get();
        if (map == null)
        {
            map = new ConcurrentHashMap<String, Object>();
            THREAD_LOCAL.set(map);
        }
        return map;
    }

    public static void setLocalMap(Map<String, Object> threadLocalMap)
    {
        THREAD_LOCAL.set(threadLocalMap);
    }

    /**
     * 设置渠道
     * @param channelNo
     */
    public static void setChannelCode(String channelNo)
    {
        set(SecurityConstants.HEAD_CHANNEL_CODE, channelNo);
    }

    /**
     * 获取渠道
     * @return
     */
    public static String getChannelCode()
    {
        return get(SecurityConstants.HEAD_CHANNEL_CODE);
    }

    public static void setClientVersion(String clientVersion){
        set(SecurityConstants.HEAD_CLIENT_VERSION, clientVersion);
    }

    public static String getClientVersion(){
        return get(SecurityConstants.HEAD_CLIENT_VERSION);
    }

    public static void setVersion(String version){
        set(SecurityConstants.HEAD_VERSION, version);
    }
    public static String getVersion(){
        return get(SecurityConstants.HEAD_VERSION);
    }

    public static void setVersionCode(String versionCode){
        set(SecurityConstants.HEAD_VERSION_CODE, versionCode);
    }

    public static String getVersionCode(){
        return get(SecurityConstants.HEAD_VERSION_CODE);
    }

    public static void setOriginIp(String originIp){
        set(SecurityConstants.ORIGIN_IP, originIp);
    }

    public static String getOriginIp(){
        return get(SecurityConstants.ORIGIN_IP);
    }

    public static void setLanguage(LanguageEnum language) {
        set(SecurityConstants.LANGUAGE, language.name());
    }

    public static LanguageEnum getLanguage() {
        return LanguageEnum.valueOf(get(SecurityConstants.LANGUAGE));
    }

    public static void remove()
    {
        THREAD_LOCAL.remove();
    }
}
