package com.juneyaoair.oneorder.thread;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/9 18:29
 */
public class MobileThreadPoolExecutor extends ThreadPoolTaskExecutor {
    public MobileThreadPoolExecutor(int corePoolSize,int maxPoolSize,int queueCapacity,int keepAliveSeconds) {
        // 配置线程池参数，例如核心线程数、最大线程数、队列容量等
        setCorePoolSize(corePoolSize);
        setMaxPoolSize(maxPoolSize);
        setQueueCapacity(queueCapacity);
        setKeepAliveSeconds(keepAliveSeconds);
        setThreadNamePrefix("MobileThreadPoolExecutor-");
        // CallerRunsPolicy：由调用线程（提交任务的线程）处理该任务
        setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    }
}
