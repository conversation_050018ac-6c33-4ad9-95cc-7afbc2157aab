package com.juneyaoair.oneorder.mobile.utils;

import com.juneyaoair.oneorder.mobile.config.RedisConstantConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;


/**
 * 2021年10月14日14:51:23
 * redis工具类 操作redis 1号库
 * <AUTHOR>
 */
@Slf4j
@Component
public class RedisUtil extends AbstractRedisUtil {

    @Autowired
    @Qualifier(value = "redisTemplateBff1")
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RedisConstantConfig config;

    @Override
    protected RedisTemplate getTemplate() {
        return redisTemplate;
    }

    public RedisConstantConfig getConfig() {
        return config;
    }

}