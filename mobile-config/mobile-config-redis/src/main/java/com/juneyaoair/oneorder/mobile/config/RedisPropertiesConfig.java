package com.juneyaoair.oneorder.mobile.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Data
@Component
@Configuration
public class RedisPropertiesConfig {
    @Value("${spring.redis.sentinel.nodes:}")
    private String redisNodes;
    @Value("${spring.redis.sentinel.master:mymaster}")
    private String master;
    @Value("${spring.redis.sentinel.database0:0}")
    private int database0;
    @Value("${spring.redis.sentinel.database1:1}")
    private int database1;
    @Value("${spring.redis.password:}")
    private String password;

    //////////////////////// redis pool配置
    @Value("${spring.redis.pool.max_idle:10}")
    private Integer maxIdle;
    @Value("${spring.redis.pool.min_idle:0}")
    private Integer minIdle;//定义池中维护的最小空闲连接数。 此设置只有在正值时才有效果。
    @Value("${spring.redis.pool.max_active:10}")
    private Integer maxActive;//可以分配的最大连接数。 使用负值为无限制。
    @Value("${spring.redis.pool.max_wait:10}")
    private Integer maxWait;//池中“空闲”连接的最大数量。 使用负值来表示无限数量的空闲连接。
    @Value("${spring.redis.pool.numTestsPerEvictionRun:10}")
    private Integer numTestsPerEvictionRun;//一次最多evict的pool里的jedis实例个数
    @Value("${spring.redis.pool.timeBetweenEvictionRunsMillis:6000}")
    private Integer timeBetweenEvictionRunsMillis;//test idle 线程的时间间隔
    @Value("${spring.redis.pool.minEvictableIdleTimeMills:60000}")
    private Integer minEvictableIdleTimeMills;//连接池中连接可空闲的时间,毫秒
    @Value("${spring.redis.pool.test-on-create:false}")
    private Boolean testOnCreate;//创建连接时检查
    @Value("${spring.redis.pool.test-on-borrow:true}")
    private Boolean testOnBorrow;//在获取连接的时候检查有效性
    @Value("${spring.redis.pool.test-on-return:false}")
    private Boolean testOnReturn;//当调用return Object方法时，是否进行有效性检查
    @Value("${spring.redis.pool.test-while-idle:true}")
    private Boolean testWhileIdle;//在空闲时检查有效性
    @Value("${spring.redis.pool.commandTimeout:60}")
    private Integer commandTimeout;
    @Value("${spring.redis.pool.shutdownTimeout:100}")
    private Integer shutdownTimeout;

}
