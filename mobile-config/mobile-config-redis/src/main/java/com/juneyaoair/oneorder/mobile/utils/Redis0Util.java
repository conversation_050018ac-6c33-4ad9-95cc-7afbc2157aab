package com.juneyaoair.oneorder.mobile.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 操作redis 0号库
 * @date 2023/7/19 17:03
 */
@Slf4j
@Component
public class Redis0Util extends AbstractRedisUtil {
    @Autowired
    @Qualifier(value = "redisTemplateBff0")
    private RedisTemplate<String, Object> redisTemplate;
    @Override
    protected RedisTemplate getTemplate() {
        return redisTemplate;
    }
}
