package com.juneyaoair.oneorder.mobile;

import com.juneyaoair.oneorder.mobile.config.RedisConstantConfig;

/**
 * <AUTHOR>
 * @description redis key处理
 * @date 2024/10/25 17:28
 */
public class RedisKeyFormatUtil {
    /**
     * 创建短信验证码缓存key
     * @param account
     * @param func    验证码功能
     * @return
     */
    public static String createSmsCaptchaKey(String account, String func) {
        return RedisConstantConfig.SMS_CAPTCHA_DIR + func + ":" + account;
    }

    /**
     * 创建邮箱验证码缓存key
     * @param account
     * @param func
     * @return
     */
    public static String createEmailCaptchaKey(String account, String func) {
        return RedisConstantConfig.EMAIL_CAPTCHA_DIR + func + ":" + account;
    }
}
