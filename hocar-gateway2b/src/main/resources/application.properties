spring.profiles.active=@profiles.active@
spring.application.name=hocar-gateway2b
server.port=9098

jasypt.encryptor.password=ho-cc-pwd
spring.main.allow-bean-definition-overriding=true
#gateway常用配置
spring.cloud.gateway.default-filters=DedupeResponseHeader=Access-Control-Allow-Origin
spring.cloud.gateway.globalcors.add-to-simple-url-handler-mapping=true

# enabled：默认为false，设置为true表明spring cloud gateway开启服务发现和路由的功能，网关自动根据注册中心的服务名为每个服务创建一个router，将以服务名开头的请求路径转发到对应的服务
spring.cloud.gateway.discovery.locator.enabled=true
# lowerCaseServiceId：启动 locator.enabled=true 自动路由时，路由的路径默认会使用大写ID，若想要使用小写ID，可将lowerCaseServiceId设置为true
spring.cloud.gateway.discovery.locator.lower-case-service-id=true
