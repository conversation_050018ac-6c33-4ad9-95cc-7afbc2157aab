package com.juneyaoair.oneorder;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@SpringBootApplication
@EnableDiscoveryClient
@EnableApolloConfig(value = {"hocar-gateway2b", "application"})
public class HoCarGateway2BApplication {

    public static void main(String[] args) {
        SpringApplication.run(HoCarGateway2BApplication.class, args);
    }

}
