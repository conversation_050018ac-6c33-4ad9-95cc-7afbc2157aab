package com.juneyaoair.oneorder.mobile.filter;

import com.alibaba.fastjson.JSONObject;
import com.juneyaoair.oneorder.mobile.constant.WSEnum;
import com.juneyaoair.oneorder.mobile.context.GatewayContext;
import com.juneyaoair.oneorder.mobile.utils.JwtTokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
@Order(20)
public class OAuth2Filter implements GlobalFilter {

    AntPathMatcher pathMatcher = new AntPathMatcher();

    public String[] pathDirs = {"/Auth/oauth/**"};

    // 自定义不需要 token 的 uri命名空间或者路由
    @Value("${hocar.oauth.ignore:/Search/QueryFlightSimpleDemo}")
    private String ignore;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        try {
            final ServerHttpRequest request = exchange.getRequest();
            //默认不拦截get请求
            final HttpMethod method = request.getMethod();
            if (HttpMethod.GET.equals(method)) {
                return chain.filter(exchange);
            }
            //认证服务所有放行
            GatewayContext gatewayContext = exchange.getAttribute(GatewayContext.CACHE_GATEWAY_CONTEXT);
            String path = gatewayContext.getPath();
            if (Arrays.stream(pathDirs).anyMatch(b -> pathMatcher.match(b, path))) {
                return chain.filter(exchange);
            }

            //自定义不需要权限
            if (StringUtils.isNotBlank(ignore) &&
                    Arrays.stream(ignore.split(",")).anyMatch(i -> pathMatcher.match(i, path))) {
                return chain.filter(exchange);
            }

            //检查token是否存在
            String bearerToken = this.getToken(exchange);
            if (StringUtils.isBlank(bearerToken)) {
                return this.responseEnd(exchange);
            }
            List<String> resourceIds = JwtTokenUtil.getResourceIds(bearerToken);
            if (resourceIds == null) {
                return this.responseEnd(exchange);
            }

            //判断token是否过期
            Date expDate = JwtTokenUtil.getExpDate(bearerToken, JwtTokenUtil.KEY.exp.name());
            if (expDate == null) {
                return this.responseEnd(exchange);
            }
            if (expDate.getTime() < System.currentTimeMillis()) {
                return this.responseEnd(exchange);
            }

            //Token 权限不足
            log.info("->> Hocar Authorization: {}. channel:{}. path :{}", resourceIds, gatewayContext.getChannel_code(), gatewayContext.getPath());
            if (resourceIds.stream().noneMatch((i) -> pathMatcher.match(i, path))) {
                return this.responseEnd(WSEnum.CHANNEL_NO_AUTH,exchange);
            }

        } catch (Exception e) {
            log.error("Hocar Token 校验异常 {}", e.getMessage(), e);
            return this.responseEnd(exchange);
        }

        return chain.filter(exchange);
    }

    /**
     * 获取token
     */
    private String getToken(ServerWebExchange exchange) {
        String tokenStr = exchange.getRequest().getHeaders().getFirst("Authorization");
        if (StringUtils.isBlank(tokenStr)) {
            return null;
        }
        return StringUtils.substring(tokenStr, "Bearer ".length());
    }

    private Mono<Void> responseEnd(ServerWebExchange serverWebExchange) {
        try {
            return responseEnd(WSEnum.INVALID_TOKEN, serverWebExchange);
        } catch (Exception e) {
            log.error(e.toString());
        }
        return null;
    }

    /**
     * 统一的返回的结果
     *
     * @param status
     * @param serverWebExchange
     * @return
     */
    private synchronized Mono<Void> responseEnd(WSEnum status, ServerWebExchange serverWebExchange) {
        JSONObject json = new JSONObject();
        //目前官网所使用的状态信息返回
        json.put("code", status.name().toString());
        json.put("status", 200);
        json.put("message", status.getResultInfo());
        ServerHttpResponse response = serverWebExchange.getResponse();
        byte[] bits = json.toJSONString().getBytes(StandardCharsets.UTF_8);
        DataBuffer buffer = response.bufferFactory().wrap(bits);
        response.setStatusCode(HttpStatus.OK);
        //指定编码，否则在浏览器中会中文乱码
        response.getHeaders().add("Content-Type", "application/json; charset=utf-8");
        return response.writeWith(Mono.just(buffer));
    }
}
