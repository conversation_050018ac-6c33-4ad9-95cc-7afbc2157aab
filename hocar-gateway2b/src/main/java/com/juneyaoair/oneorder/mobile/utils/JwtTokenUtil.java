package com.juneyaoair.oneorder.mobile.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.InvalidClaimException;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.juneyaoair.oneorder.mobile.constant.VerifyTokenEnum;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Date 2018-07-12 14:23
 * @Desc JWT工具类
 **/
@Slf4j
public class JwtTokenUtil {

    // Token过期时间30分钟（用户登录过期时间是此时间的两倍，以token在redis缓存时间为准）
    public static final long EXPIRE_TIME = 30 * 60 * 1000L;
    private static final String TOKEN_SECRET = "juneyaoair";

    /**
     * 生成签名，15min后过期
     *
     * @param map
     * @return
     */
    public static String sign(Map map) {
        //设置过期时间
        long now = System.currentTimeMillis();
        Date date = new Date(now + EXPIRE_TIME);
        return sign(map, date);
    }

    /**
     * 生成签名，15min后过期
     *
     * @param map
     * @return
     */
    public static String sign(Map map, Date date) {
        try {
            //私钥及加密算法
            Algorithm algorithm = Algorithm.HMAC256(TOKEN_SECRET);
            //设置头部信息
            Map<String, Object> header = new HashMap<>();
            header.put("typ", "JWT");
            header.put("alg", "HS256");
            //附带username,userId信息，生成签名
            JWTCreator.Builder builder = JWT.create();
            builder.withHeader(header);

            map.forEach((k, v) -> {
                if (null != v) {
                    builder.withClaim((String) k, "java.lang.String".equals(getType(v)) ? v.toString() : HoAirGsonUtil.objectToJson(v));
                }
            });
            String newToken = builder.withExpiresAt(date).sign(algorithm);
            return newToken;
        } catch (Exception e) {
            log.info("sign Exception:", e);
            return null;
        }
    }

    //获取变量类型方法
    private static String getType(Object o) {
        return o.getClass().getName();
    }

    /**
     * 校验token是否正确
     *
     * @param token
     * @return
     */
    public static VerifyTokenEnum verifyToken(String token) {
        if (token == null || token.isEmpty()) {
            log.info("verifyToken 为空" + token);
            return VerifyTokenEnum.TOKEN_VALID;
        }

        try {
            Algorithm algorithm = Algorithm.HMAC256(TOKEN_SECRET);
            JWTVerifier verifier = JWT.require(algorithm).build();
            verifier.verify(token);
            return VerifyTokenEnum.TOKEN_SUCCESS;
        } catch (TokenExpiredException e) {
            log.info("verifyToken TokenExpiredException:" + token);
            //token过期
            return VerifyTokenEnum.TOKEN_EXPIRED;
        } catch (InvalidClaimException e) {
            log.info("verifyToken InvalidClaimException:" + token, e);
            //错误claim参数
            return VerifyTokenEnum.TOKEN_VALID;
        } catch (JWTDecodeException e) {
            log.info("verifyToken JWTDecodeException:" + token, e);
            //错误claim参数
            return VerifyTokenEnum.TOKEN_VALID;
        } catch (Exception e) {
            log.info("verifyToken Exception:" + token, e);
            //非法token
            return VerifyTokenEnum.TOKEN_EXCEPTION;
        }
    }

    /**
     * 校验token是否正确
     *
     * @param token  密钥
     * @param secret 用户的密码
     * @return 是否正确
     */
    public static boolean verify(String token, String username, String secret) {
        try {
            // 根据密码生成JWT效验器
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm).withClaim(KEY.username.name(), username).build();
            // 效验TOKEN
            DecodedJWT jwt = verifier.verify(token);
            return true;
        } catch (Exception exception) {
            return false;
        }
    }

    /**
     * 获得token中的信息无需secret解密也能获得
     *
     * @return token中包含的用户名
     */
    public static String getUsername(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim(KEY.username.name()).asString();
        } catch (JWTDecodeException e) {
            return null;
        }
    }

    public static List<String> getAuthorities(String token) {
        return getStringList(token, KEY.authorities.name());
    }

    public static List<String> getResourceIds(String token) {
        return getStringList(token, KEY.aud.name());
    }

    public static List<String> getScopes(String token) {
        return getStringList(token, KEY.scope.name());
    }

    public static String getString(String token, String key) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim(key).asString();
        } catch (JWTDecodeException e) {
            return null;
        }
    }

    public static List<String> getStringList(String token, String key) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim(key).asList(String.class);
        } catch (JWTDecodeException e) {
            return null;
        }
    }

    public static Date getExpDate(String token, String key) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim(key).asDate();
        } catch (JWTDecodeException e) {
            return null;
        }
    }

    /**
     * 生成签名,5min后过期
     *
     * @param username 用户名
     * @param secret   用户的密码
     * @return 加密的token
     */
    public static String sign(String username, String secret) {
        Date date = new Date(System.currentTimeMillis() + EXPIRE_TIME);
        Algorithm algorithm = Algorithm.HMAC256(secret);
        // 附带username信息
        return JWT.create().withClaim(KEY.username.name(), username).withExpiresAt(date).sign(algorithm);

    }

    public enum KEY {
        exp,
        authorities,
        username,
        scope,
        aud
    }
}