package com.juneyaoair.oneorder.mobile.dto;

import com.juneyaoair.oneorder.mobile.constant.WSEnum;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/6 9:49
 */
@Data
public class ResponseData<T> {
    private String code;
    private int status;
    private String message;
    private Long timestamp;
    private T data;


    private Map<String, Object> error;

    public static ResponseData fail(){
        ResponseData responseData = new ResponseData();
        responseData.setCode(WSEnum.FAIL.name());
        responseData.setMessage(WSEnum.FAIL.getResultInfo());
        return responseData;
    }
    public static ResponseData fail(String message){
        ResponseData responseData = new ResponseData();
        responseData.setCode(WSEnum.FAIL.name());
        responseData.setMessage(message);
        return responseData;
    }
}
