package com.juneyaoair.oneorder.mobile.constant;

/**
 * Created by qinxiaoming on 2016-4-15.
 */
public enum WSEnum {
    SUCCESS("成功"),
    FAIL("失败"),
    INVALID_TOKEN("登录凭证已失效，请重新登录"),
    CHANNEL_INFO_FAIL("请检查channel.map配置"),
    CHANNEL_NO_AUTH("无权限访问，请联系管理员配置"),
    ;
    private String resultInfo;

    WSEnum(String resultInfo) {
        this.resultInfo = resultInfo;
    }

    @Override
    public String toString() {
        return super.toString() + "(" + resultInfo + ")";
    }

    public String getResultInfo() {
        return resultInfo;
    }

}