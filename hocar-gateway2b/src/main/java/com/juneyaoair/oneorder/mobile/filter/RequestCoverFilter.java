package com.juneyaoair.oneorder.mobile.filter;

import com.juneyaoair.oneorder.mobile.context.GatewayContext;
import com.juneyaoair.oneorder.mobile.utils.HttpRequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 请求内容存储 处理请求内容 内容放在gatewayContext中
 */

/**
 * <AUTHOR>
 *
 * <p>
 * 请求内容存储 处理请求内容 内容放在gatewayContext中
 * </p>
 */
@Component
@Slf4j
public class RequestCoverFilter implements GlobalFilter, Ordered {


    @Override
    public int getOrder() {
        return HIGHEST_PRECEDENCE;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        /**
         * save request path and serviceId into gateway context
         */
        ServerHttpRequest request = exchange.getRequest();
        GatewayContext gatewayContext = new GatewayContext(request);
        log.debug("HttpMethod:{},Url:{}", request.getMethod(), request.getURI().getRawPath());

        /// 注意，因为webflux的响应式编程 不能再采取原先的编码方式 即应该先将gatewayContext放入exchange中，否则其他地方可能取不到
        /**
         * save gateway context into exchange
         */
        exchange.getAttributes().put(GatewayContext.CACHE_GATEWAY_CONTEXT, gatewayContext);
        // 处理参数
        MediaType contentType = gatewayContext.getHeaders().getContentType();
        long contentLength = gatewayContext.getHeaders().getContentLength();
        if (contentLength > 0) {
            if (MediaType.APPLICATION_JSON.equals(contentType) || MediaType.APPLICATION_JSON_UTF8.equals(contentType)) {
                return HttpRequestUtils.readBody(exchange, chain, gatewayContext);
            }
            if (MediaType.APPLICATION_FORM_URLENCODED.equals(contentType)) {
                return HttpRequestUtils.readFormData(exchange, chain, gatewayContext);
            }
        }
        // TODO 多版本划区域控制后期实现

        log.debug("[GatewayContext]ContentType:{},Gateway context is set with {}", contentType, gatewayContext);
        return chain.filter(exchange);
    }

}
