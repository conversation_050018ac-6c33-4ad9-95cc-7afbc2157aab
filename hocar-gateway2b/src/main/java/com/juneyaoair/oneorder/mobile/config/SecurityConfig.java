package com.juneyaoair.oneorder.mobile.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.web.server.SecurityWebFilterChain;


@EnableWebFluxSecurity
@Configuration
public class SecurityConfig {


    private static final String[] AUTH_WHITELIST = new String[]{};
    private static final String[] AUTH_DEFAULT = new String[]{"/doc.html", "/*/v2/api-docs", "/swagger-resources/**"};
    @Value("${show.api.disabled:true}")
    private boolean apiDisabled;

    @Bean
    public SecurityWebFilterChain springSecurityFilterChain(ServerHttpSecurity http) {
        final ServerHttpSecurity.AuthorizeExchangeSpec authorizeExchangeSpec = http.authorizeExchange();
        if (apiDisabled) {
            authorizeExchangeSpec.pathMatchers(AUTH_DEFAULT).denyAll();
        }
        return authorizeExchangeSpec.pathMatchers("/**").permitAll()
                .anyExchange().authenticated()
                .and()
                .csrf().disable()
                .httpBasic().disable()
                .build();
    }


}
