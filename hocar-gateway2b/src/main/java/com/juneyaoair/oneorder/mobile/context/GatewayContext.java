package com.juneyaoair.oneorder.mobile.context;

import com.juneyaoair.oneorder.mobile.constant.HeaderConstant;
import com.juneyaoair.oneorder.mobile.utils.HttpRequestUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;


/**
 * 网关上下文
 */
@Data
public class GatewayContext {

    public static final String CACHE_GATEWAY_CONTEXT = "cacheGatewayContext";
    /**
     * 会员ID
     */
    public static final String FFP_ID = "ffpId";
    /**
     * 会员NO
     */
    public static final String FFP_NO = "ffpNo";
    /**
     * 会员渠道号
     */
    public static final String CHANNEL_NO = "channelNo";
    /**
     * 请求来源IP
     */
    public static final String ORIGIN_IP = "originIp";

    /**
     * cache headers
     */
    private HttpHeaders headers;

    /**
     * cache json body
     */
    private String cacheBody;
    /**
     * cache formdata
     */
    private MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();

    /**
     * ipAddress
     */
    private String ipAddress;

    /**
     * path
     */
    private String path;

    /**
     * token
     */
    private String token;

    /**
     * 机器码
     */
    private String machine_code;

    /**
     * 渠道号
     */
    private String channel_code;

    /**
     * 客户端版本
     */
    private String version;

    public GatewayContext() {

    }

    public GatewayContext(ServerHttpRequest request) {
        path = request.getPath().pathWithinApplication().value();
        formData.addAll(request.getQueryParams());
        ipAddress = HttpRequestUtils.getIpAddress(request);
        headers = request.getHeaders();

        token = headers.getFirst(HeaderConstant.TOKEN);
        channel_code = headers.getFirst(HeaderConstant.CHANNEL_CODE);
        version = headers.getFirst(HeaderConstant.VERSION);
        machine_code = headers.getFirst(HeaderConstant.MACHINE_CODE);
    }
}