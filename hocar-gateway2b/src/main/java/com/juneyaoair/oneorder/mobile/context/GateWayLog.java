package com.juneyaoair.oneorder.mobile.context;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Date;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GateWayLog {

    /**
     * 同一请求标识
     */
    private String group;
    /**
     * 访问实例
     */
    private String serviceName;

    private String method;

    /**
     * 请求 request / response
     */
    private String type;
    /**
     * 请求路径
     */
    private String path;
    /**
     * 请求方法
     */
    private String requestMethod;
    /**
     * 协议
     */
    private String schema;
    /**
     * 请求体
     */
    private String requestBody;
    /**
     * 响应体
     */
    private String responseData;
    /**
     * 请求ip
     */
    private String clientIp;
    /**
     * 请求时间
     */
    private Date requestTime;
    /**
     * 响应时间
     */
    private Date responseTime;
    /**
     * 执行时间
     */
    private long interval;

}
