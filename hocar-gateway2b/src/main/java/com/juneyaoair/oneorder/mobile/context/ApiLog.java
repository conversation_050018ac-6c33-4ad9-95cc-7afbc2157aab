package com.juneyaoair.oneorder.mobile.context;

import lombok.Data;

import java.util.Date;

@Data
public class ApiLog {
    /**
     * 同一请求标识
     */
    private String group;
    /**
     * 访问实例
     */
    private String serviceName;

    private String method;

    /**
     * 请求 request / response
     */
    private String type;
    /**
     * 请求路径
     */
    private String path;
    /**
     * 请求方法
     */
    private String requestMethod;
    /**
     * 协议
     */
    private String schema;
    /**
     * 请求体
     */
    private String message;
    /**
     * 请求ip
     */
    private String clientIp;
    /**
     * 请求时间
     */
    private Date requestTime;
    /**
     * 执行时间
     */
    private long interval;
}
