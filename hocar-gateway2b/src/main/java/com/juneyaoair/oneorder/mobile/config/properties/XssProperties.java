package com.juneyaoair.oneorder.mobile.config.properties;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * XSS跨站脚本配置
 * 
 * <AUTHOR>
 */
@Configuration
public class XssProperties
{
    /**
     * Xss开关
     */
    @Value("${security.xss.enabled:true}")
    private Boolean enabled;

    /**
     * 排除路径
     */
    @ApolloJsonValue("${security.xss.excludeUrls:[]}")
    private List<String> excludeUrls = new ArrayList<>();

    public Boolean getEnabled()
    {
        return enabled;
    }

    public void setEnabled(Boolean enabled)
    {
        this.enabled = enabled;
    }

    public List<String> getExcludeUrls()
    {
        return excludeUrls;
    }

    public void setExcludeUrls(List<String> excludeUrls)
    {
        this.excludeUrls = excludeUrls;
    }
}
