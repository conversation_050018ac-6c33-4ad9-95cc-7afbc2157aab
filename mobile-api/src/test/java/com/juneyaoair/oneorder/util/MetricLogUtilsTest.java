package com.juneyaoair.oneorder.util;

import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @description
 * @date 2025/1/11 10:19
 */
@Slf4j
public class MetricLogUtilsTest {
    @Test
    public void test(){
        CommonErrorCode commonErrorCode = CommonErrorCode.valueOf("ACCOUNT_ERROR_OVER_LIMIT");
        Assert.assertNotNull(commonErrorCode);
    }

}