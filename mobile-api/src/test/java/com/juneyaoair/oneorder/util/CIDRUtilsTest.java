package com.juneyaoair.oneorder.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.net.InetAddress;
import java.net.UnknownHostException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
public class CIDRUtilsTest {



    private CIDRUtils cidrUtils;

    @BeforeEach
    void setUp() throws UnknownHostException {
        cidrUtils = new CIDRUtils("**********/16");
    }

    private int ipToInt(String ipStr) {
        String[] parts = ipStr.split("\\.");
        int ip = 0;
        for (String part : parts) {
            ip = (ip << 8) | Integer.parseInt(part);
        }
        return ip;
    }

    @Test
    void testContains_IPInCIDR_ReturnsTrue() throws Exception {
        // Arrange
        String ipStr = "**********";

        // Act
        boolean result = cidrUtils.contains(ipStr);

        // Assert
        assertTrue(result);
    }

    @Test
    void testContains_IPNotInCIDR_ReturnsFalse() throws Exception {
        // Arrange
        String ipStr = "**********";

        // Act
        boolean result = cidrUtils.contains(ipStr);

        // Assert
        assertFalse(result);
    }

    @Test
    void testContains_InvalidIP_ThrowsException() {
        // Arrange
        String invalidIP = "invalid.ip.address";

        // Act & Assert
        assertThrows(UnknownHostException.class, () -> cidrUtils.contains(invalidIP));
    }
}
