package com.juneyaoair.oneorder.api.email.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/17 13:01
 */
@Data
@ApiModel
public class SendEmailCodeParam {
    @ApiModelProperty(value = "邮箱地址")
    @NotBlank(message = "请输入邮箱地址")
    private String email;
}
