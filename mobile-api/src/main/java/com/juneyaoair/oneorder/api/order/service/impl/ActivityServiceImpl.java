package com.juneyaoair.oneorder.api.order.service.impl;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.oneorder.api.common.HttpBaseServiceImpl;
import com.juneyaoair.oneorder.api.order.config.OrderConfig;
import com.juneyaoair.oneorder.api.order.dto.activity.BaseResultDTO;
import com.juneyaoair.oneorder.api.order.dto.activity.PtSuggestionDto;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Component
public class ActivityServiceImpl extends HttpBaseServiceImpl implements IActivityService {

    @Resource
    OrderConfig config;

    @Override
    public BaseResultDTO createFeedback(PtSuggestionDto req, Map<String, String> map) {
        return invokeHttpClient(req,
                config.URL_FARE_API_ACTIVITY + config.CREATE_SUGGESTION,
                new TypeToken<BaseResultDTO>() {
                }.getType(),
                map);
    }

}
