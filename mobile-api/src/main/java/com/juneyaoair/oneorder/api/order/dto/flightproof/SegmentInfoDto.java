package com.juneyaoair.oneorder.api.order.dto.flightproof;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class SegmentInfoDto {
    /**
     * 航班编号
     */
    private String FlightNo;
    /**
     * 计划起飞时间 hh:mm
     * 当地时间
     */
    private String DepTime;
    /**
     * 计划起飞时间 hh:mm
     * 当地时间
     */
    private String DepSpecificTime;
    /**
     * 计划到达时间
     * 当地时间
     */
    private String ArrTime;
    /**
     * 计划到达时间 hh：mm
     * 当地时间
     */
    private String ArrSpecificTime;
    /**
     * 实际起飞时间
     * 当地时间
     */
    private String ActDepTime;
    /**
     * 实际到达时间
     * 当地时间
     */
    private String ActArrTime;
    /**
     * 航班状态 C:取消  ，D：延误
     */
    private String SegmentStatus;
    /**
     * 航班状态原因原因
     */
    private String StatusReason;
    /**
     * 到达航站楼
     */
    private String ArrAirportTerminal;
    /**
     * 起飞航站楼
     */
    private String DepAirportTerminal;
    /**
     * 到达机场代码
     */
    private String ArrAirportCode;
    /**
     * 起飞机场代码
     */
    private String DepAirportCode;
    /**
     * 到达机场中文起飞机场中文
     */
    private String ArrAirportName;
    /**
     *出发机场中文起飞机场中文
     */
    private String DepAirportName;
    /**
     * 飞行时长
     */
    private long FlightTime;
    /**
     * 跨天数
     */
    private int day;
    /**
     * 客票状态
     */
    private String TicketStatus;

    /**
     * 离港延误标记
     */
    private Boolean DepDelay;
    /*
     * 到港延误标记
     */
    private Boolean ArrDelay;

    /**
     * 出发城市时区
     */
    private Integer DepCityZone;
    /**
     * 到达城市时区
     */
    private Integer ArrCityZone;

}
