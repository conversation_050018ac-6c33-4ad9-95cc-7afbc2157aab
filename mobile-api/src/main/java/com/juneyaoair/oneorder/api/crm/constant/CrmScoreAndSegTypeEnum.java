package com.juneyaoair.oneorder.api.crm.constant;

/**
 * @ClassName CrmScoreTypeEnum
 * @Description
 * <AUTHOR>
 * @Date 2023/6/30 14:24
 * @Version 1.0
 */
public enum CrmScoreAndSegTypeEnum {
    GradingScore("1","定级积分"),
    PartingScore("2","伙伴积分"),
    ExtraScore("3","额外积分"),
    PromotionScore("4","促销积分"),
    GradingSegment("5","定级航段"),
    ConsumingScore("","消费/扣减积分")
    ;
    public final String code;
    public final String desc;

    CrmScoreAndSegTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
