package com.juneyaoair.oneorder.api.passager;

import com.juneyaoair.oneorder.api.passager.dto.*;

public interface IPassagerService {
    PriSugComB2CResponse addPriSugComB2C(PriSugComB2CRequest req);

    LostItemsQueryResponse lostAndFoundQuery(LostItemsRequest req);

    LostItemsResponse checkInitemsLost(LostItemsRequest req);

    /**
     * 查询获客配置信息
     * @param cityCode
     * @return
     */
    CustLinkInfo getCustLink(String cityCode);

    /**
     * 新增免打扰记录
     * @param notDisturbParam
     */
    void addNotDisturbRecord(NotDisturbParam notDisturbParam);

}
