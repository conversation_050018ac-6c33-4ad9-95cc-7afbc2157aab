package com.juneyaoair.oneorder.api.b2chand;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class B2CBandConfig {

    @Value("${UnitOrder.Activity.URL:http://b2cmng.juneyaoair.com:88}")
    public String UNITORDER_ACTIVITY_URL;

    public static final String FLIGHT_ABNORMALITY_CHECK = "/B2CManageHandler/CheckFlightAbnormality";
    public static final String FLIGHT_ABNORMALITY_QUERY = "/B2CManageHandler/FlightAbnormality";
}
