package com.juneyaoair.oneorder.api.order.dto.ticket;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/11 16:03
 */
@Data
public class QueryTicketRuleInfoResult {
    private String traceId;
    private String ResultCode;
    private String ErrorInfo;
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String TicketNo;
    private PassengerInfo PassengerInfo;
    private List<SegRuleInfo> SegRuleInfoList;

    @Data
    public static class PassengerInfo{
        private int PassengerID;
        private int PassengerNO;
        private String PassengerName;
        private String CertType;
        private String CertNo;
        private int SupplyAmount;
        private String BelongCountry;
        private int FfpUseScore;
        private boolean InsurancePolicyHolder;
    }

    @Data
    public static class SegRuleInfo{
        private long SegmentID;
        private int SegmentSeq;
        private String DepAirport;
        private String ArrAirport;
        private String TicketState;
        private String Currency;
        private int TicketPrice;
        private int PricePaid;
        private int YQTax;
        private int CNTax;
        private String RefundedComment;
        private String ChangedComment;
        private List<TicketRule> ChangeRules;
        private List<TicketRule> RefundedRules;
    }
}
