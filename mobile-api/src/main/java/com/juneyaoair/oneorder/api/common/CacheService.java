package com.juneyaoair.oneorder.api.common;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.benmanes.caffeine.cache.Cache;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.juneyaoair.flightbasic.appenum.ChannelCodeEnum;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.feign.FlightBasicProviderClient;
import com.juneyaoair.flightbasic.response.api.ApiAirPortInfoDto;
import com.juneyaoair.flightbasic.response.api.ApiCityInfoDto;
import com.juneyaoair.flightbasic.utils.IpUtils;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.oneorder.api.crm.constant.TripTypeEnum;
import com.juneyaoair.oneorder.localcache.Segment;
import com.juneyaoair.oneorder.mobile.config.RedisConstantConfig;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.tools.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @description 热点数据缓存服务
 * @date 2023/6/20 11:13
 */
@Service
@Slf4j
public class CacheService {
    @Resource(name = "cityCache")
    private Cache<String, Object> cityCache;
    @Resource(name = "airportCache")
    private Cache<String, Object> airportCache;

    private final List<String> countryCodeList = Lists.newArrayList("TW", "HK", "MO");

    @Autowired
    private FlightBasicProviderClient flightBasicProviderClient;
    @Autowired
    private RedisConstantConfig redisConstantConfig;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 获取城市信息
     * @param cityCode
     * @return
     */
    public ApiCityInfoDto getLocalCity(String cityCode) {
        ApiCityInfoDto cityInfoDto = (ApiCityInfoDto) cityCache.getIfPresent(cityCode);
        if (Objects.nonNull(cityInfoDto)) {
            return cityInfoDto;
        }
        //如果本地缓存不存在则从远端获取
        cityInfoDto = selectCity(cityCode);
        if (Objects.nonNull(cityInfoDto)) {
            cityCache.put(cityCode, cityInfoDto);
            return cityInfoDto;
        }
        return cityInfoDto;
    }

    /**
     * 根据城市三字码获取
     *
     * @param cityCode
     * @return
     */
    private ApiCityInfoDto selectCity(String cityCode) {
        String key = redisConstantConfig.getLocalRedisKey(redisConstantConfig.REDIS_CITY_INFO_HASH);
        Object obj = redisUtil.hget(key, cityCode);
        //如果未查询到重清除缓存重新查询，可能是缓存未更新
        if (obj == null) {
            log.info("{}缓存中未查询到城市信息", cityCode);
            String errorKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.REDIS_CITY_INFO_ERROR + cityCode);
            //检查系统中是否标记为异常数据，异常数据直接返回
            if (!Objects.isNull(redisUtil.get(errorKey)) && StringUtils.isNotBlank(redisUtil.get(errorKey).toString())) {
                return null;
            }
            Map<String, Object> cityStrMap = saveCityInfoRedis(key);
            if (cityStrMap != null) {
                if (cityStrMap.get(cityCode) == null) {
                    log.info("{}数据库中未查询到城市信息", cityCode);
                    //标记为异常数据，记录缓存信息
                    redisUtil.set(errorKey, cityCode, 3600 * 24L);
                    return null;
                }
                return JacksonUtil.objectToDto(cityStrMap.get(cityCode), new TypeReference<ApiCityInfoDto>() {
                });
            }
        } else {
            return JacksonUtil.objectToDto(obj, new TypeReference<ApiCityInfoDto>() {
            });
        }
        return null;
    }

    /**
     * 查询城市信息并进行redis缓存
     *
     * @return
     */
    private Map<String, Object> saveCityInfoRedis(String key) {
        Map<String, Object> cityMap = new HashMap<>();
        BaseRequestDTO baseRequestDTO = BaseRequestDTO.builder()
                .ip(IpUtils.getLocalIp())
                .build();
        baseRequestDTO.setVersion("10");
        baseRequestDTO.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        BaseResultDTO<List<ApiCityInfoDto>> baseResultDTO = flightBasicProviderClient.queryCity(baseRequestDTO);
        if (CollectionUtils.isNotEmpty(baseResultDTO.getResult())) {
            for (ApiCityInfoDto cityInfoDtoTemp : baseResultDTO.getResult()) {
                cityMap.put(cityInfoDtoTemp.getCityCode(), cityInfoDtoTemp);
            }
            redisUtil.hmset(key, cityMap, 300L);
        }
        return cityMap;
    }

    /**
     * 获取机场信息
     * @param airportCode
     * @return
     */
    public ApiAirPortInfoDto getLocalAirport(String airportCode) {
        if (airportCode == null) {
            return null;
        }
        ApiAirPortInfoDto airPortInfo = (ApiAirPortInfoDto) airportCache.getIfPresent(airportCode);
        if (Objects.nonNull(airPortInfo)) {
            return airPortInfo;
        }
        String key = redisConstantConfig.getLocalRedisKey(RedisConstantConfig.REDIS_AIRPORT_INFO_HASH);
        Object obj = redisUtil.hget(key, airportCode);
        if (Objects.nonNull(obj)) {
            return JacksonUtil.objectToDto(obj, new TypeReference<ApiAirPortInfoDto>() {
            });
        }
        //如果本地缓存不存在则从远端获取
        Map<String, ApiAirPortInfoDto> airPortInfoMap = selectAirport(key);
        if (airPortInfoMap != null && airPortInfoMap.size() > 0) {
            airPortInfo = airPortInfoMap.get(airportCode);
            if (airPortInfo == null) {
                log.info("{}数据库中未查询到机场信息", airportCode);
                //标记为异常数据，记录缓存信息
                String errorKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.REDIS_AIRPORT_INFO_ERROR
                        + airportCode);
                redisUtil.set(errorKey, airportCode, 3600 * 24L);
                return null;
            }
            airportCache.put(airportCode, airPortInfo);
            return airPortInfo;
        }
        return null;
    }

    private Map<String, ApiAirPortInfoDto> selectAirport(String key) {
        Map<String,ApiAirPortInfoDto> apiAirPortInfoDtoMap = new HashMap<>();
        BaseRequestDTO baseRequestDTO = BaseRequestDTO.builder()
                .ip(IpUtils.getLocalIp())
                .build();
        baseRequestDTO.setVersion("10");
        baseRequestDTO.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        BaseResultDTO<List<ApiAirPortInfoDto>> allAirPortResp = flightBasicProviderClient.queryAllAirports(baseRequestDTO);
        if (CollectionUtils.isNotEmpty(allAirPortResp.getResult())) {
            for (ApiAirPortInfoDto apiAirPortInfoDto : allAirPortResp.getResult()) {
                apiAirPortInfoDtoMap.put(apiAirPortInfoDto.getAirPortCode(), apiAirPortInfoDto);
            }
            redisUtil.hmset2(key, apiAirPortInfoDtoMap, 300L);
        }
        return apiAirPortInfoDtoMap;
    }

    /**
     * 获取航线类型航线类型
     *
     * @param cityArray
     * @return “”：未知  CN:国内 TW：台湾 HK：香港  MO：澳门 其他：国外
     */
    public Segment getSegment(String... cityArray) {
        Segment segment = new Segment();
        if (ArrayUtils.isEmpty(cityArray)) {
            return segment;
        }
        Set<String> cityCodeSet = Sets.newHashSet();
        for (String cityCode : cityArray) {
            if (StringUtils.isNotBlank(cityCode)) {
                cityCodeSet.add(cityCode);
            }
        }
        segment.setCityCodeSet(cityCodeSet);
        String segmentCode = null;
        for (String cityCode : cityCodeSet) {
            ApiCityInfoDto cityInfo = this.getLocalCity(cityCode);
            if (null == cityInfo) {
                log.error("getSegment未获取到：{}城市信息", cityCode);
                throw new ServiceException("未获取到城市信息");
            }
            String countryCode = cityInfo.getCountryCode();
            // 非国内 非港澳台 结束处理
            if (!"CN".equals(countryCode) && !countryCodeList.contains(countryCode)) {
                segmentCode = "INTERNAL";
                break;
            }
            // 当前已近为TW 处理下一个数据
            if ("TW".equals(segmentCode)) {
                continue;
            }
            // 数据为台湾 标记为台湾
            if ("TW".equals(countryCode)) {
                segmentCode = countryCode;
                continue;
            }
            // 当前数据为香港澳门
            if (countryCodeList.contains(segmentCode)) {
                continue;
            }
            segmentCode = countryCode;
        }
        segment.setSegmentCode(segmentCode);
        segment.setSegmentType(getSegmentType(segmentCode));
        return segment;
    }

    /**
     * 获取航线类型
     *
     * @param segmentCode
     * @return
     */
    private String getSegmentType(String segmentCode) {
        if (StringUtils.isBlank(segmentCode)) {
            return null;
        }
        // 国内航线
        if ("CN".equals(segmentCode)) {
            return TripTypeEnum.TRIP_TYPE_D.getCode();
        }
        // 港澳台航线
        if (countryCodeList.contains(segmentCode) || "HK_MO".equals(segmentCode)) {
            return TripTypeEnum.TRIP_TYPE_D.getCode();
        }
        // 国际航线
        return TripTypeEnum.TRIP_TYPE_I.getCode();
    }
}
