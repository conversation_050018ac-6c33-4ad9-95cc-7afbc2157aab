package com.juneyaoair.oneorder.api.crm.constant;

/**
 * <AUTHOR>
 * @description 邮寄地址类型
 * @date 2020/1/3  11:36.
 */
public enum PostAddressTypeEnum {
    UNKNOWN(0,"UNKNOW","未知"),
    COMPANY(1,"O","单位地址"),
    FAMILY(2,"H","家庭地址"),
    POST(3,"E","邮寄地址"),
    OTHER(99,"OTHER","其他");
    private int code;
    private String name;
    private String desc;

    PostAddressTypeEnum(int code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

}
