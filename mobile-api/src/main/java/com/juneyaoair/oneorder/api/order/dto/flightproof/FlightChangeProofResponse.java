package com.juneyaoair.oneorder.api.order.dto.flightproof;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class FlightChangeProofResponse {
    /**
     * 渠道用户号
     */
    private String ChannelCode;
    /**
     *渠道订单编号
     */
     private String ChannelOrderNo;
    /**
     *渠道工作人员
     */
    private String UserNo;
    /**
     *航段信息列表
     */
    private List<IBETicketInfo> IBETicketInfoList;
    /**
     *结果代码
     */
    private String ResultCode;
    /**
     *错误信息
     */
    private String ErrorInfo;
}
