package com.juneyaoair.oneorder.api.b2chand;

import lombok.Data;

@Data
public class FlightAbnormality {
    public String Id;
    public String FlightNo;
    public String FlightDate;
    public String DepartureCityCode;
    public String DepartureCity;
    public String DepartureCityEn;
    public String ArrivalCityCode;
    public String ArrivalCity;
    public String ArrivalCityEn;
    public String DepartureAirportCode;
    public String DepartureAirport;
    public String DepartureAirportEn;
    public String ArrivalAirport;
    public String ArrivalAirportEn;
    public String DepartureDate;
    public String ArrivalDate;
    public String FlightStatus;//航班状态
    public String Reason;
    public String ReasonEn;
    public String ActDepDate;
    public String ActArrDate;
    public String VIACityCode;//中转城市三字码
    public String VIACity;//中转城市
    public String VIAAirportCode;//中转机场三字码
    public String VIACityEN;//中转城市英文

}
