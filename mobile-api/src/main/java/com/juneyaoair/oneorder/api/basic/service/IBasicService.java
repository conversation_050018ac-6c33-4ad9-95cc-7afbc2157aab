package com.juneyaoair.oneorder.api.basic.service;

import com.github.pagehelper.PageInfo;
import com.juneyaoair.flightbasic.advertisement.AdvertisementDto;
import com.juneyaoair.flightbasic.advertisement.AdvertisementParam;
import com.juneyaoair.flightbasic.aircraft.AircraftTypeInfo;
import com.juneyaoair.flightbasic.api.index.SaleOfficeReq;
import com.juneyaoair.flightbasic.api.index.SaleOfficeResp;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.commondto.RequestData;
import com.juneyaoair.flightbasic.request.SelectFlightCabinReq;
import com.juneyaoair.flightbasic.request.country.TCountryReqDTO;
import com.juneyaoair.flightbasic.request.flightInfo.FlightInfoReqDTO;
import com.juneyaoair.flightbasic.request.notice.NoticeRequestDTO;
import com.juneyaoair.flightbasic.response.SelectFlightCabinDto;
import com.juneyaoair.flightbasic.response.airline.FollowAirLineResDTO;
import com.juneyaoair.flightbasic.response.airport.AirPortInfoDTO;
import com.juneyaoair.flightbasic.response.api.HolidayCalendar;
import com.juneyaoair.flightbasic.response.city.CityInfoDTO;
import com.juneyaoair.flightbasic.response.country.TCountryDTO;
import com.juneyaoair.flightbasic.response.flightInfo.FlightInfoDTO;
import com.juneyaoair.flightbasic.response.notice.NoticeInfoResponseDTO;
import com.juneyaoair.flightbasic.response.notice.TNoticeInfoResponseDTO;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;

import java.util.List;

/**
 * @ClassName IBasicService
 * @Description
 * <AUTHOR>
 * @Date 2023/6/25 8:39
 * @Version 1.0
 */
public interface IBasicService {
    /**
     * @param ip
     * @param channelCode
     * @return java.util.List<com.juneyaoair.flightbasic.response.city.CityInfoDTO>
     * <AUTHOR>
     * @Description 获取全部城市信息
     * @Date 8:55 2023/6/25
     **/
    List<CityInfoDTO> toCatchAllCityList(String ip, String channelCode, String cityCode);


    /**
     * @param ip
     * @param channelCode
     * @param airportCode
     * @param cityCode
     * @return java.util.List<com.juneyaoair.flightbasic.response.airport.AirPortInfoDTO>
     * <AUTHOR>
     * @Description 获取全部机场信息
     * @Date 9:13 2023/6/25
     **/
    List<AirPortInfoDTO> toCatchAllAirPortList(String ip, String channelCode, String airportCode, String cityCode);

    /**
     * 国家列表查询
     *
     * @return
     */
    List<TCountryDTO> queryCountries(String ip, String channelCode, TCountryReqDTO tCountryReqDTO);

    /**
     * 查询航班信息
     */
    List<FlightInfoDTO> searchFlightInfo(BaseRequestDTO<FlightInfoReqDTO> baseRequestDTO);

    /**
     * 查询指定航班信息
     * @param flightNo
     * @param flightDate
     * @param depAirportCode
     * @param arrAirportCode
     * @return
     */
    FlightInfoDTO searchSingleFlightInfo(String flightNo, String flightDate, String depAirportCode, String arrAirportCode);

    /**
     * 查询公司相关披露信息，运营报告，社会责任报告
     *
     * @param bizDto
     * @param advertisementParam
     * @return
     */
    PageInfo<AdvertisementDto> queryAdvertisementListByPage(BizDto bizDto, AdvertisementParam advertisementParam);


    /**
     * 查询服务条款
     *
     * @param bizDto
     * @param noticeRequestDTO
     * @return
     */
    List<NoticeInfoResponseDTO> getAllNoticeInfo(BizDto bizDto, NoticeRequestDTO noticeRequestDTO);

    /**
     * 查询指定的条款富文本信息
     *
     * @param bizDto
     * @param noticeRequestDTO
     * @return
     */
    TNoticeInfoResponseDTO getRichTextNoticeInfo(BizDto bizDto, NoticeRequestDTO noticeRequestDTO);

    /**
     * 查询关注航班列表
     *
     * @param bizDto
     * @param ffpCardNo
     */
    List<FollowAirLineResDTO> queryAttentionFlightList(BizDto bizDto, String ffpCardNo);


    /**
     * 获取营业部信息
     * @param data
     * @return
     */
    SaleOfficeResp getSaleOffice(SaleOfficeReq data, LanguageEnum languageEnum);

    /**
     * 根据机型查询机型名称
     * @param planType
     * @param language
     * @return
     */
    String getPlaneTypeName(String planType, String language);

    /**
     * 获取机型信息
     * @param planType
     * @return
     */
    AircraftTypeInfo getAircraftTypeInfo(String planType);

    /**
     * <AUTHOR>
     * @Description 节假日列表查询
     * @Date 10:39 2024/4/10
     * @return com.juneyaoair.flightbasic.response.HolidayCalenderResponse
     **/
    List<HolidayCalendar> toCatchHolidayCalender();

    /**
     * 积分策略验证
     * @param channelNo
     * @param originIp
     * @param ffpId
     * @param ffpNo
     */
    void checkScoreRule(String channelNo, String originIp, String ffpId, String ffpNo);

    /**
     * 航班舱位查询
     * @param selectFlightCabinReq
     * @return
     */
    List<SelectFlightCabinDto> selectFlightCabin(SelectFlightCabinReq selectFlightCabinReq);

}
