package com.juneyaoair.oneorder.api.crm.clientfeign;

import com.juneyaoair.oneorder.api.crm.dto.hocar.AccountRelationQueryReqDto;
import com.juneyaoair.oneorder.api.crm.dto.hocar.RelationAccount;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/2 14:31
 */
@FeignClient(name = "hocrm-service-bff", fallbackFactory = CrmBffClientFallbackFactory.class, contextId = "CrmBffClient")
public interface CrmBffClient {
    /**
     * 查询吉祥汽车关联的账户信息
     * @param requestData
     * @return
     */
    @PostMapping(value = "/accountRelationQuery")
    ResponseData<RelationAccount> accountRelationQuery(RequestData<AccountRelationQueryReqDto> requestData);
}
