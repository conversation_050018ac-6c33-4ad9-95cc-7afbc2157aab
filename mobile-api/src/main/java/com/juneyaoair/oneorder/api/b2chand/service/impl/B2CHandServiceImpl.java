package com.juneyaoair.oneorder.api.b2chand.service.impl;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.oneorder.api.b2chand.B2CBandConfig;
import com.juneyaoair.oneorder.api.b2chand.dto.FlightAbnormalityCheckRequest;
import com.juneyaoair.oneorder.api.b2chand.dto.FlightAbnormalityRequest;
import com.juneyaoair.oneorder.api.b2chand.dto.FlightAbnormalityResponse;
import com.juneyaoair.oneorder.api.b2chand.service.IB2CHandService;
import com.juneyaoair.oneorder.api.common.HttpBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class B2CHandServiceImpl extends HttpBaseServiceImpl implements IB2CHandService {

    @Resource
    B2CBandConfig config;
    @Override
    public FlightAbnormalityResponse checkFlightAbnormality(FlightAbnormalityCheckRequest request){
        return invokeHttpClient(request,config.UNITORDER_ACTIVITY_URL+config.FLIGHT_ABNORMALITY_CHECK,
                new TypeToken<FlightAbnormalityResponse>(){}.getType());

    }

    @Override
    public FlightAbnormalityResponse queryFlightAbnormality(FlightAbnormalityRequest request) {
        return invokeHttpClient(request,config.UNITORDER_ACTIVITY_URL+config.FLIGHT_ABNORMALITY_QUERY,
                new TypeToken<FlightAbnormalityResponse>(){}.getType());

    }
}
