package com.juneyaoair.oneorder.api.pay.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName VerifyCodeSendResult
 * @Description
 * <AUTHOR>
 * @Date 2024/4/18 14:35
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VerifyCodeSendResult {
    /**
     * <AUTHOR>
     * @Description 验证码序号,两位
     * @Date 14:40 2024/4/18
     **/
    public String verificationCodeNum;

    public String paymentTransId;

}
