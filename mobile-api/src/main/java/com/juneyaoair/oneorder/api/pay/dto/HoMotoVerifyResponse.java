package com.juneyaoair.oneorder.api.pay.dto;

import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;

/**
 * @ClassName VerifyCodeSendResult
 * @Description
 * <AUTHOR>
 * @Date 2024/4/18 10:53
 * @Version 1.0
 */

@ApiModel(value = "VerifyCodeSendResult", description = "验证码发送响应体")
@Data
@Builder
public class HoMotoVerifyResponse {
    private String RespCode;
    private String ErrorMsg;
    private String PaymentTransId;
    private String PhoneLastFourNum;
    private String VerificationCodeNum;
}
