package com.juneyaoair.oneorder.api.order.dto.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "产品属性")
public class ProductFieldDto {
    @ApiModelProperty(value="字段名")
    private String fieldCode;
    @ApiModelProperty(value="字段说明")
    private String fieldValue;
    @ApiModelProperty(value="字段类型")
    private String fieldType;
    @ApiModelProperty(value="是否多值")
    private Boolean isMultiple;

}