package com.juneyaoair.oneorder.api.order.dto.coupon;

import lombok.Data;

/**
 * @description  WIFI属性参数
 */
@Data
public class WifiProduct {
    /**
     *商品名称
     */
    private String WifiProductName;
    /**
     * 供应商名称
     */
    private String VendorName;
    /**
     *租用份数
     */
    private String BorrowNumber;
    /**
     *领用日期
     */
    private String BorrowDate;
    /**
     * 领用站名
     */
    private String BorrowName;
    /**
     *领用地址
     */
    private String BorrowAddress;
    /**
     *返还日期
     */
    private String RecycleDate;
    /**
     * 返还站名
     */
    private String RecycleName;
    /**
     *返还地址
     */
    private String RecycleAddress;
    /**
     * 取货方式
     */
    private String ShipWay;
}
