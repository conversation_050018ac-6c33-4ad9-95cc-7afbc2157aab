package com.juneyaoair.oneorder.api.order.dto.flightproof;

import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class FlightChangeProofApplyReq {
    /**
     * 接口版本号
     */
    private String Version;
    /**
     * 渠道用户号
     */
    private String ChannelCode;
    /**
     * 渠道工作人员
     */
    private String UserNo;
    /**
     * 电子客票号
     */
    private String TicketNo;
    /**
     * 证件号描述
     */
    private String CertNo;
    /**
     * 证件类型
     */
    private String CertType;
    /**
     * 航班日期
     */
    private String FlightDate;

    /**
     * 航班号
     */
    private String FlightNo;

    /**
     * 业务场景
     * DELAYPROVE 延误证明 INVOLUNTARY:非自愿
     */
    private String BusinessType;
    /**
     * 旅客姓名
     **/
    private String PassengerName;

    public FlightChangeProofApplyReq(String version, String channelCode, String userNo){
        this.Version = version;
        this.ChannelCode = channelCode;
        this.UserNo = userNo;
    }
}
