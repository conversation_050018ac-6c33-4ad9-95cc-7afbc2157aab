package com.juneyaoair.oneorder.api.order.dto.ticket;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * @ClassName TrrDateLimit
 * @Description 非自愿改期条件限制
 * //例：(HO1234; 3,3; 2019-10-01,2019-10-07)仅HO1234，可改期前后三天，且航班日期不可改到国庆期间
 *         //(*; 3,3; 2019-10-01,2019-10-07)任何航班，可改期前后三天，且航班日期不可改到国庆期间
 **/
@Data
public class TrrDateLimit {

    /**
     * 可改期的航班号，逗号分隔 *表示全部
     */
    @SerializedName("FlightNo")
    private String flightNo;

    /**
     * 日期限制
     * 2,3
     * 可改期至原航班日期前2天-后3天
     */
    @SerializedName("ChangeDateLimit")
    private String changeDateLimit;

    /**
     * 不可改期的日期范围
     * 2019-10-01,2019-10-07 表示不可改期至此区间范围内
     */
    @SerializedName("FlightDateLimit")
    private String flightDateLimit;

}
