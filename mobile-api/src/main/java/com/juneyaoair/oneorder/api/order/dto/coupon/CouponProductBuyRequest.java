package com.juneyaoair.oneorder.api.order.dto.coupon;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
public class CouponProductBuyRequest {
    @NotBlank(message = "版本号不能为空")
    private String Version;
    @NotBlank(message = "渠道号不能为空")
    private String ChannelCode;
    @NotBlank(message = "用户编号不能为空")
    private String FfpId;
    @NotBlank(message = "用户卡号不能为空")
    private String FfpCardNo;
    private String UserNo;
    private String ChannelOrderNo;
    @NotBlank(message = "证件号不能为空")
    private String IdNbr;
    private List<SegmentInfo> SegmentInfoList;
    private List<BookProductInfo> Products;
    @Min(value = 0, message = "订单总价不能小于0")
    private BigDecimal TotalPrice;
    @Min(value = 0, message = "支付金额不能小于0")
    private BigDecimal PayAmount;
    private Integer UseScore;//积分支付金额
    @NotBlank(message = "请输入联系人姓名")
    private String Linker;
    @NotBlank(message = "请输入联系人手机")
    private String LinkerMobile;
    /**
     * 收件人姓名
     */
    private String Receiver;
    /**
     * 收件人手机号
     */
    private String ReceiverMobile;
    /**
     * 预订人IP地址
     */
    private String OrderRequestIp;
    /**
     * 默认为空  MailTravel-表示邮寄行程单服务,VISA签证,UpgradeUnlimited-无限升舱卡
     */
    private String BuyType;
    /**
     * 收件人地址
     */
    private String ReceiverAddress;
    /**
     * 收件人地址编码
     */
    private String PickupAddressCode;
    /**
     * 购买方式（飞机设备，陆地设备）
     * 飞机设备为true
     */
    private Boolean BuyCouponType;
    /** 使用起始日期 (使用起始日期（wifi/电话卡取件日期）)*/
    private String UseStartDate;
    /**使用结束日期(使用结束日期（wifi还件日期）)*/
    private String UseEndDate;
    /**
     * 取货方式
     */
    private String ShipWay;
    private String Province;
    private String City;
    //邀请码
    private String InvitationCode;


    public CouponProductBuyRequest(String version, String channelCode, String userNo) {
        this.Version = version;
        this.ChannelCode = channelCode;
        this.UserNo = userNo;

    }
}