package com.juneyaoair.oneorder.api.dsop.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.dsop.sdk.client.OpenClient;
import com.juneyaoair.dsop.sdk.request.CommonRequest;
import com.juneyaoair.dsop.sdk.response.CommonResponse;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.oneorder.api.common.HttpBaseServiceImpl;
import com.juneyaoair.oneorder.api.dsop.BigDataConfig;
import com.juneyaoair.oneorder.api.dsop.dto.FlightInfoDynamicDto;
import com.juneyaoair.oneorder.api.dsop.dto.PtDsopResponse;
import com.juneyaoair.oneorder.api.dsop.dto.PtFlightStatusReq;
import com.juneyaoair.oneorder.api.dsop.dto.TicketInfoQueryRequest;
import com.juneyaoair.oneorder.api.dsop.enums.DSOP_RESULT_ENUM;
import com.juneyaoair.oneorder.api.dsop.service.IDsopService;
import com.juneyaoair.oneorder.ticket.TicketInfoQueryResponse;
import com.juneyaoair.oneorder.tools.utils.EncoderHandler;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import com.juneyaoair.oneorder.tools.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class DsopServiceImpl extends HttpBaseServiceImpl implements IDsopService {

    @Resource
    public BigDataConfig bigDataConfig;

    @Override
    public PtDsopResponse<TicketInfoQueryResponse> getPtBIgDataResponse(TicketInfoQueryRequest req, Map<String, String> headMap) {
        return invokeHttpClient(req,
                bigDataConfig.getBIGDATA_API_URL() + bigDataConfig.BIGDATD_MEMBER_TICKET,
                new TypeToken<PtDsopResponse<TicketInfoQueryResponse>>() {
                }.getType(),
                headMap);
    }

    @Override
    public List<FlightInfoDynamicDto> searchFlightDynamicsInfo(PtFlightStatusReq ptFlightStatusReq, Map<String, String> headMap) {
        if (StringUtils.isNotBlank(ptFlightStatusReq.getDepartureCityCode())) {
            String focDepCityCode = bigDataConfig.getFocCityMapping().get(ptFlightStatusReq.getDepartureCityCode());
            if (StringUtils.isNotBlank(focDepCityCode)) {
                ptFlightStatusReq.setDepartureCityCode(focDepCityCode);
            }
        }
        if (StringUtils.isNotBlank(ptFlightStatusReq.getArrivalCityCode())) {
            String focArrCityCode = bigDataConfig.getFocCityMapping().get(ptFlightStatusReq.getArrivalCityCode());
            if (StringUtils.isNotBlank(focArrCityCode)) {
                ptFlightStatusReq.setArrivalCityCode(focArrCityCode);
            }
        }
        ptFlightStatusReq.setSignature(EncoderHandler.encode("MD5", bigDataConfig.getBIGDATA_CLIENTCODE() + (StringUtils.isBlank(ptFlightStatusReq.getFlightNo()) ? "" : ptFlightStatusReq.getFlightNo()) + bigDataConfig.getBIGDATA_CLIENT_PWD()));
        // 创建请求对象
        String serviceName = "traveller.flight.dynamics.info";
        CommonRequest request = new CommonRequest(serviceName, "1.0");
        OpenClient client = new OpenClient(bigDataConfig.getDsopUrl(), bigDataConfig.getDataAppId(), bigDataConfig.getPrivateKeyIsv(), "");
        request.setBizContent(JSON.toJSONString(ptFlightStatusReq));
        log.info("服务名:{},请求参数:{}", serviceName, JacksonUtil.objectToJson(ptFlightStatusReq));
        // 发送请求
        CommonResponse response = client.execute(request);
        log.info("服务名:{},响应参数参数:{}", serviceName, JacksonUtil.objectToJson(response));
        if (response.isSuccess()) {
            Type type = new TypeToken<PtDsopResponse<FlightInfoDynamicDto>>() {
            }.getType();
            PtDsopResponse<FlightInfoDynamicDto> ptDsopResponse = HoAirGsonUtil.fromJson(response.getBody(), type);
            if (ptDsopResponse == null) {
                throw new ServiceException("航班动态数据转换异常");
            }
            if (!DSOP_RESULT_ENUM.S000000.getCode().equals(ptDsopResponse.getCode())) {
                throw ServiceException.fail(ptDsopResponse.getMsg());
            }
            return ptDsopResponse.getData();
        } else {
            throw new ServiceException(response.getMsg());
        }
    }
}
