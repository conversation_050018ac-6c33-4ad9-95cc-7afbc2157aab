package com.juneyaoair.oneorder.api.order.dto.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "预定客票信息")
public class BookedTicketDto {
    /*018-*/
    @ApiModelProperty(value = "客票号")
    private String ticketNo;

    @ApiModelProperty(value = "乘机人联系方式")
    private String phoneNo;

    /*PVG*/
    @ApiModelProperty(value = "起始机场")
    private String depAirport;

    /*SHA*/
    @ApiModelProperty(value = "到达机场")
    private String arrAirport;

    /*A-X-J-Y*/
    @ApiModelProperty(value = "仓位")
//    @NotEmpty(message = "预定客票信息-舱位为空")
    private String cabin;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "乘客姓名")
    private String passengerName;

    /*ADT*/
    @ApiModelProperty(value = "乘客类型")
    @JsonProperty(value = "PassengerType")
    private String passengerType;

    /*D-国内 I-国际*/
    @ApiModelProperty(value = "航线类型")
    private String airlineType;

    /*OW-单车 RT-来回程*/
    @ApiModelProperty(value = "航程类型")
    private String routeType;

    @ApiModelProperty(value = "证件号")
    private String certNo;

    @ApiModelProperty(value = "优惠券来源")
    private String couponSource;

    @ApiModelProperty(value = "优惠券代码")
    private String couponCode;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "产品编号")
    private String productNum;

    @ApiModelProperty(value = "销售金额")
    private Integer saleAmount;

    @ApiModelProperty(value = "数量")
    private Integer amount;

    @ApiModelProperty(value = "使用积分")
    private String useScore;

    @ApiModelProperty(value = "座位偏好")
    private String seatPreference;

    @ApiModelProperty(value = "是否有托运行李")
    private String hasCheckedBaggage;

    @ApiModelProperty(value = "到达交通方式")
    private String arrivalTransport;

    @ApiModelProperty(value = "到达时间")
    private String arrivalTime;
}