package com.juneyaoair.oneorder.api.passager.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 免打扰参数
 * <AUTHOR>
 */
@Data
public class NotDisturbParam {

    @ApiModelProperty(value = "会员id(memberId和memberNo传入一个即可)")
    private String memberId;

    @ApiModelProperty(value = "会员卡号")
    private String memberNo;

    @ApiModelProperty(value = "免打扰天数")
    private int setDays;

    @ApiModelProperty(value = "免打扰设置类型(1-人工电话邀约，2-对客端邀约，3-人工外呼场景，4-贵宾厅标记，5-客舱标记，6-系统判定)")
    private String setType;

    @ApiModelProperty(value = "来源系统")
    private String systemSource;

}
