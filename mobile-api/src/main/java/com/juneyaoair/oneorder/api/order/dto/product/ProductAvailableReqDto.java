package com.juneyaoair.oneorder.api.order.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;

@Data
public class ProductAvailableReqDto {

    @ApiModelProperty(value="活动号")
    private List<String> activityNo;

    @NotBlank(message="产品类型不能为空")
    @ApiModelProperty(value = "产品类型")
    private String productType;
}
