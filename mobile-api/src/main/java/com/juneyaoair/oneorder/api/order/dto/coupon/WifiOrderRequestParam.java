package com.juneyaoair.oneorder.api.order.dto.coupon;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class WifiOrderRequestParam {
    private String FlightNum;
    private String FlightDate;
    private String OriginIata;
    private String DestinationIata;
    private String CertNo;
    private String EtrDate;
    private String MdfDate;
    private String OrderStat;
    private String SaleType;
    private BigDecimal SaleNum;
    private BigDecimal SaleUse;
    private String MemberCardNo;
    private String MemberName;
    private String FftId;
    private String PassengerNm;
    private String TktNo;
    private String PhoneNo;
    private String PlanFlightTime;
    private String Remark;
    private String Cabin;
}
