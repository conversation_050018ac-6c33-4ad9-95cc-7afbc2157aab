package com.juneyaoair.oneorder.api.order.service;

import com.juneyaoair.oneorder.api.order.dto.product.ProductAvailableReqDto;
import com.juneyaoair.oneorder.api.order.dto.product.ProductAvailableResDto;
import com.juneyaoair.oneorder.api.order.dto.product.ProductOrderBookReqDto;
import com.juneyaoair.oneorder.api.order.dto.product.ProductOrderBookResDto;
import com.juneyaoair.oneorder.api.order.dto.product.ProductPurchaseQualificationReqDto;
import com.juneyaoair.oneorder.api.order.dto.product.ProductPurchaseQualificationResDto;
import com.juneyaoair.oneorder.restresult.response.RequestData;


/**
 * 产品平台3.0服务
 */
public interface Product3HttpService {


    ProductAvailableResDto productQueryAvailable(RequestData<ProductAvailableReqDto> request);

    ProductOrderBookResDto productOrderBook(RequestData<ProductOrderBookReqDto> request);

    ProductPurchaseQualificationResDto purchaseQualificationInquiry(RequestData<ProductPurchaseQualificationReqDto> request);
}
