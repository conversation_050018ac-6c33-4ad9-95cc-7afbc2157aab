package com.juneyaoair.oneorder.api.crm.constant;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description  资源分类枚举
 * @date 2019/2/28  17:27.
 */
public enum VoucherTypesEnum {
    LOUNGE("Lounge","贵宾休息室"),
    CHECKINSUBSTITUTION("CheckinSubstitution","预留登机牌"),
    DELIVERY("Delivery","文件速递"),
    BAGGAGE("Baggage","逾重行李额"),
    UPGRADE("Upgrade","升舱"),
    UPGRADEUNLIMITED("UpgradeUnlimited","无限升舱卡"),
    CHILD_UNLIMITED_FLY("ChildUnlimitedFly","儿童畅飞卡"),
    ADT_UNLIMITED_FLY("AdultUnlimitedFly","吉祥畅飞卡"),
    UNLIMITED_FLY_V2("UnlimitedFly","吉祥畅飞卡-2021畅游版"),
    UNLIMITED_FLY_V2_SF("UnlimitedFlySF","吉祥畅飞卡-2021春运版"),
    PACKAGE("Package","固包"),
    ONBOARDWIFI("OnboardWifi","wifi"),
    SEAT("Seat","值机选座"),
    PAY_SEAT("PaySeat","优选座位"),
    TRAFFIC("Traffic","接送机"),
    MAILTRAVEL("MailTravel","邮寄行程单"),
    WIFI("WIFI","随身WIFI"),
    PHONECARD("PhoneCard","电话卡"),
    VISA("VISA","签证"),
    GATEWAYUPGRADE("GatewayUpgrade","登机口升舱"),
    RESCHEDULECOUPON("RescheduleCoupon","改期券"),
    COUPON("Coupon","优惠券"),
    COUPONBAG("CouponBag","优惠券礼包"),
    GUIDESERVICE("GuideService", "礼宾接机"),
    COUPONPRODUCT("CouponProduct", "产品券"),
    ONBOARDPRODUCT("OnboardProduct", "机上购物"),
    BRANDMEALS("BrandMeals", "品牌餐食"),
    AIRPLANE_UPGRADE("AirplaneUpgrade", "机上升舱"),
    MemberLevelDelay("MemberLevelDelay", "吉祥航空等你回来一起飞"),
    UnlimitUpgradeYear("UnlimitUpgradeYear", "升舱卡2.0全年版"),
    UnlimitUpgradeHalf("UnlimitUpgradeHalf", "升舱卡2.0半年版"),
    EasyLiveHotel("EasyLiveHotel", "华住酒店"),
    RESCHEDULE("Reschedule","改期券"), //新产品平台
    UPGRADECOUPON("UpgradeCoupon","升舱券"), //新产品平台
    LOUNGECOUPON("LoungeCoupon","贵宾休息室"), //新产品平台
    BAGGAGECOUPON("BaggageCoupon","逾重行李券"), //新产品平台
    EXTRABAGGAGE("ExtraBaggage","预付费行李"), //新产品平台
    THEMECOUPON("ThemeCoupon","主题卡")
    ;
    private String code;
    private String name;

    VoucherTypesEnum(String code, String name){
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static List<String> getAllVoucherTypeCodes() {
        VoucherTypesEnum[] values = VoucherTypesEnum.values();
        List<String> results = new ArrayList<>();
        for (VoucherTypesEnum value : values) {
            results.add(value.code);
        }
        return results;
    }

    /**
     * 查询时展示的权益类型
     * @return
     */
    public static List<String> getShowVoucherTypeCodes(){
        List<String> voucherTypes = getAllVoucherTypeCodes();
        voucherTypes.remove(VoucherTypesEnum.EasyLiveHotel.getCode());
        voucherTypes.remove(VoucherTypesEnum.ONBOARDWIFI.getCode());
        return voucherTypes;
    }

    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static VoucherTypesEnum getTypeByCode(String code) {
        VoucherTypesEnum[] values = VoucherTypesEnum.values();
        for (VoucherTypesEnum value : values) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static boolean contains(String name){
        return Stream.of(VoucherTypesEnum.values()).anyMatch(t->t.getCode().equals(name));
    }

}
