package com.juneyaoair.oneorder.api.crm.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/16 10:36
 */
@Data
@Configuration
public class CrmConfig {
    public static final String DEFAULT_TOKEN = "www.juneyaoair.com";
    /**
     * .net版本
     */
    @Value("${crm.login.url:}")
    private String crmLoginUrl;
    /**
     * .net版本
     */
    @Value("${crm.member.url:}")
    private String crmMemberUrl;

    /**
     * .java版本 会员积分里程
     * HOCRM-SERVICE-MILEAGE
     */
    @Value("${crmMileage.openapi.url:}")
    private String crmMileageOpenApiUrl;
    /**
     * .java版本 会员账户信息
     * HOCRM-SERVICE-MEMBER 8013
     */
    @Value("${crmMember.openapi.url:}")
    private String crmMemberOpenApiUrl;
    /**
     * .java版本 会员账户信息
     * HOCRM-SERVICE-ACCOUNT 8014
     */
    @Value("${crmAccount.openapi.url:}")
    private String crmAccountOpenApiUrl;

    @Value("${crm.func.url:}")
    private String crmFuncUrl;

    @Value("${flight.basic.url:}")
    private String flightBasicUrl;
    /**
     * crm bff地址
     */
    @Value("${crm.bff.url:}")
    private String crmBffUrl;
}
