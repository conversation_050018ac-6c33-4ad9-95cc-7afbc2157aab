package com.juneyaoair.oneorder.api.order.dto.flightproof;

import lombok.Data;
import lombok.NoArgsConstructor;
@Data
@NoArgsConstructor
public class SegmentInfo {
    /**
     * 航班编号
     */
    public String FlightNo;
    /**
     * 计划起飞时间 hh:mm
     * 当地时间
     */
    public String DepTime;
    /**
     * 计划起飞时间 hh:mm
     * 当地时间
     */
    public String DepSpecificTime;
    /**
     * 计划到达时间
     * 当地时间
     */
    public String ArrTime;
    /**
     * 计划到达时间 hh：mm
     * 当地时间
     */
    public String ArrSpecificTime;
    /**
     * 实际起飞时间
     * 当地时间
     */
    public String ActDepTime;
    /**
     * 实际到达时间
     * 当地时间
     */
    public String ActArrTime;
    /**
     * 航班状态 N:正常   C:取消  ，D：延误
     */
    public String SegmentStatus;
    /**
     * 航班状态原因原因
     */
    public String StatusReason;
    /**
     * 到达航站楼
     */
    public String ArrAirportTerminal;
    /**
     * 起飞航站楼
     */
    public String DepAirportTerminal;
    /**
     * 到达机场代码
     */
    public String ArrAirportCode;
    /**
     * 起飞机场代码
     */
    public String DepAirportCode;
    /**
     * 到达机场中文起飞机场中文
     */
    public String ArrAirportName;
    /**
     *出发机场中文起飞机场中文
     */
    public String DepAirportName;
    /**
     * 飞行时长
     */
    public long FlightTime;
    /**
     * 跨天数
     */
    public int day;
    /**
     * 客票状态
     */
    public String TicketStatus;

    /**
     * 离港延误标记 2020-10-30
     */
    public Boolean DepDelay;
    /*
     * 到港延误标记 2020-10-30
     */
    public Boolean ArrDelay;

    /**
     * 出发城市时区 2020-11-03
     */
    public Integer DepCityZone;
    /**
     * 到达城市时区 2020-11-03
     */
    public Integer ArrCityZone;
    /**
     * 改签之前的航段 2021-03-29
     */
    public SegmentInfoDto BeforeChangeSegment;


    /**
     * 滑入时间
     */
    public String FlightInn;

    /**
     * 滑出时间
     */
    public String FlightOut;
}