package com.juneyaoair.oneorder.api.order.dto.flightproof;

import com.juneyaoair.oneorder.order.dto.IdentityInfo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class IBETicketInfo {
    /**
     * 旅客姓名
     */
    public String PassengerName;
    /**
     * 航班日期
     */
    public String FlightDate;
    /**
     * 航班日期yyyy-MM-dd 周几
     */
    public String FlightDateFormat;
    /**
     * 终点城市
     */
    public String DstCity;
    /**
     * 终点英文
     */
    public String DstEnCity;
    /**
     * 起点城市
     */
    public String OrgCity;
    /**
     * 起点英文
     */
    public String OrgEnCity;
    /**
     *航班信息
     */
    public List<SegmentInfo> SegmentInfoList;

    /**
     * 证件信息
     */
    public List<IdentityInfo> IdentityInfoList;
    /**
     * 票号
     */
    public String TicketNo;


}
