package com.juneyaoair.oneorder.api.crm.constant;

/**
 * @ClassName FlihtTypeEnum
 * @Description
 * <AUTHOR>
 * @Date 2023/7/3 8:57
 * @Version 1.0
 */
public enum TripTypeEnum {

    TRIP_TYPE_D("D", "国内"),
    TRIP_TYPE_I("I", "国际"),
    TRIP_TYPE_R("R", "港澳台");


    private final String code;
    private final String desc;

    TripTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
