package com.juneyaoair.oneorder.api.order.dto.ticket;

import com.juneyaoair.oneorder.order.dto.TaxInfo;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class PtIBETicketInfo {
    private boolean IsHasMoreTax; //是否有更多税  需要DETR:X提取的税项
    private boolean IsIT; //是否IT票
    private String EqviuCurrencyType;//等值支付货币
    private String EqviuFare;//等值支付金额
    private String ETicketType;//电子客票类型r
    private String CurrencyType;//货币类型
    private String DstCity;//终点城市
    private String ExchangeInfo;//改签信息
    private Double Fare;//票价
    private String FareCompute;//票价计算信息
    private String FollowTicketNo;//后续票号
    private String ISI;//ISI信息
    private String IssueAirline;//出票航空公司
    private String OrgCity;//始发城市
    private String OriginalIssue;//OI信息
    private String PassengerName;//旅客姓名
    private String PayMethod;//支付方式
    private String SigningInfo;//签注信息
    private Double CN;//机场建设税
    private Double YQ;//燃油费
    private Double OB;//改期
    private Double Tax;//税款金额
    private String TicketNo;//票号
    private Double TotalAmount;//客票总金额
    private String TourCode;//旅游代码
    private String CurrencyTypeTotal;//票面总价的货币类型
    private String IsReceiptPrinted;//是否已打印T4（发票）联
    private String Remark;//原始主机返回信息
    private String InfantBirthday;//无人陪伴儿童年龄 yyyy-MM-dd
    private String PassengerType;//旅客类型
    private String UnaccompaniedChildAge;//无人陪伴儿童年龄
    private String IataNo;//出票的Iata号
    private String IssueDate;//旅游代码出票时间
    private String PassengerID;
    private List<PtSegmentInfo> SegmentInfoList;//航段信息列表
    private List<IdentityInfo> IdentityInfoList;//证件信息列表
    private List<TaxInfo> TaxInfoList;//税费信息列表
    private String InterFlag;//国际国内航班
    private String IsRefundValid;//是否可退票
    private String UnValidReson;//不可退原因
    private String IsFromDomestic;//是否国内出发
    private String ResultCode;//结果代码
    private String ErrorInfo;//错误信息
    private String TicketNoInf;// 成人携带的婴儿卡号
    private String EticketMold; //客票类型 团队：Group,散客：Person  20201217
    private boolean IsSpecialService; //是否申请特殊服务  申请了特殊服务：true，没有申请特殊服务：false，默认false  20201217
    private boolean IsAllDeduction; //是否全积分抵扣  全积分抵扣：true  20201217

    /**
     * 婴儿携带人姓名
     */
    @ApiModelProperty(value = "婴儿携带人姓名")
    private String AdtNameToInf;

    /**
     * 婴儿携带人票号
     */
    @ApiModelProperty(value = "婴儿携带人票号")
    private String AdtNameToInfTicketNo;

    //儿童生日
    private String PassengerBirthday;

    @ApiModelProperty(value = "是否允许改期,true-代表允许改期 false-不允许改期")
    private Boolean IsAllowChange;

    public boolean isHasMoreTax() {
        return IsHasMoreTax;
    }

    public void setHasMoreTax(boolean hasMoreTax) {
        IsHasMoreTax = hasMoreTax;
    }

    public boolean isIT() {
        return IsIT;
    }

    public void setIT(boolean IT) {
        IsIT = IT;
    }

    public String getEqviuCurrencyType() {
        return EqviuCurrencyType;
    }

    public void setEqviuCurrencyType(String eqviuCurrencyType) {
        EqviuCurrencyType = eqviuCurrencyType;
    }

    public String getEqviuFare() {
        return EqviuFare;
    }

    public void setEqviuFare(String eqviuFare) {
        EqviuFare = eqviuFare;
    }

    public String getETicketType() {
        return ETicketType;
    }

    public void setETicketType(String ETicketType) {
        this.ETicketType = ETicketType;
    }

    public String getCurrencyType() {
        return CurrencyType;
    }

    public void setCurrencyType(String currencyType) {
        CurrencyType = currencyType;
    }

    public String getDstCity() {
        return DstCity;
    }

    public void setDstCity(String dstCity) {
        DstCity = dstCity;
    }

    public String getExchangeInfo() {
        return ExchangeInfo;
    }

    public void setExchangeInfo(String exchangeInfo) {
        ExchangeInfo = exchangeInfo;
    }

    public Double getFare() {
        return Fare;
    }

    public void setFare(Double fare) {
        Fare = fare;
    }

    public String getFareCompute() {
        return FareCompute;
    }

    public void setFareCompute(String fareCompute) {
        FareCompute = fareCompute;
    }

    public String getFollowTicketNo() {
        return FollowTicketNo;
    }

    public void setFollowTicketNo(String followTicketNo) {
        FollowTicketNo = followTicketNo;
    }

    public String getISI() {
        return ISI;
    }

    public void setISI(String ISI) {
        this.ISI = ISI;
    }

    public String getIssueAirline() {
        return IssueAirline;
    }

    public void setIssueAirline(String issueAirline) {
        IssueAirline = issueAirline;
    }

    public String getOrgCity() {
        return OrgCity;
    }

    public void setOrgCity(String orgCity) {
        OrgCity = orgCity;
    }

    public String getOriginalIssue() {
        return OriginalIssue;
    }

    public void setOriginalIssue(String originalIssue) {
        OriginalIssue = originalIssue;
    }

    public String getPassengerName() {
        return PassengerName;
    }

    public void setPassengerName(String passengerName) {
        PassengerName = passengerName;
    }

    public String getPayMethod() {
        return PayMethod;
    }

    public void setPayMethod(String payMethod) {
        PayMethod = payMethod;
    }

    public String getSigningInfo() {
        return SigningInfo;
    }

    public void setSigningInfo(String signingInfo) {
        SigningInfo = signingInfo;
    }

    public Double getTax() {
        return Tax;
    }

    public void setTax(Double tax) {
        Tax = tax;
    }

    public String getTicketNo() {
        return TicketNo;
    }

    public void setTicketNo(String ticketNo) {
        TicketNo = ticketNo;
    }

    public Double getTotalAmount() {
        return TotalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        TotalAmount = totalAmount;
    }

    public String getTourCode() {
        return TourCode;
    }

    public void setTourCode(String tourCode) {
        TourCode = tourCode;
    }

    public String getCurrencyTypeTotal() {
        return CurrencyTypeTotal;
    }

    public void setCurrencyTypeTotal(String currencyTypeTotal) {
        CurrencyTypeTotal = currencyTypeTotal;
    }

    public String getIsReceiptPrinted() {
        return IsReceiptPrinted;
    }

    public void setIsReceiptPrinted(String isReceiptPrinted) {
        IsReceiptPrinted = isReceiptPrinted;
    }

    public String getRemark() {
        return Remark;
    }

    public void setRemark(String remark) {
        Remark = remark;
    }

    public String getInfantBirthday() {
        return InfantBirthday;
    }

    public void setInfantBirthday(String infantBirthday) {
        InfantBirthday = infantBirthday;
    }

    public String getPassengerType() {
        return PassengerType;
    }

    public void setPassengerType(String passengerType) {
        PassengerType = passengerType;
    }

    public String getUnaccompaniedChildAge() {
        return UnaccompaniedChildAge;
    }

    public void setUnaccompaniedChildAge(String unaccompaniedChildAge) {
        UnaccompaniedChildAge = unaccompaniedChildAge;
    }

    public String getIataNo() {
        return IataNo;
    }

    public void setIataNo(String iataNo) {
        IataNo = iataNo;
    }

    public String getIssueDate() {
        return IssueDate;
    }

    public void setIssueDate(String issueDate) {
        IssueDate = issueDate;
    }

    public String getPassengerID() {
        return PassengerID;
    }

    public void setPassengerID(String passengerID) {
        PassengerID = passengerID;
    }

    public List<PtSegmentInfo> getSegmentInfoList() {
        return SegmentInfoList;
    }

    public void setSegmentInfoList(List<PtSegmentInfo> segmentInfoList) {
        SegmentInfoList = segmentInfoList;
    }

    public List<IdentityInfo> getIdentityInfoList() {
        return IdentityInfoList;
    }

    public void setIdentityInfoList(List<IdentityInfo> identityInfoList) {
        IdentityInfoList = identityInfoList;
    }

    public List<TaxInfo> getTaxInfoList() {
        return TaxInfoList;
    }

    public void setTaxInfoList(List<TaxInfo> taxInfoList) {
        TaxInfoList = taxInfoList;
    }

    public String getInterFlag() {
        return InterFlag;
    }

    public void setInterFlag(String interFlag) {
        InterFlag = interFlag;
    }

    public String getIsRefundValid() {
        return IsRefundValid;
    }

    public void setIsRefundValid(String isRefundValid) {
        IsRefundValid = isRefundValid;
    }

    public String getUnValidReson() {
        return UnValidReson;
    }

    public void setUnValidReson(String unValidReson) {
        UnValidReson = unValidReson;
    }

    public String getIsFromDomestic() {
        return IsFromDomestic;
    }

    public void setIsFromDomestic(String isFromDomestic) {
        IsFromDomestic = isFromDomestic;
    }

    public String getResultCode() {
        return ResultCode;
    }

    public void setResultCode(String resultCode) {
        ResultCode = resultCode;
    }

    public String getErrorInfo() {
        return ErrorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        ErrorInfo = errorInfo;
    }

    public Double getCN() {
        return CN;
    }

    public void setCN(Double CN) {
        this.CN = CN;
    }

    public Double getYQ() {
        return YQ;
    }

    public void setYQ(Double YQ) {
        this.YQ = YQ;
    }

    public Double getOB() {
        return OB;
    }

    public void setOB(Double OB) {
        this.OB = OB;
    }

    public String getTicketNoInf() {
        return TicketNoInf;
    }

    public void setTicketNoInf(String ticketNoInf) {
        TicketNoInf = ticketNoInf;
    }

    public String getEticketMold() {
        return EticketMold;
    }

    public void setEticketMold(String eticketMold) {
        EticketMold = eticketMold;
    }

    public boolean isSpecialService() {
        return IsSpecialService;
    }

    public void setSpecialService(boolean specialService) {
        IsSpecialService = specialService;
    }

    public boolean isAllDeduction() {
        return IsAllDeduction;
    }

    public void setAllDeduction(boolean allDeduction) {
        IsAllDeduction = allDeduction;
    }

    public String getAdtNameToInf() {
        return AdtNameToInf;
    }

    public void setAdtNameToInf(String adtNameToInf) {
        AdtNameToInf = adtNameToInf;
    }

    public String getAdtNameToInfTicketNo() {
        return AdtNameToInfTicketNo;
    }

    public void setAdtNameToInfTicketNo(String adtNameToInfTicketNo) {
        AdtNameToInfTicketNo = adtNameToInfTicketNo;
    }

    public String getPassengerBirthday() {
        return PassengerBirthday;
    }

    public void setPassengerBirthday(String passengerBirthday) {
        PassengerBirthday = passengerBirthday;
    }

    public Boolean getAllowChange() {
        return IsAllowChange;
    }

    public void setAllowChange(Boolean allowChange) {
        IsAllowChange = allowChange;
    }
}
