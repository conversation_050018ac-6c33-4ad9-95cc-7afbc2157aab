package com.juneyaoair.oneorder.api.order.dto.product;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "产品活动")
public class ProductActivityDto {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "产品活动ID")
    private String productId;

    @ApiModelProperty(value = "资源标签;多个标签以|分隔")
    private String showTags;

    @ApiModelProperty(value = "产品类型")
    private String productType;

    @ApiModelProperty(value = "活动号")
    private String activityNo;


    @ApiModelProperty(value = "活动名称")
    private String activityName;


    @ApiModelProperty(value = "活动标题")
    private String activityTitle;

    @ApiModelProperty(value = "价格类型;T抵用券/D折扣券")
    private String salePriceType;

    @ApiModelProperty(value = "币种;CNY人民币/USD美元/JPY日元/EUR欧元/KRW韩元/THB泰铢")
    private String currencyCode;


    @ApiModelProperty(value = "原始销售价格")
    private BigDecimal originalPrice;


    @ApiModelProperty(value = "产品销售价格")
    private BigDecimal priceOrDiscount;


    @ApiModelProperty(value = "销售总量")
    private Integer saleAmountTotal;

    //限制总量

    @ApiModelProperty(value = "销售剩余量")
    private Integer saleAmountAvilable;


    @ApiModelProperty(value = "销售单人最大购买量")
    private Integer maxAmountPer;


    @ApiModelProperty(value = "活动开始日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date beginDatetime;

    @ApiModelProperty(value = "活动结束日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date endDatetime;

    @ApiModelProperty(value = "活动有效期是否有效 Y 有效 N 无效")
    private String effectiveStatus;

    @ApiModelProperty(value = "活动自定义属性")
    List<ProductFieldDto> productFieldList;


}
