package com.juneyaoair.oneorder.api.order.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * @ClassName OrderConfig
 * @Description
 * <AUTHOR>
 * @Date 2023/6/27 14:19
 * @Version 1.0
 */
@Data
@Configuration
public class OrderConfig {
    /**
     * Order服务
     * 已经配apollo 例http://***********:9031/Refund
     */
    @Value("${hoService.refund.url:}")
    public String URL_FARE_API_REFUND;
    /**
     * Order服务
     * 已经配apollo 例http://***********:9031/Order
     */
    @Value("${hoService.order.url:}")
    public String URL_FARE_API_ORDER;
    /**
     * 活动服务
     * 已经配apollo 例http://***********:9031/Activity
     */
    @Value("${hoService.activity.url:}")
    public String URL_FARE_API_ACTIVITY;

    /**
     * Coupon服务
     * 已经配apollo 例http://***********:9031/Coupon
     */
    @Value("${hoService.coupon.url:}")
    public String URL_FARE_API_COUPON;
    /**
     * 产品平台服务
     * http://openapi-test.juneyaoair.com:9031/Pdm
     */
    @Value("${hoService.pdm.url:}")
    public String URL_FARE_API_PDM;

    /**
     * 例http://***********:9031/Book
     */
    @Value("${hoService.book.url:}")
    public String URL_FARE_API_BOOK;


    @Value("${hoService.product.url:}")
    public String URL_FARE_API_PRODUCT;

    /**
     * 主题卡类型
     */
    @Value("${themeCouponList:[ThemeHotPotCoupon,ThemeOutHikeCoupon,ThemeVermicelliCoupon]}")
    public List<String> themeCouponList;
    /**
     * 检查客票信息
     */
    public  final String TICKET_INFO = "/TicketFlightCheck";

    public  final String GET_TICKETINFO_BYTKTNO = "/GetTicketInfoByTktNo"; // 获取客票信息

    public  final String COUPON_BUY_PRODUCT_V2 = "/BuyCouponProduct"; //可售产品购买

    //表扬,投诉,意见新增接口
    public final String CREATE_SUGGESTION = "/CreateFeedback";

    public final String SUB_QUERY_COUPON = "/MyCouponQuery";/// 优惠券查询(ALL可用)

    public final String COUPON_MY_PRODUCT_V2 = "/GetMyCouponProduct";//我的权益券查看


    /**
     * product3.0 购买资格查询
     */
    public static final String PRODUCT_3_QUERY_QUALIFICATION = "/purchaseQualificationInquiry";

    /**
     * product3.0 查询可售产品
     */
    public static final String PRODUCT_3_QUERY_AVAILABLE = "/productQueryAvailable";

    /**
     * product3.0 产品下单
     */
    public static final String PRODUCT_3_BOOK = "/productOrderBook";

}
