package com.juneyaoair.oneorder.api.crm.service.impl;

import com.juneyaoair.flightbasic.utils.MdcUtils;
import com.juneyaoair.message.bean.auth.AuthResult;
import com.juneyaoair.message.client.MessageClient;
import com.juneyaoair.oneorder.config.SmsConfig;
import com.juneyaoair.oneorder.mobile.config.RedisConstantConfig;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class MessageClientImpl extends MessageClient {

    @Autowired
    private SmsConfig smsConfig;

    @Resource
    private RedisUtil redisUtil;

    @Override
    protected String getPushUrl() {
        return smsConfig.getMessagePlatformPush();
    }

    @Override
    protected String getChannelCode() {
        return smsConfig.getMessagePlatformChannelCode();
    }

    @Override
    protected String getChannelSecret() {
        return smsConfig.getMessagePlatformChannelSecret();
    }

    @Override
    protected String getSmsAisleNumber(String aisleType) {
        // 暂不区分 通道使用同一类型
        return smsConfig.getMessagePlatformSmsAisleNumber();
    }

    /**
     * 在消息发送和查询APP消息分类时使用
     * @param aisleType
     * @return
     */
    @Override
    public String getAppAisleNumber(String aisleType) {
        // 暂不区分 通道使用同一类型
        return smsConfig.getMessagePlatformAppAisleNumber();
    }

    @Override
    protected String getToken() {
        final String redisKey = RedisConstantConfig.MESSAGE_PLATFORM_TOKEN + smsConfig.getMessagePlatformChannelCode();
        String token = (String) redisUtil.get(redisKey);
        if (StringUtils.isNotBlank(token)) {
            return token;
        }
        AuthResult authResult = getToken(MdcUtils.getRequestId());
        token = authResult.getToken();
        redisUtil.set(redisKey, token, authResult.getExpiresIn() - 5 * 60L);
        return token;
    }

    @Override
    protected void notLogin() {
        final String redisKey = RedisConstantConfig.MESSAGE_PLATFORM_TOKEN + smsConfig.getMessagePlatformChannelCode();
        redisUtil.del(redisKey);
    }
}
