package com.juneyaoair.oneorder.api.order.dto.coupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: caolei
 * @Description: 客票信息
 * @Date: 2025/01/01 13:52
 * @Modified by:
 */
@Data
public class BuyInsuranceInfo {

    @ApiModelProperty(value = "票号")
    private String TicketNo;

    @ApiModelProperty(value = "姓名")
    private String PassengerName;

    @ApiModelProperty(value = "是否已购买航意险")
    private String IsBuy;

}
