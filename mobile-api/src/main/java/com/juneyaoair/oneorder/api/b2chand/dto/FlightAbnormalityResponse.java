package com.juneyaoair.oneorder.api.b2chand.dto;

import com.juneyaoair.oneorder.api.b2chand.FlightAbnormality;
import lombok.Data;

@Data
public class FlightAbnormalityResponse {
    public String Version;
    public String ChannelCode;
    public String UserNo;
    public String CountryList;
    public String ResultCode;
    public String ErrorInfo;
    public int Id;
    public String Name;
    public String IdNumber;
    public String TicketNo;
    public String CodeNumber;
    public String FlightAbnormalityId;
    public String FlightNo;
    public String FlightDate;
    public FlightAbnormality FlightAbnormality;
}
