package com.juneyaoair.oneorder.api.basic.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.request.activity.MemberPrivateDomainParam;
import com.juneyaoair.flightbasic.response.MemberPrivateDomainResult;
import com.juneyaoair.flightbasic.utils.BaseRequestUtil;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.api.crm.config.CrmConfig;
import com.juneyaoair.oneorder.api.crm.constant.CrmUrlConstant;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.api.basic.service.FlightBasicActivityService;
import com.juneyaoair.oneorder.tools.utils.HttpResult;
import com.juneyaoair.oneorder.tools.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 基础服务活动
 * <AUTHOR>
 */
@Slf4j
@Service
public class FlightBasicActivityServiceImpl implements FlightBasicActivityService {

    @Autowired
    private CrmConfig crmConfig;

    @Override
    public MemberPrivateDomainResult getMemberPrivateDomain(RequestDataDto requestData, MemberPrivateDomainParam memberPrivateDomainParam) {
        BaseRequestDTO<MemberPrivateDomainParam> flightBasicRequest = BaseRequestUtil.createRequest(memberPrivateDomainParam, requestData.getChannelNo());
        String url = crmConfig.getFlightBasicUrl() + CrmUrlConstant.GET_MEMBER_PRIVATE_DOMAIN;
        HttpResult httpResult = HttpUtil.doPostClient(flightBasicRequest, url);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR);
        }
       TypeReference<BaseResultDTO<MemberPrivateDomainResult>> typeReference = new TypeReference<BaseResultDTO<MemberPrivateDomainResult>>() {
       };
        BaseResultDTO<MemberPrivateDomainResult> baseResult = JSON.parseObject(httpResult.getResponse(), typeReference);
        if (!WSEnum.SUCCESS.resultCode.equals(baseResult.getResultCode())) {
            throw new MultiLangServiceException(baseResult.getErrorMsg());
        }
        return baseResult.getResult();
    }

}
