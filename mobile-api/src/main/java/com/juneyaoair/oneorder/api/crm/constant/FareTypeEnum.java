package com.juneyaoair.oneorder.api.crm.constant;

/**
 * <AUTHOR>
 * @description 运价类型 Simple-普通类型 SPA-协议运价 Addon-联程运价。新增对接国际中转需要，国内和之前接口不需要
 * @date 2019/7/10  18:42.
 */
public enum FareTypeEnum {
    SIMPLE("Simple","普通类型"),
    SPA("SPA","协议运价"),
    ADDON("Addon","联程运价"),
    ONEWAY("OneWay","国内组合运价");
    private String fare;
    private String desc;

    FareTypeEnum(String fare, String desc) {
        this.fare = fare;
        this.desc = desc;
    }

    public String getFare() {
        return fare;
    }

    public String getDesc() {
        return desc;
    }
}
