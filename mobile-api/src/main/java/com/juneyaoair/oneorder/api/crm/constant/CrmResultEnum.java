package com.juneyaoair.oneorder.api.crm.constant;

/**
 * Created by yaocf on 2022/9/14  18:48.
 * 会员返回结果枚举错误码
 */
public enum CrmResultEnum {
    /**
     * 会员1.0响应代码枚举
     */
    SUC000("000","成功"),
    EXCEPTION("9999","异常");

    private String resultCode;
    private String resultInfo;

    CrmResultEnum(String resultCode, String resultInfo) {
        this.resultCode = resultCode;
        this.resultInfo = resultInfo;
    }

    public String getResultCode() {
        return resultCode;
    }

    public String getResultInfo() {
        return resultInfo;
    }
}
