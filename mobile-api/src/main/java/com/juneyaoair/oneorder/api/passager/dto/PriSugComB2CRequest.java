package com.juneyaoair.oneorder.api.passager.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 调用 表扬,投诉,意见新增接口 请求参数
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PriSugComB2CRequest<T> {
    /**
     * 接口编码：
     *      申请：apply
     *      取消申请：cancelApply
     *      我的申请列表查询：queryAllApply
     *      进度查询：queryApply
     */
    private String inftCode;
    /**
     * 会员Id
     */
    private String memberId;

    /**
     * 业务参数
     */
    private T data;

}
