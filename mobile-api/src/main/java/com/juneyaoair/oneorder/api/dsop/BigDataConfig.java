package com.juneyaoair.oneorder.api.dsop;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
@Getter

@Configuration
public class BigDataConfig {
    @Value("${bigdata_api_url:http://old-marketing.juneyaoair.com/traveller-service-gateway/traveller}")
    private String BIGDATA_API_URL;
    @Value("${bigdata_client_code:CUXIAO}")
    private String BIGDATA_CLIENTCODE;
    @Value("${bigdata_client_pwd:CUXIAO2018}")
    private String BIGDATA_CLIENT_PWD;
    /**
     * 数据平台dsop地址
     */
    @Value("${dsopUrl:https://dsop.juneyaoair.com}")
    private String dsopUrl;
    /**
     * 数据平台APPID
     */
    @Value("${dataAppId:202304131096093572752474112}")
    private String dataAppId;
    /**
     * 数平台开发者私钥
     */
    @Value("${privateKeyIsv:MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCQ1nZ8EXoja3hfCOrr7aV1vVcgq+HpuWka/PkN9AcNuV8noGO96d6+5UCGlPGteJPKezQN6BBoPD/1RXbQFY0eOcdVKmUSuz/2xIdeCi1O30oBoUOY4iAO4+d+aFwwx6wo57uiWvJ8cdFT3yOtSUMj1asP7D6RTdDL64Kqkala5Ty8jOKcPiplHlM63k+VVHCvxzEx+f/EYoXUV0tRtGdMvUEn64D1FBy+Qo5BLFUoHAbC0acCNe71fWTYaYwkrkXCmhxjYBIaOIK1tuWAX/9Jjn+S1en37MyPqW70PuZSy4AZJsTqk9zWUPJ1haG2ksf4/i5bTYQEZzJh6KfHGMK3AgMBAAECggEAG9nBatYJgGoX+V5Dnh5SZLOF33httn26oj/mvDIicRcmHv6Ob2RbOqXmpHpbhpnbAWJzfgETtpdvTQCQei7KtfcyR0Qhr1pmUFM2HHUkhTgWiP2ze+Bvfa2BRCNZJuAeULdOs0d9vUQ9lj0gJSAjUu6xOBybY2FtNukQdTAROrLzx5gb2+RD4f1t9tNgfRKOzh4m3K9ayfoSg/zxxIXKR8UHWPwrqSM7QEB+IDQ4EyMjDncS3vKAFTQMHJC8PqdwYcWEGY6omFxQ7iwacfQl3NXwDUAxuVDgLLYNuew9+TRaf/s2LAwLRUOr8d4O5qBLPdsLVZuIW3251in+WziIMQKBgQDGA0O3Ihjt9B2sMSmPy7Y8BoLZSs6JHa8aPC9Bx1ybVHJi3mnKc6MoU4lFkYlyB3IN51gzB7ycgjGWT7GMK8cVJtzUF8efuHmS2aLwbNJjPcdfxV58UNFLwXXY4LGjcqNcAlprhdWSZpGEsSX7uHGELh6SbnJhDfpZOsg7X/jnwwKBgQC7QL5QFVf8UxLGEqhfFSpqw7y/Cqy2nS0iGKJ0sEIgHtKBC2VChoBtInNvmBCUE+HKq7u7R+yvnhNED029b2w6IacketS8ghFOJgAzCJyvuxf/W2B0FP7KBsBvrtWfZdvD9I3mbhVfFv783rNooTqeonujjNVT0V7N8Q25SbD9/QKBgHNcEYZOyiFPWD4qQiFxOBEOZgr75wnBDnOFXq6+2/fuodlx5EY7SnHDATVTjbSuG8d34U2Rk1PHEeaVNUYJdymeg4bY98bYrZNdUe0qKQQCSPZFwT9Bwj2VTvf0J+mmbv+6qzz7MqrhMKQSpKNbGF8+kzcSO3bJsvFrKp5U0mkLAoGAG4g1c0jwGuWMCRuNLg3KotAM3HoRUvhhPnYozMdtsZNWmCq4xMNJ9sHt/yZ4qY0S0d3WSH8WyeuQIZ2ubIAJhbKVBMQaq2zkZn0n4lkN0LjNmnoxAGBl5gaAHOuz9S/g1DsM8iiVc9nJvBxyfJvYJqL1UuLOKvjJ27XpsqinIXECgYEAhkd2vzLqXzDjC/eKY7v/bPbhGaFx30mpNimIe1Ipwxybs6vjIEbKkdS0OXsnd2BH2nRfnlzGAjhoVwcPFPlqLRjFjIKRR7lZBEF0sANdu090Q3BYJrRhufB8taPYnKHdh2I8PGqNQlWNwMeBXnp5K/RZhlJ8/HUYZ7btedg5gkw=}")
    private String privateKeyIsv;

    /**
     * 基础服务与foc城市关系映射
     * key：     基础服务城市三字码
     * value：   FOC城市三字码
     */
    @ApolloJsonValue("${dsop.foc.cityMapping:{}}")
    private Map<String, String> focCityMapping;

    /**
     * 旅客行程查询
     */
    public final String BIGDATD_MEMBER_TICKET = "/itinerary/info";
    /**
     * 航班动态查询
     */
    public final String SEARCH_FLIGHT_DYNAMIC_INFO_URL = "/searchFlightDynamicsInfo";
}
