package com.juneyaoair.oneorder.api.email;

import com.juneyaoair.oneorder.common.constant.BeanNameConstant;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.util.Properties;

/**
 * <AUTHOR>
 * @description 客票邮箱配置
 * @date 2024/10/30 8:46
 */
@Configuration
public class EmailBookConfig {
    @Value("${spring.mail.book.host:mail.juneyaoair.com}")
    private String host;
    @Value("${spring.mail.book.port:25}")
    private int port;
    @Value("${spring.mail.book.username:<EMAIL>}")
    private String username;
    @Value("${spring.mail.book.password:}")
    private String password;
    @Value("${spring.mail.book.from:<EMAIL>}")
    private String from;

    /**
     * @description 此方式暂时不可用，因为不知道密码
     **/
    @Bean(name = BeanNameConstant.BEAN_NAME_EMAIL_SENDER_BOOK)
    public JavaMailSender getJavaMailSenderBook() {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        mailSender.setHost(host);
        mailSender.setPort(port);
        mailSender.setUsername(username);
        mailSender.setPassword(password);
        return mailSender;
    }

    @Bean(name = BeanNameConstant.BEAN_NAME_EMAIL_CONFIG_BOOK)
    public EmailCommConfig initEmailCommConfig(){
        EmailCommConfig emailCommConfig = new EmailCommConfig();
        emailCommConfig.setHost(host);
        emailCommConfig.setPort(port);
        emailCommConfig.setUsername(username);
        emailCommConfig.setPassword(password);
        emailCommConfig.setFrom(from);
        return emailCommConfig;
    }
}
