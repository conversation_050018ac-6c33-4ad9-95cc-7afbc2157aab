package com.juneyaoair.oneorder.api.order.dto.activity;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * 接口报文返回dto基类，此类作为公用结构模板
 * 
 * <AUTHOR>
 * @date 2018年3月8日 下午4:53:06
 * @version 1.0
 */
@SuppressWarnings("serial")
public class BaseResultDTO<T> implements Serializable {

	@SerializedName(value =  "resultCode", alternate = {"ResultCode"})
	private String resultCode;// 返回编码，非1001为错误返回，错误原因查看msg
	@SerializedName(value =  "errorMsg", alternate = {"ErrorInfo"})
	private String errorMsg;// 返回结果描述，正确 ok
	@SerializedName(value =  "result", alternate = {"Result"})
	private T result; // 返回结果

	public T getResult() {
		return result;
	}

	public void setResult(T result) {
		this.result = result;
	}

	public void success() {
		this.resultCode = "1001";
		this.errorMsg = "ok";
	}

	public void fail(String code, String msg) {
		this.resultCode = code;
		this.errorMsg = msg;
	}

	public void fail(String msg) {
		fail("1002", msg);
	}

	public void fail() {
		fail("fail");
	}

	public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}

	public String getResultCode() {
		return resultCode;
	}

	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}
}
