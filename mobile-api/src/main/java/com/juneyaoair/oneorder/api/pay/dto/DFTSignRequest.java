package com.juneyaoair.oneorder.api.pay.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName DFTSignRequest
 * @Description 德付通身份验证/签约 请求体
 * <AUTHOR>
 * @Date 2024/5/27 10:23
 * @Version 1.0
 */

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DFTSignRequest extends InitPayMethodParam {

    @ApiModelProperty(value = "持卡人姓名")
    @NotEmpty(message = "持卡人姓名不可为空")
    private String name;

    @ApiModelProperty(value = "银行卡号")
    @NotEmpty(message = "银行卡号不可为空")
    private String cardNo;

    @ApiModelProperty(value = "身份证号")
    @NotEmpty(message = "身份证号不可为空")
    private String idNo;

    @ApiModelProperty(value = "银行预留手机号")
    @NotEmpty(message = "银行预留手机号不可为空")
    private String phone;

    @ApiModelProperty(value = "支付方式")
    @NotEmpty(message = "支付方式不可为空")
    private String method;

    @ApiModelProperty(value = "验证码")
    private String verifyCode;


}
