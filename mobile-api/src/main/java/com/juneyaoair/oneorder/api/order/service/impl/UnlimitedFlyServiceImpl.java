package com.juneyaoair.oneorder.api.order.service.impl;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.oneorder.api.order.constant.OrderUrlConstant;
import com.juneyaoair.oneorder.api.crm.constant.VoucherTypesEnum;
import com.juneyaoair.oneorder.api.order.config.OrderConfig;
import com.juneyaoair.oneorder.api.order.service.IUnlimitedFlyService;
import com.juneyaoair.oneorder.order.dto.*;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import com.juneyaoair.oneorder.tools.utils.HttpResult;
import com.juneyaoair.oneorder.tools.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.List;

/**
 * @ClassName UnlimitedFlyServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/7/3 13:15
 * @Version 1.0
 */
@Service
@Slf4j
public class UnlimitedFlyServiceImpl implements IUnlimitedFlyService {

    @Autowired
    private OrderConfig orderConfig;


    @Override
    public List<UnlimitedFlyBindRecord> listChdBindRecord(String channelCode, String ffpId, String ip) {
        BaseCouponOrderRequestDto requestDto = new BaseCouponOrderRequestDto();
        requestDto.setVersion("10");
        requestDto.setChannelCode(channelCode);
        requestDto.setRequestIp(ip);
        requestDto.setFfpId(ffpId);
        requestDto.setSearchType(VoucherTypesEnum.CHILD_UNLIMITED_FLY.getCode());
        BaseCouponOrderResponseDto<ChdFlyCardInfo> responseDto;
        String url = orderConfig.URL_FARE_API_ORDER + OrderUrlConstant.QUERY_UNLIMITED_FLY_BING_RECORD;
        HttpResult httpResult = HttpUtil.doPostClient(requestDto,url);
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw new ServiceException("服务器网络错误，查询儿童畅飞卡绑定记录失败");
        }
        Type type = new TypeToken<BaseCouponOrderResponseDto<ChdFlyCardInfo>>() {
        }.getType();
        responseDto = HoAirGsonUtil.fromJson(result, type);
        if (responseDto == null || !"1001".equals(responseDto.getResultCode())) {
            throw new ServiceException("服务器网络错误，查询儿童畅飞卡绑定记录失败");
        }
        return responseDto.getResult().getChildFlyRecords();
    }

    @Override
    public List<UnlimitedFlyV2BindRecord> listFlyCard2BindRecord(String channelCode, String ffpId, String ip) {
        BaseCouponOrderRequestDto requestDto = buildRequestDto(channelCode, ffpId, ip);
        requestDto.setSearchType(VoucherTypesEnum.UNLIMITED_FLY_V2.getCode() + "," + VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode());
        String url = orderConfig.URL_FARE_API_ORDER + OrderUrlConstant.QUERY_UNLIMITED_FLY_BING_RECORD_V2;
        HttpResult httpResult = HttpUtil.doPostClient(requestDto,url);
        String result = httpResult.getResponse();
        if(!httpResult.isResult()){
            throw MultiLangServiceException.fail("请求远程服务异常");
        }
        if (StringUtils.isBlank(result)) {
            throw new ServiceException("服务器网络错误，查询儿童畅飞卡绑定记录失败");
        }
        Type type = new TypeToken<BaseCouponOrderResponseDto<PtQueryBindDetailResponse>>() {
        }.getType();
        BaseCouponOrderResponseDto<PtQueryBindDetailResponse> responseDto = HoAirGsonUtil.fromJson(result, type);
        if (responseDto == null || !"1001".equals(responseDto.getResultCode())) {
            throw new ServiceException("服务器网络错误，查询儿童畅飞卡绑定记录失败");
        }
        return responseDto.getResult().getBindDetails();
    }

    @Override
    public List<UnlimitedFlyV2BindRecord> ThemeCouponBindRecord(String channelCode, String ffpId, String ip, List<String> cabinTypes) {
        BaseCouponOrderRequestDto requestDto = buildRequestDto(channelCode, ffpId, ip);
        String themeCouponStr = StringUtils.join(cabinTypes.toArray(), ",");
        requestDto.setSearchType(VoucherTypesEnum.THEMECOUPON.getCode() + "," + themeCouponStr);
        BaseCouponOrderResponseDto<PtQueryBindDetailResponse> responseDto;
        String url = orderConfig.URL_FARE_API_ORDER + OrderUrlConstant.QUERY_UNLIMITED_FLY_BING_RECORD_V2;
        HttpResult httpResult = HttpUtil.doPostClient(requestDto,url);
        String result = httpResult.getResponse();
        if(!httpResult.isResult()){
            throw MultiLangServiceException.fail("请求远程服务异常");
        }
        if (StringUtils.isBlank(result)) {
            throw new ServiceException("服务器网络错误，查询主题卡绑定记录失败");
        }
        Type type = new TypeToken<BaseCouponOrderResponseDto<PtQueryBindDetailResponse>>() {
        }.getType();
        responseDto = HoAirGsonUtil.fromJson(result, type);
        if (responseDto == null || !"1001".equals(responseDto.getResultCode())) {
            throw new ServiceException("服务器网络错误，查询主题卡绑定记录失败");
        }
        return responseDto.getResult().getBindDetails();
    }

    /**
     * 构建请求参数DTO
     *
     * @param channelCode
     * @param ffpId
     * @param ip
     * @return
     */
    private BaseCouponOrderRequestDto buildRequestDto(String channelCode, String ffpId, String ip) {
        BaseCouponOrderRequestDto requestDto = new BaseCouponOrderRequestDto();
        requestDto.setVersion("10");
        requestDto.setChannelCode(channelCode);
        requestDto.setRequestIp(ip);
        requestDto.setFfpId(ffpId);
        return requestDto;
    }
}
