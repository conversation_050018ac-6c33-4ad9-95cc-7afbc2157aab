package com.juneyaoair.oneorder.api.email;

import com.juneyaoair.oneorder.common.constant.BeanNameConstant;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

/**
 * <AUTHOR>
 * @description 会员邮箱配置
 * @date 2024/10/30 8:46
 */
@Configuration
public class EmailCrmConfig {
    @Value("${spring.mail.crm.host:mail.juneyaoair.com}")
    private String host;
    @Value("${spring.mail.crm.port:25}")
    private int port;
    @Value("${spring.mail.crm.username:<EMAIL>}")
    private String username;
    @Value("${spring.mail.crm.password:mHdH5Pcy}")
    private String password;
    @Value("${spring.mail.crm.from:<EMAIL>}")
    private String from;

    @Bean(name = BeanNameConstant.BEAN_NAME_EMAIL_SENDER_CRM)
    public JavaMailSender getJavaMailSenderCrm() {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        mailSender.setHost(host);
        mailSender.setPort(port);
        mailSender.setUsername(username);
        mailSender.setPassword(password);
        return mailSender;
    }

    @Bean(name = BeanNameConstant.BEAN_NAME_EMAIL_CONFIG_CRM)
    public EmailCommConfig initEmailCommConfig(){
        EmailCommConfig emailCommConfig = new EmailCommConfig();
        emailCommConfig.setHost(host);
        emailCommConfig.setPort(port);
        emailCommConfig.setUsername(username);
        emailCommConfig.setPassword(password);
        emailCommConfig.setFrom(from);
        return emailCommConfig;
    }
}
