package com.juneyaoair.oneorder.api.common;

import com.alibaba.fastjson2.TypeReference;
import com.alibaba.fastjson2.JSON;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import com.juneyaoair.oneorder.tools.utils.HttpResult;
import com.juneyaoair.oneorder.tools.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Type;
import java.util.Map;

@Slf4j
public abstract class HttpBaseServiceImpl {

    /**
     * @param request 请求参数
     * @param url     调用接口路径
     * @param type    接口返回结果需要反序列化的类型
     */
    protected <R, Q> Q invokeHttpClient(R request, String url, Type type) {
        return invokeHttpClient(request, url, type, null);
    }

    /**
     * @param request 请求参数
     * @param url     调用接口路径
     * @param type    接口返回结果需要反序列化的类型
     */
    protected <R, Q> Q invokeHttpClient(R request, String url, Type type, Map<String, String> headMap) {
        Q result = null;
        try {
            HttpResult httpResult;
            if (MapUtils.isEmpty(headMap)) {
                httpResult = HttpUtil.doPostClient(request, url);
            } else {
                httpResult = HttpUtil.doPostClient(request, url, headMap);
            }
            if (!httpResult.isResult()) {
                throw MultiLangServiceException.fail("调用远程服务繁忙");
            }
            if (StringUtils.isNotEmpty(httpResult.getResponse())) {
                result = HoAirGsonUtil.fromJson(httpResult.getResponse(), type);
            }
        } catch (Exception e) {
            log.error("调用接口报错，请求地址：{} 请求参数：{} 异常原因：", url, null == request ? "" : JSON.toJSONString(request), e);
            throw MultiLangServiceException.fail("调用接口报错");
        }
        return result;

    }

    /**
     * @param request
     * @param url
     * @param typeReference
     * @param headMap
     * @return
     * @param <T>
     */
    protected <T> T invokeHttp(Object request, String url, TypeReference<T> typeReference, Map<String, String> headMap) {
        try {
            HttpResult httpResult;
            if (MapUtils.isEmpty(headMap)) {
                httpResult = HttpUtil.doPostClient(request, url);
            } else {
                httpResult = HttpUtil.doPostClient(request, url, headMap);
            }
            if (!httpResult.isResult()) {
                throw MultiLangServiceException.fail("调用远程服务繁忙");
            }
            return JSON.parseObject(httpResult.getResponse(), typeReference);
        } catch (Exception e) {
            log.error("调用接口报错，请求地址：{} 请求参数：{} 异常原因：", url, null == request ? "" : JSON.toJSONString(request), e);
            throw MultiLangServiceException.fail("调用接口报错");
        }
    }

}
