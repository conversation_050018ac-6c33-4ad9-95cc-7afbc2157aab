package com.juneyaoair.oneorder.api.order.service.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.oneorder.api.order.dto.product.ProductAvailableReqDto;
import com.juneyaoair.oneorder.api.order.dto.product.ProductAvailableResDto;
import com.juneyaoair.oneorder.api.order.dto.product.ProductOrderBookReqDto;
import com.juneyaoair.oneorder.api.order.dto.product.ProductOrderBookResDto;
import com.juneyaoair.oneorder.api.order.dto.product.ProductPurchaseQualificationReqDto;
import com.juneyaoair.oneorder.api.order.dto.product.ProductPurchaseQualificationResDto;
import com.juneyaoair.oneorder.api.order.service.Product3HttpService;
import com.juneyaoair.oneorder.api.order.config.OrderConfig;
import com.juneyaoair.oneorder.order.util.JsonUtil;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.tools.utils.HttpResult;
import com.juneyaoair.oneorder.tools.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;

@Slf4j
@Service
public class Product3HttpServiceImpl implements Product3HttpService {

    @Resource
    private OrderConfig orderConfig;

    @Override
    public ProductAvailableResDto productQueryAvailable(RequestData<ProductAvailableReqDto> request) {
        //
        String url = orderConfig.URL_FARE_API_PRODUCT + orderConfig.PRODUCT_3_QUERY_AVAILABLE;
        //
        HttpResult httpResult = HttpUtil.doPostClient(request, url);
        if (httpResult.isResult()) {
            Type type = new TypeToken<ResponseData<ProductAvailableResDto>>() {
            }.getType();
            ResponseData<ProductAvailableResDto> response = (ResponseData<ProductAvailableResDto>) JsonUtil.jsonToBean(httpResult.getResponse(), type);
            return response.getData();
        }
        return null;
    }


    @Override
    public ProductOrderBookResDto productOrderBook(RequestData<ProductOrderBookReqDto> request) {
        //
        String url = orderConfig.URL_FARE_API_PRODUCT + orderConfig.PRODUCT_3_BOOK;
        //
        HttpResult httpResult = HttpUtil.doPostClient(request, url);
        if (httpResult.isResult()) {
            Type type = new TypeToken<ResponseData<ProductOrderBookResDto>>() {
            }.getType();
            ResponseData<ProductOrderBookResDto> response = (ResponseData<ProductOrderBookResDto>) JsonUtil.jsonToBean(httpResult.getResponse(), type);

            if ("SUCCESS".equals(response.getCode())) {
                return response.getData();
            } else {
                log.error("[创建订单请求发生错误],req:{},resp:{}", new Gson().toJson(request), httpResult.getResponse());
                throw new MultiLangServiceException(response.getMessage());
            }
        } else {
            log.error("[创建订单请求发生错误],req:{},resp:{}", new Gson().toJson(request), httpResult.getResponse());
            throw new MultiLangServiceException("创建订单异常");
        }
    }

    @Override
    public ProductPurchaseQualificationResDto purchaseQualificationInquiry(RequestData<ProductPurchaseQualificationReqDto> request) {
        //
        String url = orderConfig.URL_FARE_API_PRODUCT + orderConfig.PRODUCT_3_QUERY_QUALIFICATION;
        //
        HttpResult httpResult = HttpUtil.doPostClient(request, url);
        if (httpResult.isResult()) {
            Type type = new TypeToken<ResponseData<ProductPurchaseQualificationResDto>>() {
            }.getType();
            ResponseData<ProductPurchaseQualificationResDto> response = (ResponseData<ProductPurchaseQualificationResDto>) JsonUtil.jsonToBean(httpResult.getResponse(), type);
            return response.getData();
        }
        return null;
    }



}
