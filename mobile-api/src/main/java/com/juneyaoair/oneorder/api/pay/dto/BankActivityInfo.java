package com.juneyaoair.oneorder.api.pay.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/28 15:46
 */
@ApiModel(value = "BankActivityInfo",description = "活动信息")
@Data
@Builder
public class BankActivityInfo {
    @ApiModelProperty(value = "活动名称")
    private String bankActivity;
    @ApiModelProperty(value = "活动介绍Url")
    private String bankActivityUrl;
    @ApiModelProperty(value = "支付活动说明")
    private String bankActivityRemark;
    @ApiModelProperty(value = "支付宝立减活动参数")
    private String promoParam;
}
