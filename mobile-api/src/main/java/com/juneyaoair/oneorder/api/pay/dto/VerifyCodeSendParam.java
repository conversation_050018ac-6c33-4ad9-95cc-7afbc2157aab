package com.juneyaoair.oneorder.api.pay.dto;

import com.juneyaoair.oneorder.common.dto.BizDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName VerifySendParam
 * @Description
 * <AUTHOR>
 * @Date 2024/4/18 8:46
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "VerifyCodeSendParam", description = "交行信用卡支付请求参数")
public class VerifyCodeSendParam extends BizDto {

    @ApiModelProperty(value = "支付方式")
    @NotEmpty(message="支付方式不能为空")
    private String method;

    @ApiModelProperty(value = "银行卡号", required = true)
    @NotEmpty(message = "银行卡号不可为空")
    private String cardNo;

    @ApiModelProperty(value = "银行卡有效期（格式：月份年份（MMyy） 如0324，为24年3月份）", required = true)
    @NotEmpty(message = "银行卡有效期不可为空")
    private String expiryDate;

    @ApiModelProperty(value = "交易金额", required = true)
    @NotEmpty(message = "交易金额不可为空")
    private String amount;

    @ApiModelProperty(value = "订单类型",allowableValues = "D,I,O,GatewayUpgrade",notes = "D-国内机票订单,I-国际机票订单,O-其他,GatewayUpgrade-登机口升舱订单",required = true)
    @NotEmpty(message = "订单类型不可为空")
    private String orderType;

    @ApiModelProperty(value = "商户类型",allowableValues = "JX,JN",notes = "JX-吉祥，JN-吉宁，默认不传是JX")
    @NotEmpty(message = "收款商户不可为空")
    private String merchantPayment;

}
