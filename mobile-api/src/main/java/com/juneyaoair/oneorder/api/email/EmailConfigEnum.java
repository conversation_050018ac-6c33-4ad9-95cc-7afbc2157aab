package com.juneyaoair.oneorder.api.email;

import com.juneyaoair.oneorder.common.constant.BeanNameConstant;

/**
 * <AUTHOR>
 * @description 邮箱配置
 * @date 2025/3/17 10:49
 */
public enum EmailConfigEnum {
    CRM(BeanNameConstant.BEAN_NAME_EMAIL_SENDER_CRM,BeanNameConstant.BEAN_NAME_EMAIL_CONFIG_CRM),
    BOOK(BeanNameConstant.BEAN_NAME_EMAIL_SENDER_BOOK,BeanNameConstant.BEAN_NAME_EMAIL_CONFIG_BOOK),
    ;
    private String javaMailSenderBeanName;
    private String emailCommConfigBeanName;

    EmailConfigEnum(String javaMailSenderBeanName, String emailCommConfigBeanName) {
        this.javaMailSenderBeanName = javaMailSenderBeanName;
        this.emailCommConfigBeanName = emailCommConfigBeanName;
    }

    public String getJavaMailSenderBeanName() {
        return javaMailSenderBeanName;
    }

    public String getEmailCommConfigBeanName() {
        return emailCommConfigBeanName;
    }
}
