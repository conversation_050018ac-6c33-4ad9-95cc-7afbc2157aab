package com.juneyaoair.oneorder.api.pay.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName CardInfo
 * @Description 信用卡信息
 * <AUTHOR>
 * @Date 2024/4/24 10:02
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardInfo {

    @ApiModelProperty(value = "卡号")
    private String cardNo;

    @ApiModelProperty(value = "预付费卡密码")
    private String passWord;

    @ApiModelProperty(value = "卡有效期 信用卡有效期为2位年+2位月(yyMM)")
    private String validDate;

    @ApiModelProperty(value = "银行卡Cvv2码")
    private String cvv2;

    @ApiModelProperty(value = "证件类型代码 0:身份证 1：临时身份证 2：护照")
    private String idType;

    @ApiModelProperty(value = "证件号 预付费卡不需要")
    private String idNo;

    @ApiModelProperty(value = "卡所属人的姓名 预付费卡不需要")
    private String name;

    @ApiModelProperty(value = "此卡在银行预留手机号 预付费卡不需要")
    private String buyerPhone;

    @ApiModelProperty(value = "验证码")
    private String verificationCode;

    @ApiModelProperty(value = "验证码序号（两位）")
    private String verificationCodeNum;

    @ApiModelProperty(value = "支付编号")
    private String paymentTransId;
}
