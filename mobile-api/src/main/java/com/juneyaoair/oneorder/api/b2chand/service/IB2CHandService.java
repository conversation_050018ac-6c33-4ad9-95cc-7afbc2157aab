package com.juneyaoair.oneorder.api.b2chand.service;

import com.juneyaoair.oneorder.api.b2chand.dto.FlightAbnormalityCheckRequest;
import com.juneyaoair.oneorder.api.b2chand.dto.FlightAbnormalityRequest;
import com.juneyaoair.oneorder.api.b2chand.dto.FlightAbnormalityResponse;

public interface IB2CHandService {
    FlightAbnormalityResponse checkFlightAbnormality(FlightAbnormalityCheckRequest request);

    FlightAbnormalityResponse queryFlightAbnormality(FlightAbnormalityRequest request);
}
