package com.juneyaoair.oneorder.api.order.service.impl;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.common.HttpBaseServiceImpl;
import com.juneyaoair.oneorder.api.dsop.BigDataConfig;
import com.juneyaoair.oneorder.api.order.config.OrderConfig;
import com.juneyaoair.oneorder.api.order.constant.OrderUrlConstant;
import com.juneyaoair.oneorder.api.order.dto.coupon.BuyInsuranceInfo;
import com.juneyaoair.oneorder.api.order.dto.coupon.NewBasicOrderDetailResponse;
import com.juneyaoair.oneorder.api.order.dto.coupon.NewCouponOrderDetailReq;
import com.juneyaoair.oneorder.api.order.dto.coupon.NewOrderBaseResponse;
import com.juneyaoair.oneorder.api.order.dto.coupon.QueryIsBuyInsuranceReq;
import com.juneyaoair.oneorder.api.order.dto.coupon.QueryIsBuyInsuranceResponse;
import com.juneyaoair.oneorder.api.order.dto.flightproof.FlightChangeProofApplyReq;
import com.juneyaoair.oneorder.api.order.dto.flightproof.FlightChangeProofResponse;
import com.juneyaoair.oneorder.api.order.dto.insurance.RefundInsuranceParam;
import com.juneyaoair.oneorder.api.order.dto.ticket.PtOwnSaleRequestDto;
import com.juneyaoair.oneorder.api.order.dto.ticket.PtOwnSaleResponseDto;
import com.juneyaoair.oneorder.api.order.dto.ticket.QueryTicketRuleInfoParam;
import com.juneyaoair.oneorder.api.order.dto.ticket.QueryTicketRuleInfoResult;
import com.juneyaoair.oneorder.api.order.service.IOrderService;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.order.common.PtResponse;
import com.juneyaoair.oneorder.common.common.UnifiedOrderResultEnum;
import com.juneyaoair.oneorder.order.dto.*;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import com.juneyaoair.oneorder.tools.utils.HttpResult;
import com.juneyaoair.oneorder.tools.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

/**
 * @ClassName OrderServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/6/27 13:57
 * @Version 1.0
 */
@Service
@Slf4j
public class OrderService extends HttpBaseServiceImpl implements IOrderService {
    @Autowired
    private CommonService commonService;
    @Autowired
    private OrderConfig orderConfig;
    @Autowired
    private BigDataConfig bigDataConfig;

    @Override
    public PtV2GeneralContactResponse toAddGeneralContact(GeneralContactRequest generalContactRequest) {
        PtV2GeneralContactResponse ptResponse;
        String url = orderConfig.URL_FARE_API_BOOK + OrderUrlConstant.ADD_COMMON_PERSON_V20;
        HttpResult httpResult = HttpUtil.doPostClient(generalContactRequest,url);
        if(!httpResult.isResult()){
            throw MultiLangServiceException.fail("请求远程服务异常");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw MultiLangServiceException.fail("添加常用乘机人出错");
        }
        Type type = new TypeToken<PtV2GeneralContactResponse>() {
        }.getType();
        ptResponse = HoAirGsonUtil.fromJson(result, type);
        //此错误编码时，前端需根据提示选择操作方式
        if (UnifiedOrderResultEnum.REPEATED_PASS.getResultCode().equals(ptResponse.getResultCode())) {
            throw MultiLangServiceException.fail(CommonErrorCode.PASSENGER_ADD_FAIL);
        }
        if (UnifiedOrderResultEnum.PASSENGER_NAME_LENGTH_ERROR.getResultCode().equals(ptResponse.getResultCode())) {
            throw MultiLangServiceException.fail(CommonErrorCode.PASSENGER_NAME_LENGTH_ERROR);
        }
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
            throw MultiLangServiceException.fail(CommonErrorCode.PASSENGER_ADD_FAIL);
        }
        return ptResponse;
    }

    @Override
    @SuppressWarnings("rawtypes")
    public PtResponse toModifyGeneralContact(GeneralContactRequest generalContactRequest) {
        PtResponse ptResponse;
        String url = orderConfig.URL_FARE_API_BOOK + OrderUrlConstant.MODIFY_COMMON_PERSON_V20;
        HttpResult httpResult = HttpUtil.doPostClient(generalContactRequest,url);
        if(!httpResult.isResult()){
            throw MultiLangServiceException.fail("请求远程服务异常");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw MultiLangServiceException.fail("修改/删除常用乘机人出错");
        }
        Type type = new TypeToken<PtResponse>() {
        }.getType();
        ptResponse = HoAirGsonUtil.fromJson(result, type);
        if ("5116".equals(ptResponse.getResultCode())) {
            throw MultiLangServiceException.fail("乘客姓名长度错误，需大于0小于等于25个字符");
        }
        if (!"1001".equals(ptResponse.getResultCode())) {
            throw MultiLangServiceException.fail(ptResponse.getErrorInfo());
        }
        return ptResponse;
    }

    @Override
    public FlightChangeProofResponse getTicketFlightCheck(FlightChangeProofApplyReq req) {
        return invokeHttpClient(req,
                orderConfig.URL_FARE_API_ORDER + orderConfig.TICKET_INFO,
                new TypeToken<FlightChangeProofResponse>() {
                }.getType());
    }

    @Override
    public PtOwnSaleResponseDto queryOwnSaleTicketInfo(PtOwnSaleRequestDto req, Map<String, String> map) {
        return invokeHttpClient(req,
                orderConfig.URL_FARE_API_ORDER + OrderUrlConstant.QUERY_OWN_SALE,
                new TypeToken<PtOwnSaleResponseDto>() {
                }.getType(),
                map);
    }

    @Override
    public com.juneyaoair.oneorder.api.order.dto.ticket.TicketListInfoResponse listTicketInfo(TicketInfoRequest req, Map<String, String> map) {
        return invokeHttpClient(req,
                orderConfig.URL_FARE_API_ORDER + OrderUrlConstant.QUERY_TICKET_INFO,
                new TypeToken<com.juneyaoair.oneorder.api.order.dto.ticket.TicketListInfoResponse>() {
                }.getType(),
                map);
    }

    @Override
    public NewBasicOrderDetailResponse queryCouponOrderDetail(NewCouponOrderDetailReq couponOrderDetailReq) {
        couponOrderDetailReq.setVersion("V1.0");
        String url = orderConfig.URL_FARE_API_ORDER + OrderUrlConstant.BASIC_GET_COUPON_ORDER;
        HttpResult httpResult = HttpUtil.doPostClient(couponOrderDetailReq, url);
        if(!httpResult.isResult()){
            throw MultiLangServiceException.fail("请求远程服务异常");
        }
        String result = httpResult.getResponse();
        // 没有返回
        if (StringUtils.isBlank(result)) {
            throw MultiLangServiceException.fail("新权益券订单详情查询失败，请稍后再试！");
        }
        Type type = new TypeToken<NewOrderBaseResponse<NewBasicOrderDetailResponse>>() {}.getType();
        NewOrderBaseResponse<NewBasicOrderDetailResponse> orderBaseResponse = HoAirGsonUtil.fromJson(result, type);
        if (!"1001".equals(orderBaseResponse.getResultCode())) {
            throw MultiLangServiceException.fail("新权益券订单详情查询失败，请稍后再试！");
        }
        NewBasicOrderDetailResponse orderDetailResponse = orderBaseResponse.getData();
        if (null == orderDetailResponse) {
            throw MultiLangServiceException.fail("新权益券订单不存在！");
        }
        return orderDetailResponse;
    }

    @Override
    public List<BuyInsuranceInfo> QueryIsBuyInsurance(QueryIsBuyInsuranceReq queryIsBuyInsuranceReq) {
        queryIsBuyInsuranceReq.setVersion("V1.0");
        String url = orderConfig.URL_FARE_API_ORDER + OrderUrlConstant.QUERY_IS_BUY_INSURANCE;
        HttpResult httpResult = HttpUtil.doPostClient(queryIsBuyInsuranceReq, url);
        if(!httpResult.isResult()){
            throw MultiLangServiceException.fail("请求远程服务异常");
        }
        String result = httpResult.getResponse();
        // 没有返回
        if (StringUtils.isBlank(result)) {
            throw MultiLangServiceException.fail("是否购买保险查询失败，请稍后再试！");
        }
        Type type = new TypeToken<QueryIsBuyInsuranceResponse>() {}.getType();
        QueryIsBuyInsuranceResponse orderBaseResponse = HoAirGsonUtil.fromJson(result, type);
        if (!"1001".equals(orderBaseResponse.getResultCode())) {
            throw MultiLangServiceException.fail("是否购买保险查询失败，请稍后再试！");
        }
        return orderBaseResponse.getBuyInsruanceInfoList();
    }

    @Override
    public void refundInsurance(String channelNo, RefundInsuranceParam refundInsuranceParam) {
        refundInsuranceParam.setVersion(OrderUrlConstant.ORDER_VERSION);
        if (StringUtils.isNotBlank(channelNo)) {
            ChannelInfo channelInfo = commonService.findChannelInfo(channelNo);
            refundInsuranceParam.setChannelCode(channelInfo.getOrderChannelCode());
            refundInsuranceParam.setUserNo(channelInfo.getUserNo());
        }
        String url = orderConfig.URL_FARE_API_REFUND + OrderUrlConstant.SUB_REFUND_INSURE;
        HttpResult httpResult = HttpUtil.doPostClient(refundInsuranceParam, url);
        if(!httpResult.isResult()){
            throw MultiLangServiceException.fail("请求远程服务异常");
        }
        String result = httpResult.getResponse();
        // 没有返回
        if (StringUtils.isBlank(result)) {
            throw MultiLangServiceException.fail("订单退保失败，请稍后再试！");
        }
        Type type = new TypeToken<NewOrderBaseResponse<Object>>() {}.getType();
        NewOrderBaseResponse<Object> orderBaseResponse = HoAirGsonUtil.fromJson(result, type);
        if (!"1001".equals(orderBaseResponse.getResultCode())) {
            String message = StringUtils.isBlank(orderBaseResponse.getErrorInfo()) ? "订单退保失败，请稍后再试！" : orderBaseResponse.getErrorInfo();
            throw new MultiLangServiceException(CommonErrorCode.SYSTEM_ERROR, message);
        }
    }

    @Override
    public QueryTicketRuleInfoResult queryTicketRuleInfo(ChannelInfo channelInfo,String ticketNo) {
        QueryTicketRuleInfoParam queryTicketRuleInfoParam = new QueryTicketRuleInfoParam();
        queryTicketRuleInfoParam.setVersion(OrderUrlConstant.ORDER_VERSION);
        queryTicketRuleInfoParam.setChannelCode(channelInfo.getOrderChannelCode());
        queryTicketRuleInfoParam.setUserNo(channelInfo.getUserNo());
        queryTicketRuleInfoParam.setLanguageCode("CN");
        queryTicketRuleInfoParam.setTicketNo(ticketNo);
        String url = orderConfig.URL_FARE_API_ORDER + OrderUrlConstant.QUERY_TICKET_RULE_INFO;
        HttpResult httpResult = HttpUtil.doPostClient(queryTicketRuleInfoParam, url);
        if(!httpResult.isResult()){
            throw MultiLangServiceException.fail("请求远程服务异常");
        }
        String result = httpResult.getResponse();
        // 没有返回
        if (StringUtils.isBlank(result)) {
            throw MultiLangServiceException.fail("查询退改规则失败，请稍后再试！");
        }
        Type type = new TypeToken<QueryTicketRuleInfoResult>() {}.getType();
        QueryTicketRuleInfoResult queryTicketRuleInfoResult = HoAirGsonUtil.fromJson(result, type);
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(queryTicketRuleInfoResult.getResultCode())) {
            String message = StringUtils.isBlank(queryTicketRuleInfoResult.getErrorInfo()) ? "查询退改规则失败，请稍后再试！" : queryTicketRuleInfoResult.getErrorInfo();
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR, message);
        }
        return queryTicketRuleInfoResult;
    }
}
