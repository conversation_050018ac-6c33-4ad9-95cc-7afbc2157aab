package com.juneyaoair.oneorder.api.dsop.service;

import com.juneyaoair.oneorder.api.dsop.dto.FlightInfoDynamicDto;
import com.juneyaoair.oneorder.api.dsop.dto.PtDsopResponse;
import com.juneyaoair.oneorder.api.dsop.dto.PtFlightStatusReq;
import com.juneyaoair.oneorder.api.dsop.dto.TicketInfoQueryRequest;
import com.juneyaoair.oneorder.ticket.TicketInfoQueryResponse;

import java.util.List;
import java.util.Map;


public interface IDsopService {
    /**
     * 大数据获取旅程info
     */
    PtDsopResponse<TicketInfoQueryResponse> getPtBIgDataResponse(TicketInfoQueryRequest req, Map<String,String> headMap);
    /**
     *航班动态查询
     */
    List<FlightInfoDynamicDto> searchFlightDynamicsInfo(PtFlightStatusReq ptFlightStatusReq, Map<String, String> headMap);
}
