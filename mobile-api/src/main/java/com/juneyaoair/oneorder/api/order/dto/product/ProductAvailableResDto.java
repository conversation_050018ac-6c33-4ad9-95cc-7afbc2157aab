package com.juneyaoair.oneorder.api.order.dto.product;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "可售产品查询响应")
public class ProductAvailableResDto {

    @ApiModelProperty(value = "活动号")
    private List<String> activityNo;

    @ApiModelProperty(value = "活动开始日期 yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date beginDatetime;

    @ApiModelProperty(value = "活动结束日期 yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date endDatetime;

    @ApiModelProperty(value = "通用属性")
    List<ProductFieldDto> commonFieldList;

    @ApiModelProperty(value = "产品列表")
    List<ProductActivityDto> productList;
}
