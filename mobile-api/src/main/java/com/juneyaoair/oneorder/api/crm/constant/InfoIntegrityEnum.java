package com.juneyaoair.oneorder.api.crm.constant;

/**
 * @ClassName InfoIntegrityEnum
 * @Description
 * <AUTHOR>
 * @Date 2023/7/13 18:22
 * @Version 1.0
 */
public enum InfoIntegrityEnum {

    HIGH("0","高"),
    MEDIUM("1","中"),
    <PERSON><PERSON>("2","低"),
    UNKNOWN("3","-");

    public final String code;
    public final String desc;

    InfoIntegrityEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
