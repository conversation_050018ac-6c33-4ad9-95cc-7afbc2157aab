package com.juneyaoair.oneorder.api.crm.utils;


import com.juneyaoair.oneorder.api.crm.constant.PassengerTypeEnum;
import com.juneyaoair.oneorder.order.constant.CertificateTypeEnum;
import com.juneyaoair.oneorder.constant.PatternCommon;

/**
 * <AUTHOR>
 * @description
 * @date 2018/11/12  14:20.
 */
public class CertNoUtil {

    public static final String IDReg = "^(\\d{6})(18|19|20)?(\\d{2})([01]\\d)([0123]\\d)(\\d{3})(\\d|X|x)?$";

    /**
     *  根据证件号判断类型
     */
    public static String getCertTypeByCertNo(String certNo){
        if(certNo.matches(PatternCommon.ID_NUMBER)){
            return "NI";
        }else if(certNo.matches(PatternCommon.TICKET_NO)){
            return  "TN";
        }else if(certNo.matches(PatternCommon.PASSPORT_NO)){
            return  "PP";
        }else{
            return "CC";
        }
    }

    /**
     *  根据证件号判断类型
     *  此处返回的是枚举类型
     */
    public static CertificateTypeEnum getCTypeByCertNo(String certNo){
        if(certNo.matches(PatternCommon.ID_NUMBER)){
            return CertificateTypeEnum.ID_CARD;
        }else if(certNo.matches(PatternCommon.PASSPORT_NO)){
            return CertificateTypeEnum.PASSPORT;
        }else{
            return CertificateTypeEnum.OTHER;
        }
    }

    //根据年龄转换乘客类型
    public static String toPassType(int age) {
        if (age >= 0 && age < 2) {
            return PassengerTypeEnum.INF.getPassType();
        } else if (age >= 2 && age < 12) {
            return PassengerTypeEnum.CHD.getPassType();
        } else if (age >= 12) {
            return PassengerTypeEnum.ADT.getPassType();
        }
        return PassengerTypeEnum.ADT.getPassType();
    }
}
