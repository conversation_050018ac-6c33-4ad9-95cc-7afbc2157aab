package com.juneyaoair.oneorder.api.order.dto.coupon;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class ResourcePriceDetail {
    /**
     * 价种
     *   Unit：单价
         ADT：成人价
         CHD：儿童价
         INF:婴儿价
     */
    private String PriceType;
    /**
     * 预定数量
     */
    private int BookingCount;
    /**
     * 价格单位
     */
    private String PriceUnit;
    /**
     * 销售单价
     */
    private Double SalePrice;
    /**
     *标准售价
     */
    private Double StandardPrice;
    /**
     * 单位数量
     */
    private Double UnitNumber;

}
