package com.juneyaoair.oneorder.api.pay.dto;

import com.juneyaoair.oneorder.common.dto.BizDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/18 13:06
 */
@ApiModel(value = "OrderParam",description = "订单请求参数")
@Data
public class OrderParam extends BizDto {
    @NotBlank(message = "渠道订单编号不可为空")
    @ApiModelProperty("渠道订单编号")
    private String channelOrderNo;
    @NotBlank(message = "订单编号不可为空")
    @ApiModelProperty("订单编号")
    private String orderNo;
}
