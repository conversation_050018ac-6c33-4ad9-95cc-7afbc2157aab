package com.juneyaoair.oneorder.api.cuss.service;

import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.oneorder.api.cuss.dto.DetrMemberInfo;
import com.juneyaoair.oneorder.api.cuss.dto.TicketVerifyResponse;

public interface IVerifyTicketService {
    /**
     * @description 客票提取操作
     * <AUTHOR>
     * @date 2025/5/15 13:14
     * @param req
     * @return BaseResultDTO<TicketVerifyResponse>
     **/
    BaseResultDTO<TicketVerifyResponse> verifyTicket(BaseRequestDTO<DetrMemberInfo> req);
}
