package com.juneyaoair.oneorder.api.crm.utils;

import com.juneyaoair.oneorder.api.crm.constant.VerifyStatusEnum;
import com.juneyaoair.oneorder.crm.dto.common.MemberRealNameSummarySoaModel;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Comparator;
import java.util.List;

/**
 * @ClassName CrmUtil
 * @Description
 * <AUTHOR>
 * @Date 2023/6/20 15:49
 * @Version 1.0
 */
public class CrmUtil {
    /**
     * 根据认证记录判断是否实名
     *
     * @param realVerifyInfos
     * @return true - 已实名 false - 未实名
     */
    public static boolean toCheckRealNameStatus(List<MemberRealNameSummarySoaModel> realVerifyInfos) {
        if (CollectionUtils.isEmpty(realVerifyInfos)) {
            return false;
        }
        MemberRealNameSummarySoaModel memberRealNameSummarySoaModel = realVerifyInfos.stream().max(Comparator.comparing(MemberRealNameSummarySoaModel::getVerifyDate)).orElse(null);
        if (memberRealNameSummarySoaModel == null) {
            return false;
        } else {
            boolean status;
            status = VerifyStatusEnum.PASS.code.equals(memberRealNameSummarySoaModel.getStatus());
            return status;
        }
    }
}
