package com.juneyaoair.oneorder.api.crm.clientfeign;

import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.api.crm.dto.hocar.AccountRelationQueryReqDto;
import com.juneyaoair.oneorder.api.crm.dto.hocar.RelationAccount;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/2 14:33
 */
@Slf4j
@Component
public class CrmBffClientFallbackFactory implements FallbackFactory<CrmBffClient> {
    @Override
    public CrmBffClient create(Throwable throwable) {
        log.error("CrmBffClient服务降级异常:", throwable);
        return new CrmBffClient() {
            @Override
            public ResponseData<RelationAccount> accountRelationQuery(RequestData<AccountRelationQueryReqDto> requestData) {
                ResponseData responseData = new ResponseData<>(CommonErrorCode.FAIL.getCode(),CommonErrorCode.FAIL.getStatus(), CommonErrorCode.FAIL.getMessage());
                return responseData;
            }
        };
    }
}
