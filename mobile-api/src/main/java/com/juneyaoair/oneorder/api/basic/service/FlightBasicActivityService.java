package com.juneyaoair.oneorder.api.basic.service;

import com.juneyaoair.flightbasic.request.activity.MemberPrivateDomainParam;
import com.juneyaoair.flightbasic.response.MemberPrivateDomainResult;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;

/**
 * 基础服务活动
 * <AUTHOR>
 */
public interface FlightBasicActivityService {

    /**
     * 获取用户私域信息
     * @param requestData
     * @param memberPrivateDomainParam
     * @return
     */
    MemberPrivateDomainResult getMemberPrivateDomain(RequestDataDto requestData, MemberPrivateDomainParam memberPrivateDomainParam);

}
