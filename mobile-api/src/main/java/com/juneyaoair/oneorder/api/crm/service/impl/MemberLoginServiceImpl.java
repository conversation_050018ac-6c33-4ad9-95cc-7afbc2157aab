package com.juneyaoair.oneorder.api.crm.service.impl;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.config.CrmConfig;
import com.juneyaoair.oneorder.api.crm.constant.CrmUrlConstant;
import com.juneyaoair.oneorder.api.crm.service.IMemberLoginService;
import com.juneyaoair.oneorder.crm.dto.PtApiCRMRequest;
import com.juneyaoair.oneorder.crm.dto.PtCRMResponse;
import com.juneyaoair.oneorder.crm.dto.login.PtCheckLoginResponse;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import com.juneyaoair.oneorder.tools.utils.HttpResult;
import com.juneyaoair.oneorder.tools.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/31 18:01
 */
@Slf4j
@Service
public class MemberLoginServiceImpl extends CommonService implements IMemberLoginService {
    @Autowired
    private CrmConfig crmConfig;
    @Autowired
    private RedisUtil redisUtil;

    @Override
    public PtCRMResponse Logout(PtApiCRMRequest ptApiCRMRequest) {
        String url = crmConfig.getCrmLoginUrl() + CrmUrlConstant.MEMBER_LOGIN_OUT;
        HttpResult httpResult = HttpUtil.doPostClient(ptApiCRMRequest,url);
        if(!httpResult.isResult()){
            throw MultiLangServiceException.fail("请求会员服务异常");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw new ServiceException("CRM请求异常:" + ptApiCRMRequest.getRequestId());
        }
        Type type = new TypeToken<PtCRMResponse>() {
        }.getType();
        PtCRMResponse<PtCheckLoginResponse> ptCRMResponse = HoAirGsonUtil.fromJson(result, type);
        if (ptCRMResponse == null) {
            throw new ServiceException("数据转换异常:" + ptApiCRMRequest.getRequestId());
        }
        if (0 != ptCRMResponse.getCode() && 110002 != ptCRMResponse.getCode()) {
            throw ServiceException.fail(ptCRMResponse.getMsg());
        }
        return ptCRMResponse;
    }

}
