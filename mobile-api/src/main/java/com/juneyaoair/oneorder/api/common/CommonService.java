package com.juneyaoair.oneorder.api.common;

import com.juneyaoair.flightbasic.common.BaseReq;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.mobile.exception.util.HoAirIpUtil;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.CheckDayLicense;
import com.juneyaoair.oneorder.common.dto.enums.CaptchaFuncEnum;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.common.request.FrequencyLimit;
import com.juneyaoair.oneorder.common.request.LimitCount;
import com.juneyaoair.oneorder.common.response.SendSmsCodeResponse;
import com.juneyaoair.oneorder.config.LimitTimesConfig;
import com.juneyaoair.oneorder.config.MobileApiConfig;
import com.juneyaoair.oneorder.constant.CommonBaseConstants;
import com.juneyaoair.oneorder.core.context.SecurityContextHolder;
import com.juneyaoair.oneorder.mobile.config.AppConfig;
import com.juneyaoair.oneorder.mobile.config.RedisConstantConfig;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.mobile.dto.EmailContent;
import com.juneyaoair.oneorder.mobile.dto.PayChannelDetail;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.order.util.AirStringUtil;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import com.juneyaoair.oneorder.tools.utils.HoAirUuidUtil;
import com.juneyaoair.oneorder.tools.utils.RedisKeyUtil;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.*;

import static com.juneyaoair.oneorder.config.LimitTimesConfig.SMS_COMMON_SOURCE;
import static com.juneyaoair.oneorder.constant.CommonBaseConstants.LOG_REQ_TWO;

/**
 * <AUTHOR>
 * @description 通用service 包含基础方法，一些渠道配置参数检验
 * @date 2023/6/19 10:05
 */
@Service
@Slf4j
public class CommonService {
    @Autowired
    private AppConfig appConfig;
    @Resource
    protected RedisUtil redisUtil;
    @Resource
    private MobileApiConfig mobileApiConfig;
    @Resource
    private LimitTimesConfig limitTimesConfig;
    @Autowired
    private RedisConstantConfig redisConstantConfig;

    private Random random;

    //选座值机未登录次数限制
    public static final String CHECKIN_SELECT_SOURCE_NOT_LOGIN = "CheckInSeatNotLogin";
    public static final String PHOTOAUTH_IP_SOURCE = "photoauthip";
    public static final String PHOTOAUTH_DEVICE_SOURCE = "photoauthdevice";
    public static final String PHOTOAUTH_MOBILE_SOURCE = "photoauth";
    //运价查询限制次数
    public static String AV_SOURCE = "av";
    //注册限制次数
    public static String REG_SOURCE = "reg";

    //短信验证限制次数
    public static String VERYCODE_SOURCE = "verycode";
    //邮箱验证码限制次数
    public static String EMAIL_VERYCODE_SOURCE = "emailverycode";
    public static int EMAIL_VERYCODE_LIMIT_FOREVER_TIMES = 10;
    // 修改密码
    public static String UPDATEPWD_SOURCE = "upwd";
    // 忘记密码
    public static String FORGETPWD_SOURCE = "fpwd";

    // 修改消费密码
    public static String CONSUME_SOURCE = "xiaofei";

    // 修改手机号码
    public static String MOBILE_SOURCE = "mb";
    // 总次数
    public static String TOTAL_SOURCE = "all";

    // 下单会员id和卡号默认值
    private static final String FFP_DEFAULT_VAL = "-1";

    /**
     * 获取渠道配置信息
     *
     * @param headChannelCode 注意此渠道是来源于请求头
     * @return
     */
    public ChannelInfo findChannelInfo(String headChannelCode) {
        Map<String, ChannelInfo> channelInfoMap = appConfig.getChannelSetMap();
        if (ObjectUtils.isEmpty(channelInfoMap)) {
            throw ServiceException.fail("请检查channel.map配置");
        }
        ChannelInfo channelInfo = channelInfoMap.get(headChannelCode);
        if (ObjectUtils.isEmpty(channelInfo)) {
            throw ServiceException.fail("请检查" + headChannelCode + "配置");
        }
        channelInfo.setHeadChannelCode(headChannelCode);
        return channelInfo;
    }

    /**
     * @param merchantPayment 收款商户号
     * @param channelCode     渠道，客户端渠道
     * @param method          支付方式
     * @return
     * @description 获取支持人民币的支付方式
     */
    public PayChannelDetail findPayChannelInfo(String merchantPayment, String channelCode, String method, String orderType) {
        return findPayChannelInfo(merchantPayment, channelCode, method, orderType, "CNY");
    }

    /**
     * @param merchantPayment 收款商户号
     * @param channelCode     渠道，客户端渠道
     * @param payMethod       支付方式
     * @param currency        支付币种
     * @return
     */
    public PayChannelDetail findPayChannelInfo(String merchantPayment, String channelCode, String payMethod, String orderType, String currency) {
        Map<String, Map<String, List<PayChannelDetail>>> payChannelShowMap = appConfig.getPayChannelDetailMap();
        if (ObjectUtils.isEmpty(payChannelShowMap)) {
            throw ServiceException.fail(CommonErrorCode.SYSTEM_ERROR, "请检查payChannel.map配置");
        }
        //商户支付配置
        Map<String, List<PayChannelDetail>> payChannelDetailMap = payChannelShowMap.get(merchantPayment);
        if (ObjectUtils.isEmpty(payChannelDetailMap)) {
            throw ServiceException.fail(CommonErrorCode.SYSTEM_ERROR, "请检查" + merchantPayment + "配置");
        }
        //渠道支付配置
        List<PayChannelDetail> payChannelDetailMapList = payChannelDetailMap.get(channelCode);
        if (CollectionUtils.isEmpty(payChannelDetailMapList)) {
            throw ServiceException.fail(CommonErrorCode.SYSTEM_ERROR, "请检查" + channelCode + "配置");
        }
        Optional<PayChannelDetail> optional = payChannelDetailMapList.stream().filter(payChannelDetail -> {
            if (payMethod.equals(payChannelDetail.getMethod())) {
                if (StringUtils.isBlank(payChannelDetail.getCurrency())) {
                    return true;
                } else {
                    return StringUtils.isNotBlank(currency) && payChannelDetail.getCurrency().contains(currency);
                }
            } else {
                return false;
            }
        }).findFirst();
        if (!optional.isPresent()) {
            throw ServiceException.fail(CommonErrorCode.SYSTEM_ERROR, "请检查" + payMethod + "," + currency + "配置");
        }
        PayChannelDetail payChannelDetail = optional.get();
        if (CollectionUtils.isNotEmpty(payChannelDetail.getSuitOrderType()) && payChannelDetail.getSuitOrderType().contains(orderType)) {
            return payChannelDetail;
        } else {
            throw ServiceException.fail(CommonErrorCode.SYSTEM_ERROR, "请检查" + orderType + "配置");
        }
    }

    /**
     * 获取请求头中客户端版本信息
     *
     * @return
     */
    public String fetchClientVersion() {
        String clientVersion = SecurityContextHolder.getClientVersion();
        if (StringUtils.isBlank(clientVersion)) {
            clientVersion = SecurityContextHolder.getVersion();
        }
        return StringUtils.isBlank(clientVersion) ? "0" : clientVersion;
    }

    /**
     * 输出请求日志
     *
     * @param reqId
     * @param url
     * @param obj
     */
    public void printReqLog(String reqId, String url, Object obj) {
        log.info("请求ID:{},请求地址:{},请求参数:{}", reqId, url, HoAirGsonUtil.objectToJson(obj));
    }

    /**
     * 输出响应日志
     *
     * @param reqId
     * @param url
     * @param obj
     * @param time
     */
    public void printResultLog(String reqId, String url, Object obj, long time) {
        log.info("请求ID:{},请求地址:{},请求响应:{},耗时:{}ms", reqId, url, HoAirGsonUtil.objectToJson(obj), System.currentTimeMillis() - time);
    }

    /**
     * 创建BaseRequestDTO 基础服务系统
     *
     * @param bizDto 通用系统参数 IP，请求流水号
     * @param obj    业务参数
     * @return
     */
    public <T> BaseRequestDTO<T> createBaseRequestDTO(BizDto bizDto, T obj) {
        BaseRequestDTO baseRequestDTO = BaseRequestDTO.builder()
                .ip(bizDto.getIp())
                .request(obj)
                .build();
        baseRequestDTO.setVersion("10");
        baseRequestDTO.setServieCode(bizDto.getBizSeq());
        baseRequestDTO.setChannelCode(bizDto.getHeadChannelCode());
        return baseRequestDTO;
    }

    /**
     * 创建BaseRequestDTO  基础服务系统
     *
     * @param ip          通用系统参数 IP
     * @param channelCode 渠道号
     * @param obj         业务参数
     * @return
     */
    public <T> BaseRequestDTO<T> createBaseRequestDTO(String ip, String channelCode, T obj) {
        BaseRequestDTO baseRequestDTO = BaseRequestDTO.builder()
                .ip(ip)
                .request(obj)
                .build();
        baseRequestDTO.setVersion("10");
        baseRequestDTO.setServieCode(HoAirUuidUtil.randomUUID8());
        baseRequestDTO.setChannelCode(channelCode);
        return baseRequestDTO;
    }

    /**
     * 创建BaseReq 基础服务 1.0
     *
     * @param bizDto 通用系统参数 IP，请求流水号
     * @param t      业务参数
     * @return
     */
    public <T> BaseReq<T> createBaseReq(BizDto bizDto, T t) {
        BaseReq baseReq = BaseReq.builder()
                .channelCode(bizDto.getHeadChannelCode())
                .clientVersion("1.0")
                .request(t)
                .build();
        return baseReq;
    }

    /*验证访问*/
    public boolean chkDayVisit(String ip, String source, String type) {
        if (StringUtils.isBlank(ip) || StringUtils.isBlank(source)) {
            return false;
        }
        if (HoAirIpUtil.filterIP(ip, mobileApiConfig.IP_PATTERN)) {
            return true;
        }
        String foreverLimitKey = RedisKeyUtil.getDayVisitForeverLimitKey("", ip, source, type);
        String visitCntStr = (String) redisUtil.get(foreverLimitKey);
        Integer foreverVisitCode = StringUtil.isNullOrEmpty(visitCntStr) ? 0 : Integer.parseInt(visitCntStr);
        if (foreverVisitCode > 0 && "Y".equalsIgnoreCase(mobileApiConfig.getForeverLimitSwitch())) {
            log.error(CommonBaseConstants.LOG_RESP_TWO, ip, foreverVisitCode, source);
            return false;
        }

        String key = RedisKeyUtil.getDayVisitKey("", ip, source, type);
        String visitCode = (String) redisUtil.get(key);
        if (StringUtil.isNullOrEmpty(visitCode)) {
            redisUtil.set(key, "1", 86400);
            return true;
        } else {
            log.debug(CommonBaseConstants.LOG_REQ_TWO, key, ip, visitCode);
            int limitTimes = getLimitTimesBySource(source);//每日访问次数限制
            int visitCnt = Integer.parseInt(visitCode);//当日已访问次数
            int limitForeverTimes = getForeverLimitTimesBySource(source);//永久访问次数限制
            if (visitCnt >= limitTimes) {
                if (limitForeverTimes > 0) {
                    if (visitCnt >= limitForeverTimes) {
                        redisUtil.set(foreverLimitKey, String.valueOf(visitCnt), 0);
                        log.error(CommonBaseConstants.LOG_RESP_TWO, ip, visitCnt, source);
                        return false;
                    } else {
                        redisUtil.set(key, String.valueOf(visitCnt + 1), 86400);
                    }
                } else {
                    redisUtil.set(key, String.valueOf(visitCnt + 1), 86400);
                }
                log.error("ip:{},到达访问上限:{},类型：{}", ip, visitCode, key);
                return false;
            } else {
                redisUtil.set(key, String.valueOf(visitCnt + 1), 86400);
                return true;
            }
        }
    }

    /**
     * 根据操作来源获取相应限制次数
     *
     * @param source
     * @return
     */
    private int getLimitTimesBySource(String source) {
        int limitTimes = 500;
        //引用了flightBasic里的constant
        if (source.equals(AV_SOURCE)) {
            limitTimes = limitTimesConfig.AV_LIMIT_TIMES;
        }
        if (source.equals(REG_SOURCE)) {
            limitTimes = limitTimesConfig.REG_LIMIT_TIMES;
        }
        if (source.equals(EMAIL_VERYCODE_SOURCE)) {
            limitTimes = limitTimesConfig.EMAIL_VERYCODE_LIMIT_TIMES;
        }
        if (source.equals(VERYCODE_SOURCE)) {//注册验证码获取次数限制
            limitTimes = limitTimesConfig.VERYCODE_LIMIT_TIMES;
        }
        if (source.equals(limitTimesConfig.COMM_SOURCE)) {
            limitTimes = limitTimesConfig.COMM_LIMIT_TIMES;
        }
        if (source.equals(limitTimesConfig.LOGINERR_SOURCE)) {//每日登录出错限制 5次
            limitTimes = limitTimesConfig.LOGINERR_LIMIT_SOURCE;
        }
        if (source.equals(limitTimesConfig.LOGINCODE_IP_SOURCE)) {//IP快捷登录限制 3次
            limitTimes = limitTimesConfig.LOGINCODE_IP_LIMIT_SOURCE;
        }
        if (source.equals(limitTimesConfig.LOGINCODE_MOBILE_SOURCE)) {//账号快捷登陆限制 5次
            limitTimes = limitTimesConfig.LOGINCODE_MOBILE_LIMIT_SOURCE;
        }
        if (source.equals(MOBILE_SOURCE)) {//修改手机号码限制 5次
            limitTimes = limitTimesConfig.MOBILE_LIMIT_TIMES;
        }
        if (source.equals(CONSUME_SOURCE)) {//修改消费密码限制 5次
            limitTimes = limitTimesConfig.CONSUME_LIMIT_TIMES;
        }
        if (source.equals(FORGETPWD_SOURCE)) {//忘记密码限制 5次
            limitTimes = limitTimesConfig.FORGETPWD_LIMIT_TIMES;
        }
        if (source.equals(UPDATEPWD_SOURCE)) {//修改密码限制 5次
            limitTimes = limitTimesConfig.UPDATEPWD_LIMIT_TIMES;
        }
        if (source.equals(TOTAL_SOURCE)) {//总次数修改10次
            limitTimes = limitTimesConfig.TOTAL_LIMIT_TIMES;
        }
        if (source.equals(limitTimesConfig.CHECKIN_SOURCE)) {//值机次数限制10次
            limitTimes = limitTimesConfig.CHECKIN_SOURCE_CNT;
        }
        if (source.equals(limitTimesConfig.ACTIVITY_SOURCE)) {//活动每日限制次数
            limitTimes = limitTimesConfig.ACTIVITY_SOURCE_CNT;
        }
        if (source.equals(limitTimesConfig.LOGIN_SOURCE)) {//每日登录频次限制 30
            limitTimes = limitTimesConfig.LOGIN_LIMIT_TIMES;
        }
        if (source.equals(limitTimesConfig.NAMEAUTH_MOBILE_SOURCE)) {//每日实名验证码频次限制 10
            limitTimes = limitTimesConfig.NAMEAUTH_MOBILE_SOURCE_CNT;
        }
        if (source.equals(limitTimesConfig.NAMEAUTH_IP_SOURCE)) {//每日实名验证码IP频次限制 8
            limitTimes = limitTimesConfig.NAMEAUTH_IP_SOURCE_CNT;
        }
        if (source.equals(limitTimesConfig.NAMEAUTH_MOBILEERR_SOURCE)) {//每日实名验证码IP错误频次限制 6
            limitTimes = limitTimesConfig.NAMEAUTH_MOBILEERR_SOURCE_CNT;
        }
        if (source.equals(limitTimesConfig.GIVECOUPON_SOURCE)) {//领取优惠券验证码次数
            limitTimes = limitTimesConfig.GIVECOUPON_SOURCE_CNT;
        }
        if (source.equals(limitTimesConfig.CHECKIN_SELECT_SOURCE)) {
            limitTimes = limitTimesConfig.CHECKIN_SELECT_SOURCE_CNT;
        }
        if (source.equals(CHECKIN_SELECT_SOURCE_NOT_LOGIN)) {
            limitTimes = limitTimesConfig.CHECKIN_SELECT_SOURCE_NOT_LOGIN_CNT;
        }
        if (source.equals(limitTimesConfig.TICKET_VERIFY_SOURCE)) {
            limitTimes = limitTimesConfig.TICKET_VERIFY_SOURCE_CNT;
        }
        if (source.equals(PHOTOAUTH_IP_SOURCE)) {
            limitTimes = limitTimesConfig.PHOTOAUTH_IP_SOURCE_CNT;
        }
        if (source.equals(PHOTOAUTH_DEVICE_SOURCE)) {
            limitTimes = limitTimesConfig.PHOTOAUTH_DEVICE_SOURCE_CNT;
        }
        if (source.equals(PHOTOAUTH_MOBILE_SOURCE)) {
            limitTimes = limitTimesConfig.PHOTOAUTH_MOBILE_SOURCE_CNT;
        }
        if (source.equals(LimitTimesConfig.GOODS_LOST_SOURCE)) {
            limitTimes = limitTimesConfig.GOODS_LOST_SOURCE_CNT;
        }
        if (SMS_COMMON_SOURCE.equals(source)) {
            limitTimes = limitTimesConfig.SMS_COMMON_SOURCE_CNT;
        }
        return limitTimes;
    }

    /**
     * 根据操作来源获取相应永久限制次数
     * 返回-1的话表示该项操作没有永久限制判断
     *
     * @param source
     * @return
     */
    private int getForeverLimitTimesBySource(String source) {
        int foreverLimitTimes = -1;
        if (source.equals(LimitTimesConfig.AV_SOURCE)) {
            foreverLimitTimes = limitTimesConfig.AV_LIMIT_FOREVER_TIMES;
        }
        if (source.equals(LimitTimesConfig.VERYCODE_SOURCE)) {
            foreverLimitTimes = limitTimesConfig.VERYCODE_LIMIT_FOREVER_TIMES;
        }
        if (source.equals(LimitTimesConfig.COMM_SOURCE)) {
            foreverLimitTimes = limitTimesConfig.COMM_LIMIT_FOREVER_TIMES;
        }
        if (source.equals(LimitTimesConfig.MOBILE_SOURCE)) {
            foreverLimitTimes = limitTimesConfig.MOBILE_LIMIT_FOREVER_TIMES;
        }
        if (source.equals(LimitTimesConfig.CHECKIN_SOURCE)) {
            foreverLimitTimes = limitTimesConfig.CHECKIN_SOURCE_FOREVER_CNT;
        }
        if (source.equals(LimitTimesConfig.ACTIVITY_SOURCE)) {
            foreverLimitTimes = limitTimesConfig.ACTIVITY_SOURCE_FOREVER_CNT;
        }
        if (source.equals(LimitTimesConfig.NAMEAUTH_MOBILE_SOURCE)) {//每日实名验证码频次限制 10
            foreverLimitTimes = limitTimesConfig.NAMEAUTH_MOBILE_SOURCE_FOREVER_CNT;
        }
        if (source.equals(LimitTimesConfig.NAMEAUTH_IP_SOURCE)) {//每日实名验证码IP频次限制 6
            foreverLimitTimes = limitTimesConfig.NAMEAUTH_IP_SOURCE_FOREVER_CNT;
        }
        if (source.equals(LimitTimesConfig.GIVECOUPON_SOURCE)) {//领取优惠券验证码次数
            foreverLimitTimes = limitTimesConfig.GIVECOUPON_SOURCE_FOREVER_CNT;
        }
        if (source.equals(limitTimesConfig.CHECKIN_SELECT_SOURCE)) {
            foreverLimitTimes = limitTimesConfig.CHECKIN_SELECT_SOURCE_FOREVER_CNT;
        }
        if (source.equals(LimitTimesConfig.CHECKIN_SELECT_SOURCE_NOT_LOGIN)) {
            foreverLimitTimes = limitTimesConfig.CHECKIN_SELECT_SOURCE_FOREVER_NOT_LOGIN_CNT;
        }
        if (source.equals(LimitTimesConfig.TICKET_VERIFY_SOURCE)) {
            foreverLimitTimes = limitTimesConfig.TICKET_VERIFY_SOURCE_FOREVER_CNT;
        }
        if (source.equals(LimitTimesConfig.PHOTOAUTH_IP_SOURCE)) {
            foreverLimitTimes = limitTimesConfig.PHOTOAUTH_IP_SOURCE_FOREVER_CNT;
        }
        if (source.equals(LimitTimesConfig.PHOTOAUTH_DEVICE_SOURCE)) {
            foreverLimitTimes = limitTimesConfig.PHOTOAUTH_DEVICE_FOREVER_CNT;
        }
        if (source.equals(LimitTimesConfig.PHOTOAUTH_MOBILE_SOURCE)) {
            foreverLimitTimes = limitTimesConfig.PHOTOAUTH_MOBILE_SOURCE_FOREVER_CNT;
        }
        if (SMS_COMMON_SOURCE.equals(source)) {
            foreverLimitTimes = limitTimesConfig.SMS_COMMON_SOURCE_FOREVER_CNT;
        }
        return foreverLimitTimes;
    }

    /**
     * 总次数限制
     *
     * @param type
     * @return
     */
    public boolean chkDayTotalVisit(String ip, String type) {
        String dataStr = DateUtil.dateToString(new Date(), DateUtil.YYYY_MM_DD_PATTERN);
        String keyMb = dataStr + ip + "_" + MOBILE_SOURCE + type;
        String visitCodeMb = (String) redisUtil.get(keyMb);
        String keyXiaofei = dataStr + ip + "_" + CONSUME_SOURCE + type;
        String visitCodeXiaofei = (String) redisUtil.get(keyXiaofei);
        String keyUpwd = dataStr + ip + "_" + UPDATEPWD_SOURCE + type;
        String visitCodeUpwd = (String) redisUtil.get(keyUpwd);
        int visitCntMb = 0;
        if (!StringUtil.isNullOrEmpty(visitCodeMb)) {
            visitCntMb = Integer.parseInt(visitCodeMb);
        }
        int visitCntXiaofei = 0;
        if (!StringUtil.isNullOrEmpty(visitCodeXiaofei)) {
            visitCntXiaofei = Integer.parseInt(visitCodeXiaofei);
        }
        int visitCntFpwd = 0;
        int visitCntUpwd = 0;
        if (!StringUtil.isNullOrEmpty(visitCodeUpwd)) {
            visitCntUpwd = Integer.parseInt(visitCodeUpwd);
        }
        int visitCntTotal = visitCntMb + visitCntXiaofei + visitCntFpwd + visitCntUpwd;
        int limitTimes = getLimitTimesBySource("all");//每日访问总次数限制
        if (visitCntTotal >= limitTimes) {
            log.info("ip:{},到达访问上限:{},类型:all", ip, visitCntTotal);
            return false;
        }
        return true;
    }

    /**
     * 保存账号 IP等相关信息
     *
     * @param tableName 目录
     * @param map
     * @param date
     */
    public void saveDayInfo(String tableName, String cellName, String primary, Map<String, Object> map, Date date) {
        Date curDate = new Date();//TRIP_TICKET ip card info
        String curTime = DateUtil.convertDateToString(curDate, "yyyyMMddHHmmssSSS");
        String curDateStr = DateUtil.convertDateToString(curDate, DateUtil.YYYY_MM_DD_PATTERN);
        map.put("curDate", curTime);
        redisUtil.hmset2(tableName + ":" + curDateStr + "_" + cellName + ":" + primary, map,
                DateUtil.secondDiff(curDate, date)
        );
    }

    /**
     * 绑定邮箱每日次数限制
     */
    public boolean chkDayEmailVisit(String ip, String source, String email) {
        if (StringUtils.isBlank(ip) || StringUtils.isBlank(source)) {
            return false;
        }
        if (HoAirIpUtil.filterIP(ip, mobileApiConfig.IP_PATTERN)) {
            return true;
        }
        String dataStr = DateUtil.dateToString(new Date(), DateUtil.YYYY_MM_DD_PATTERN);
        //每日邮箱发送ip总次数限制
        String dayVisitForeverLimitKey = RedisKeyUtil.getDayVisitForeverLimitKey("", ip, source, "Email");
        String totalVisitCode = (String) redisUtil.get(dayVisitForeverLimitKey);
        if (StringUtil.isNullOrEmpty(totalVisitCode)) {
            redisUtil.set(dayVisitForeverLimitKey, "1", 86400);
            return true;
        } else {
            int totalLimitTimes = EMAIL_VERYCODE_LIMIT_FOREVER_TIMES;
            int totalVisitEmailCode = Integer.parseInt(totalVisitCode);
            if (totalVisitEmailCode > totalLimitTimes) {
                log.error("ip:{},该ip到达访问上限:{},类型:{}", ip, totalVisitEmailCode, dayVisitForeverLimitKey);
                return false;
            } else {
                redisUtil.set(dayVisitForeverLimitKey, String.valueOf(totalVisitEmailCode + 1), 86400);
            }
        }

        //每日邮箱验证码次数限制
        String key = dataStr + ip + "_" + source + email;
        String visitCode = (String) redisUtil.get(key);
        if (StringUtil.isNullOrEmpty(visitCode)) {
            redisUtil.set(key, "1", 86400);
            return true;
        } else {
            log.debug(LOG_REQ_TWO, key, ip, visitCode);
            //获取绑定邮箱每日限制的次数
            int limitTimes = getLimitTimesBySource(source);
            //每日目前的使用次数
            int visitInt = Integer.parseInt(visitCode);
            if (visitInt > limitTimes) {
                log.error("ip:{},该用户到达访问上限:{},类型:{}", ip, visitCode, key);
                return false;
            } else {
                redisUtil.set(key, String.valueOf(visitInt + 1), 86400);
                return true;
            }
        }
    }


    /**
     * 验证计数限制
     *
     * @return
     */
    public boolean chkSendSmsCountOperation(String limitType, String source, String type, LimitCount limitCount, SendSmsCodeResponse resp) {
        if (StringUtil.isNullOrEmpty(type)) {
            type = "default";
        }
        if (HoAirIpUtil.filterIP(limitType, mobileApiConfig.IP_PATTERN)) {
            return true;
        }
        //频率限制
        FrequencyLimit timeLimit = limitCount.getLimitMin();
        if (timeLimit != null && timeLimit.getAccessLimit() > 0) {
            String minKey = RedisKeyUtil.getSmsDayVisitMinLimitKey(source, limitType, type);
            String visitCodeStr = (String) redisUtil.get(minKey);
            int visitCode = StringUtil.isNullOrEmpty(visitCodeStr) ? 0 : Integer.parseInt(visitCodeStr);
            if (visitCode > timeLimit.getAccessLimit()) {
                resp.setResultCode(WSEnum.ERROR.resultCode);
                resp.setResultInfo("访问过于频繁，请稍后再试！");
                return false;
            }
        }
        //每日访问次数
        FrequencyLimit dayLimit = limitCount.getLimitDay();
        if (dayLimit != null && dayLimit.getAccessLimit() > 0) {
            String dayKey = RedisKeyUtil.getSmsDayVisitKey(source, limitType, type);
            String visitCodeStr = (String) redisUtil.get(dayKey);
            int visitCode = StringUtil.isNullOrEmpty(visitCodeStr) ? 0 : Integer.parseInt(visitCodeStr);
            if (visitCode > dayLimit.getAccessLimit()) {
                resp.setResultCode(WSEnum.ERROR.resultCode);
                resp.setResultInfo("访问过于频繁，请明日再试！");
                return false;
            }
        }
        //永久访问次数
        FrequencyLimit foreverLimit = limitCount.getLimitForever();
        if (foreverLimit != null && foreverLimit.getAccessLimit() > 0) {
            String foreverFinalKey = RedisKeyUtil.getSmsForeverVisitKey(source, limitType, type);
            String visitCodeStr = (String) redisUtil.get(foreverFinalKey);
            int visitCode = StringUtil.isNullOrEmpty(visitCodeStr) ? 0 : Integer.parseInt(visitCodeStr);
            if (visitCode > 0) {
                resp.setResultCode(WSEnum.ERROR.resultCode);
                resp.setResultInfo("访问过于频繁！");
                return false;
            }
        }
        return true;
    }

    /**
     * 操作计数方式
     *
     * @param limitType  IP地址，设备，手机号等等
     * @param source
     * @param type
     * @param limitCount
     */
    public void sendSmsCountOperation(String limitType, String source, String type, LimitCount limitCount) {
        if (StringUtil.isNullOrEmpty(type)) {
            type = "default";
        }
        //频率限制
        FrequencyLimit timeLimit = limitCount.getLimitMin();
        if (timeLimit != null && timeLimit.getFrequency() > 0) {
            String minKey = RedisKeyUtil.getSmsDayVisitMinLimitKey(source, limitType, type);
            String visitCodeStr = (String) redisUtil.get(minKey);
            Integer visitCode = StringUtil.isNullOrEmpty(visitCodeStr) ? 0 : Integer.parseInt(visitCodeStr);
            redisUtil.set(minKey, String.valueOf(visitCode + 1), timeLimit.getFrequency());
        }
        //每日访问次数
        FrequencyLimit dayLimit = limitCount.getLimitDay();
        String dayKey = RedisKeyUtil.getSmsDayVisitKey(source, limitType, type);
        Integer visitCode = 0;
        if (dayLimit != null && dayLimit.getFrequency() > 0) {
            String visitCodeStr = (String) redisUtil.get(dayKey);
            visitCode = StringUtil.isNullOrEmpty(visitCodeStr) ? 0 : Integer.parseInt(visitCodeStr);
            visitCode++;
            redisUtil.set(dayKey, String.valueOf(visitCode), dayLimit.getFrequency());
        }
        //永久访问次数
        FrequencyLimit foreverLimit = limitCount.getLimitForever();
        if (foreverLimit != null && foreverLimit.getAccessLimit() > 0) {
            String foreverFinalKey = RedisKeyUtil.getSmsForeverVisitKey(source, limitType, type);
            if (visitCode > foreverLimit.getAccessLimit()) {
                redisUtil.set(foreverFinalKey, String.valueOf(visitCode), -1);
            }
        }
    }

    /**
     * 生成6位数验证码
     *
     * @return
     */
    public String getChkCode() {
        if (null == random) {
            random = new SecureRandom();
        }
        StringBuilder stringBuilder = new StringBuilder("");
        for (int i = 0; i < 6; i++) {
            String rand = String.valueOf(random.nextInt(10));
            stringBuilder.append(rand);
        }
        return stringBuilder.toString();
    }

    /**
     * 校验验证码
     *
     * @param redisKey
     * @param verifyCode
     */
    public void checkVerifyCode(String redisKey, String verifyCode) {
        if (StringUtils.isAnyBlank(redisKey, verifyCode)) {
            log.error("验证码校验不通过，key:{} 验证码：{}", redisKey, verifyCode);
            throw new MultiLangServiceException(CommonErrorCode.VERIFY_CODE_FAIL, "验证码错误或已经失效！");
        }
        String redisVerifyCode = (String) redisUtil.get(redisKey);
        boolean flag = verifyCode.equals(redisVerifyCode);
        if (!flag) {
            log.error("验证码校验不通过，key:{} 验证码：{} 缓存验证码：{}", redisKey, verifyCode, redisVerifyCode);
            throw new MultiLangServiceException(CommonErrorCode.VERIFY_CODE_FAIL, "验证码错误或已经失效！");
        }
        redisUtil.del(redisKey);
    }

    /**
     * 根据位数获取指定位数的随机数 至少6位
     *
     * @param num
     * @return
     */
    public static String getChkCode(int num) throws NoSuchAlgorithmException {
        Random random = SecureRandom.getInstance("SHA1PRNG");
        StringBuilder stringBuilder = new StringBuilder();
        if (num < 6) {
            num = 6;
        }
        for (int i = 0; i < num; i++) {
            String rand = String.valueOf(random.nextInt(10));
            stringBuilder.append(rand);
        }
        return stringBuilder.toString();
    }

    /**
     * 验证每日操作或失败次数
     *
     * @param ip     IP或用户账号
     * @param source
     * @param type
     * @return
     */
    public boolean chkDayOptErr(String ip, String source, String type) {
        if (StringUtils.isBlank(ip) || StringUtils.isBlank(source)) {
            return false;
        }
        String foreverLimitKey = RedisKeyUtil.getDayVisitForeverLimitKey("", ip, source, type);
        String visitCntStr = (String) redisUtil.get(foreverLimitKey);
        Integer foreverVisitCode = StringUtil.isNullOrEmpty(visitCntStr) ? 0 : Integer.parseInt(visitCntStr);
        //永久禁止访问
        if (foreverVisitCode > 0) {
            log.error("ip:{},到达永久限制访问上限:{},类型：{}", ip, foreverVisitCode, source);
            return false;
        }
        String dataStr = DateUtil.dateToString(new Date(), DateUtil.YYYY_MM_DD_PATTERN);
        String key = dataStr + ip + "_" + source + type;
        String visitCode = (String) redisUtil.get(key);
        log.info(LOG_REQ_TWO, key, ip, visitCode);
        if (StringUtil.isNullOrEmpty(visitCode)) {
            return true;
        }
        int limitTimes = getLimitTimesBySource(source);//每日访问次数限制
        int visitCnt = Integer.parseInt(visitCode);//当日已访问次数
        if (visitCnt >= limitTimes + 1) {//达到每日错误峰值
            log.info("key_错误已达今日上线，请明日再试或联系服务人员:{}", key);
            return false;
        } else {
            return true;
        }
    }

    /**
     * 清除每日访问控制
     */
    public void clearDayVisit(String dir, String ip, String source, String type) {
        String foreverLimitKey = RedisKeyUtil.getDayVisitForeverLimitKey(dir, ip, source, type);
        String dayVisitKey = RedisKeyUtil.getDayVisitKey(dir, ip, source, type);
        redisUtil.del(dayVisitKey);
        redisUtil.del(foreverLimitKey);
    }

    /**
     * 验证计数限制
     *
     * @return
     */
    public boolean chkCountOperation(String limitType, String source, String type, LimitCount limitCount, ResponseData resp) {
        if (StringUtil.isNullOrEmpty(type)) {
            type = "default";
        }
        if (HoAirIpUtil.filterIP(limitType, mobileApiConfig.IP_PATTERN)) {
            return true;
        }
        //频率限制
        FrequencyLimit timeLimit = limitCount.getLimitMin();
        if (timeLimit != null && timeLimit.getAccessLimit() > 0) {
            String minKey = RedisKeyUtil.getSmsDayVisitMinLimitKey(source, limitType, type);

            String visitCodeStr = (String) redisUtil.get(minKey);
            int visitCode = StringUtil.isNullOrEmpty(visitCodeStr) ? 0 : Integer.parseInt(visitCodeStr);
            if (visitCode > timeLimit.getAccessLimit()) {
                resp.setCode(WSEnum.ERROR.resultCode);
                resp.setMessage("访问过于频繁，请稍后再试！");
                return false;
            }
        }
        //每日访问次数
        FrequencyLimit dayLimit = limitCount.getLimitDay();
        if (dayLimit != null && dayLimit.getAccessLimit() > 0) {
            String dayKey = RedisKeyUtil.getSmsDayVisitKey(source, limitType, type);
            String visitCodeStr = (String) redisUtil.get(dayKey);
            int visitCode = StringUtil.isNullOrEmpty(visitCodeStr) ? 0 : Integer.parseInt(visitCodeStr);
            if (visitCode > dayLimit.getAccessLimit()) {
                resp.setCode(WSEnum.ERROR.resultCode);
                resp.setMessage("访问过于频繁，请明日再试！");
                return false;
            }
        }
        //永久访问次数
        FrequencyLimit foreverLimit = limitCount.getLimitForever();
        if (foreverLimit != null && foreverLimit.getAccessLimit() > 0) {
            String foreverFinalKey = RedisKeyUtil.getSmsForeverVisitKey(source, limitType, type);
            String visitCodeStr = (String) redisUtil.get(foreverFinalKey);
            int visitCode = StringUtil.isNullOrEmpty(visitCodeStr) ? 0 : Integer.parseInt(visitCodeStr);
            if (visitCode > 0) {
                resp.setCode(WSEnum.ERROR.resultCode);
                resp.setMessage("访问过于频繁！");
                return false;
            }
        }
        return true;
    }

    /**
     * 操作计数方式
     *
     * @param limitType  IP地址，设备，手机号等等
     * @param source
     * @param type
     * @param limitCount
     */
    public void countOperation(String limitType, String source, String type, LimitCount limitCount) {
        if (StringUtil.isNullOrEmpty(type)) {
            type = "default";
        }
        //频率限制
        FrequencyLimit timeLimit = limitCount.getLimitMin();
        if (timeLimit != null && timeLimit.getAccessLimit() > 0) {
            String minKey = RedisKeyUtil.getSmsDayVisitMinLimitKey(source, limitType, type);

            String visitCodeStr = (String) redisUtil.get(minKey);
            int visitCode = StringUtil.isNullOrEmpty(visitCodeStr) ? 0 : Integer.parseInt(visitCodeStr);
            redisUtil.set(minKey, String.valueOf(visitCode + 1), timeLimit.getFrequency());
        }

        //每日访问次数
        int visitCode = 0;
        FrequencyLimit dayLimit = limitCount.getLimitDay();
        if (dayLimit != null && dayLimit.getAccessLimit() > 0) {
            String dayKey = RedisKeyUtil.getSmsDayVisitKey(source, limitType, type);
            String visitCodeStr = (String) redisUtil.get(dayKey);
            visitCode = StringUtil.isNullOrEmpty(visitCodeStr) ? 0 : Integer.parseInt(visitCodeStr);
            visitCode++;
            redisUtil.set(dayKey, String.valueOf(visitCode), dayLimit.getFrequency());
        }
    }

    /**
     * 按天校验操作许可
     *
     * @param addDayLicense      true : 校验同步增加许可次数 false：只校验不增加许可测试
     * @param checkDayLicenseArr
     * @return
     */
    public void checkDayLicense(boolean addDayLicense, CheckDayLicense... checkDayLicenseArr) {
        for (CheckDayLicense checkDayLicense : checkDayLicenseArr) {
            if (null == checkDayLicense) {
                continue;
            }
            String key = checkDayLicense.getKey();
            String checkLicense = checkDayLicense.getCheckLicense();
            String message = checkDayLicense.getMessage();
            if (StringUtils.isAllBlank(key, checkLicense)) {
                log.info("按天校验操作许可校验不通过，参数校验不通过，key：{} 校验类型：{}", key, checkLicense);
                throw MultiLangServiceException.fail(CommonErrorCode.OVER_LIMIT, StringUtils.isBlank(message) ? "校验不通过" : message);
            }
            Integer licenseMaxCount = appConfig.getCheckLicenseMap().get(checkLicense);
            if (null == licenseMaxCount) {
                licenseMaxCount = 10;
            }
            // 获取当日日期并按规则生成key
            String data = DateUtil.dateToString(new Date(), DateUtil.YYYYMMDD_PATTERN);
            String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisConstantConfig.CHECK_LICENSE + data + ":" + checkLicense + ":" + key);
            long count = redisUtil.increment(redisKey, addDayLicense ? 1L : 0L, 25 * 60 * 60L);
            boolean checkFlag = count <= licenseMaxCount;
            if (!addDayLicense) {
                checkFlag = count < licenseMaxCount;
            }
            if (!checkFlag) {
                log.info("按天校验操作许可校验不通过，已达到上限，校验类型：{} key:{} redisKey：{} 许可次数：{} 当前次数：{}", checkLicense, key, redisKey, licenseMaxCount, count);
                //优先根据code码去返回错误描述提示
                if (StringUtils.isNotBlank(checkDayLicense.getErrorCode())) {
                    CommonErrorCode errorCode = null;
                    try {
                        errorCode = CommonErrorCode.valueOf(checkDayLicense.getErrorCode());
                    } catch (Exception e) {
                        log.error("{},枚举类型不匹配", checkDayLicense.getErrorCode(), e);
                    }
                    if (errorCode != null) {
                        throw MultiLangServiceException.fail(errorCode);
                    }
                }
                throw MultiLangServiceException.fail(CommonErrorCode.OVER_LIMIT, StringUtils.isBlank(message) ? "校验不通过" : message);
            }
        }
    }


    /**
     * 增加已许可次数
     *
     * @param checkDayLicenseArr
     */
    public void addDayLicense(CheckDayLicense... checkDayLicenseArr) {
        for (CheckDayLicense checkDayLicense : checkDayLicenseArr) {
            if (null == checkDayLicense) {
                continue;
            }
            String key = checkDayLicense.getKey();
            String checkLicense = checkDayLicense.getCheckLicense();
            if (StringUtils.isAnyBlank(key, checkLicense)) {
                continue;
            }
            // 获取当日日期并按规则生成key
            String data = DateUtil.dateToString(new Date(), DateUtil.YYYYMMDD_PATTERN);
            String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisConstantConfig.CHECK_LICENSE + data + ":" + checkLicense + ":" + key);
            redisUtil.increment(redisKey, 1L, 25 * 60 * 60L);
        }
    }

    /**
     * 增加已许可次数,并返回剩余次数
     *
     * @param checkDayLicense
     * @return long
     */
    public long addDayLicense(CheckDayLicense checkDayLicense) {
        String key = checkDayLicense.getKey();
        String checkLicense = checkDayLicense.getCheckLicense();
        Integer licenseMaxCount = appConfig.getCheckLicenseMap().get(checkLicense);
        if (null == licenseMaxCount) {
            licenseMaxCount = 10;
        }
        // 获取当日日期并按规则生成key
        String data = DateUtil.dateToString(new Date(), DateUtil.YYYYMMDD_PATTERN);
        String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisConstantConfig.CHECK_LICENSE + data + ":" + checkLicense + ":" + key);
        long count = redisUtil.increment(redisKey, 1L, 25 * 60 * 60L);
        return licenseMaxCount - count < 0 ? 0 : licenseMaxCount - count;
    }

    /**
     * @return captchaFuncEnum 验证码功能模块
     * @return language 语言
     * @description 根据模板号获取邮件模板内容
     * <AUTHOR>
     * @date 2024/10/30 16:22
     **/
    public EmailContent initEmailTemplate(CaptchaFuncEnum captchaFuncEnum, LanguageEnum language) {
        String templateCode = captchaFuncEnum.name() + "_" + language.name();
        Map<String, EmailContent> emailMap = appConfig.getEmailTemplateHash();
        if (emailMap == null) {
            log.error("emailTemplateMap未进行模板配置");
            throw MultiLangServiceException.fail("未进行邮箱模板参数配置");
        }
        EmailContent template = emailMap.get(templateCode);
        if (template == null) {
            log.error("{}未进行模板配置", templateCode);
            throw MultiLangServiceException.fail("未进行邮箱模板配置");
        }
        return template;
    }

    /***
     * 游客的下单情况下设置会员信息默认值
     * @param requestData
     */
    public static <T> void setFfpDefault(RequestData<T> requestData) {
        // 游客免登录下单，会员id默认值设置
        if (AirStringUtil.isBlank(requestData.getFfpId())) {
            requestData.setFfpId(FFP_DEFAULT_VAL);
        }
        // 游客免登录下单，会员卡号默认值设置
        if (AirStringUtil.isBlank(requestData.getFfpNo())) {
            requestData.setFfpNo(FFP_DEFAULT_VAL);
        }
    }
}
