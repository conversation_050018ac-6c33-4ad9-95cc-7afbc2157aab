package com.juneyaoair.oneorder.api.order.dto.coupon;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class BookResourceInfo {
    /**
     * 资源编号
     */
    private String ResourceId;
    /**
     * 资源持有信息编号
     */
    private String VendorMessageId;
    /**
     * 资源品类
     */
    private String ResourceType;
    /**
     * 资源名称
     */
    private String ResourceName;
    /**
     * 使用时间  yyyy-MM-dd HH:mm:ss
     */
    private String UseStartDate;
    private String UseEndDate;
    /**
     * 航程类型
     */
    private String FlightType;
    /**
     * 出发机场三字码
     */
    private String DepAirportCode;
    private String ArrAirportCode;
    /**
     * 起飞时间
     * yyyy-MM-dd HH:mm:ss
     */
    private String DepDateTime;
    private String ArrDateTime;
    private String FlightNo;
    private String Cabin;
    /**
     * 价种，份数
     */
    private ResourcePriceDetail[] ResourcePriceDetails;
    /**
     * 邮寄行程单信息
     */
    private MailTravelProduct MailTravelProduct;
    /**
     * 电话卡购买信息
     */
    private PhoneCardProduct PhoneCardProduct;
    /**
     * WIFI购买信息
     */
    private WifiProduct WifiProduct;
    /**
     * 签证信息
     */
    private VisaProduct VisaProduct;
    /**
     * 预约wifi信息
     */
    private PackageWifiParamProduct PackageWifiParamProduct;
}
