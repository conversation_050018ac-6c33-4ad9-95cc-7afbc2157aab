package com.juneyaoair.oneorder.api.pay.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/4 16:02
 */
@Data
@NoArgsConstructor
public class OrderTypeDesc {
    /**
     * 支付平台订单类型
     */
    private String orderType;
    /**
     * 商品主题
     */
    private String subject;
    /**
     * 商品描述
     */
    private String body;
    /**
     * 支付成功前端回调地址 非必填 不填写的情况下将返回默认页面地址
     */
    private String returnPage;
}
