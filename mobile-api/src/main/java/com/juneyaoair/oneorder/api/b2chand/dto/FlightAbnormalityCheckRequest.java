package com.juneyaoair.oneorder.api.b2chand.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2019/10/23  15:55.
 */
@Data
public class FlightAbnormalityCheckRequest {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String Code;

    public FlightAbnormalityCheckRequest(String version, String channelCode, String userNo, String code){
        this.Version = version;
        this.ChannelCode = channelCode;
        this.UserNo = userNo;
        this.Code = code;
    }
}
