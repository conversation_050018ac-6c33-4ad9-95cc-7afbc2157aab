package com.juneyaoair.oneorder.api.order.dto.coupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: caolei
 * @Description: 调用新的订单详情接口（/Order/BasicGetCouponOrder）返回结果
 * @Date: 2021/8/12 14:07
 * @Modified by:
 */
@Data
public class NewBasicOrderDetailResponse {
    @ApiModelProperty(value = "订单编号")
    private String OrderNo;
    @ApiModelProperty(value = "渠道订单编号")
    private String ChannelOrderNo;
    @ApiModelProperty(value = "渠道客户编号")
    private String ChannelCustomerNo;
    @ApiModelProperty(value = "订单状态")
    private String OrderState;
    @ApiModelProperty(value = "订单创建时间")
    private String OrderCreateDatetime;
    @ApiModelProperty(value = "支付状态")
    private String PayState;
    @ApiModelProperty(value = "客户id")
    private Long CustomerId;
    @ApiModelProperty(value = "客户姓名")
    private String CustomerName;
    @ApiModelProperty(value = "订单总金额")
    private String OrderTotalAmount;
    @ApiModelProperty(value = "订单创建请求ip地址")
    private String OrderRequestIp;
    @ApiModelProperty(value = "国际码")
    private String PhoneCountryCode;
    @ApiModelProperty(value = "通电电话")
    private String ContactTelphone;
    @ApiModelProperty(value = "订单增送积分总计")
    private String FfpGiftTotalScore;
    @ApiModelProperty(value = "订单使用积分总计")
    private String FfpUseTotalScore;
    @ApiModelProperty(value = "币种")
    private String Currency;
    @ApiModelProperty(value = "优惠劵金额合计")
    private String CouponAmountSum;
    @ApiModelProperty(value = "联合订单id")
    private String OrderIdUnion;
    @ApiModelProperty(value = "指定网关号")
    private String OnlyGatewayNo;
    @ApiModelProperty(value = "网关优惠金额")
    private String GatewayReductionSum;
    @ApiModelProperty(value = "子订单类型")
    private String SubOrderType;
    @ApiModelProperty(value = "取消原因")
    private String Reason;
    @ApiModelProperty(value = "辅营订单类型")
    private String CouponOrderSource;
    @ApiModelProperty(value = "支付方式")
    private String PayMethod;
    @ApiModelProperty(value = "支付时间")
    private String PaidDateTime;
    @ApiModelProperty(value = "支付超时时间/单位（分钟）")
    private Integer PayDatetimeLimit;
    @ApiModelProperty(value = "是否内部渠道")
    private String IsSelf;
    @ApiModelProperty(value = "子订单信息")
    private List<NewBasicCouponOrderDetail> BasicCouponOrderDtoList;
}