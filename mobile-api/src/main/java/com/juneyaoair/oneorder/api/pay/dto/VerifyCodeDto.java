package com.juneyaoair.oneorder.api.pay.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName VerifyCodeDto
 * @Description
 * <AUTHOR>
 * @Date 2024/4/18 10:22
 * @Version 1.0
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "VerifyCodeDto", description = "支付平台验证码发送-请求参数")
public class VerifyCodeDto {
    /**
     * <AUTHOR>
     * @Description 接口版本号10
     * @Date 10:24 2024/4/18
     **/
    private String version;

    /**
     * <AUTHOR>
     * @Description 渠道用户号B2C, CC等
     * @Date 10:24 2024/4/18
     **/
    private String channelNo;

    /**
     * <AUTHOR>
     * @Description 在业务系统中选择使用的支付网关。该字段为空时将会在支付平台进行支付网关选择
     * @Date 10:24 2024/4/18
     **/
    private String gatewayNo;

    /**
     * <AUTHOR>
     * @Description 支付金额, 一千元表示为：1000.00。
     * @Date 10:24 2024/4/18
     **/
    private String amount;

    /**
     * <AUTHOR>
     * @Description 银行卡号
     * @Date 10:24 2024/4/18
     **/
    private String cardNo;

    /**
     * <AUTHOR>
     * @Description 银行卡有效期
     * @Date 10:24 2024/4/18
     **/
    private String expiryDate;

    /**
     * <AUTHOR>
     * @Description 交易日期 格式：yyyy-MM-dd
     * @Date 10:24 2024/4/18
     **/
    private String transDate;

    /**
     * <AUTHOR>
     * @Description 动态参数, JSON数据格式。可以用于传输第三方支付网关需要的特殊参数或前端才有的动态参数等等
     * @Date 10:24 2024/4/18
     **/
    protected String dynamicParameters;

    /**
     * <AUTHOR>
     * @Description 支付网关类型, 1 － 直联银行，2 － 第三方网关，3 －预付费卡,4 – 无磁无密
     * @Date 10:24 2024/4/18
     **/
    private String gatewayType;

    /**
     * <AUTHOR>
     * @Description 签名
     * @Date 10:24 2024/4/18
     **/
    private String chkValue;

    public String toCatchVerifyCodeParaSHA1Str() {
        return version + channelNo + gatewayNo + cardNo + expiryDate + transDate + amount;
    }

    public Map<String, String> toGenerateVerifySendMap() {
        Map<String, String> parametersMap = new HashMap<>();
        //1版本号
        parametersMap.put("Version", this.getVersion());
        //2渠道用户
        parametersMap.put("ChannelNo", this.getChannelNo());
        //3支付网关
        parametersMap.put("GatewayNo", this.getGatewayNo());
        //4支付金额
        parametersMap.put("Amount", this.getAmount());
        //5银行卡号
        parametersMap.put("CardNo", this.getCardNo());
        //6银行卡有效期
        parametersMap.put("ExpiryDate", this.getExpiryDate());
        //7交易日期
        parametersMap.put("TransDate", this.getTransDate());
        //8动态参数
        parametersMap.put("DynamicParameters", this.getDynamicParameters());
        //9网关类型
        parametersMap.put("GatewayType", this.getGatewayType());
        return parametersMap;
    }
}
