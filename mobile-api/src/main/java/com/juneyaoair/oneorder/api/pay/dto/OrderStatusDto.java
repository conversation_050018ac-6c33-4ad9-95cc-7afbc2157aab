package com.juneyaoair.oneorder.api.pay.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/18 16:13
 */
@ApiModel(value = "OrderStatusDto",description = "订单状态返回")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderStatusDto {
    @ApiModelProperty("渠道订单编号")
    private String channelOrderNo;
    @ApiModelProperty("原渠道号")
    private String originChannelCode;
    @ApiModelProperty("订单编号")
    private String orderNo;
    @ApiModelProperty("机票订单状态")
    private String state;
    @ApiModelProperty("机票订单支付状态")
    private String payState;
    @ApiModelProperty("订单出票状态")
    private String orderState;
    @ApiModelProperty("机票订单创建时间")
    private String createDatetime;
    @ApiModelProperty("机票订单支付时间")
    private String paidDatetime;
    @ApiModelProperty("订单金额")
    private BigDecimal amount;
    @ApiModelProperty("订单支付方式")
    private String payMethod;
    @ApiModelProperty("联系人")
    private String linker;
    @ApiModelProperty("联系人电话")
    private String linkerHandPhone;
}
