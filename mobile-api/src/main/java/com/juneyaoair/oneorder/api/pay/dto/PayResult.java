package com.juneyaoair.oneorder.api.pay.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/29 9:19
 */
@ApiModel(value = "PayResult",description = "支付响应")
@Data
@Builder
public class PayResult {
    @ApiModelProperty(value = "支付文档类型",allowableValues = "form,json,http",notes = "form表示;html表单提交;http表示url地址")
    private String docType;
    @ApiModelProperty(value = "支付文档")
    private Object result;
}
