package com.juneyaoair.oneorder.api.pay.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/28 8:53
 */
@ApiModel(value = "PayMethodDto",description = "支付方式说明")
@Data
public class PayMethodDto {
    @ApiModelProperty(value = "支付方式列表")
    private List<Bank> bankList;
}
