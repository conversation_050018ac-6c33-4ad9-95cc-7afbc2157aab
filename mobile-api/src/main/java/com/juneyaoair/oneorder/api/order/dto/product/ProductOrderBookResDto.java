package com.juneyaoair.oneorder.api.order.dto.product;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "产品下单返回")
public class ProductOrderBookResDto {
    @ApiModelProperty(value="订单号")
    private String orderNo;

    @ApiModelProperty(value="订单ID")
    private Long orderId;

    @ApiModelProperty(value = "渠道订单号")
    private String channelOrderNo;

    @ApiModelProperty(value="创建时间")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private String createDate;

    @ApiModelProperty(value="权益列表")
    private List<EquityItemDto> equityItemList;
}
