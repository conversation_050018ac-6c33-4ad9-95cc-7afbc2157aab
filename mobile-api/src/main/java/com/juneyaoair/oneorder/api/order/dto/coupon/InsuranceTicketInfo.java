package com.juneyaoair.oneorder.api.order.dto.coupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: caolei
 * @Description: 客票信息
 * @Date: 2025/01/01 13:52
 * @Modified by:
 */
@Data
public class InsuranceTicketInfo {

    @NotBlank(message = "票号不能为空")
    @ApiModelProperty(value = "票号", required = true)
    private String TicketNo;

    @NotBlank(message = "姓名不能为空")
    @ApiModelProperty(value = "姓名", required = true)
    private String PassengerName;

}
