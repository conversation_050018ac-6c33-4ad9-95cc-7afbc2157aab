package com.juneyaoair.oneorder.api.order.dto.coupon;

import com.juneyaoair.flightbasic.common.UserInfoNoMust;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: caolei
 * @Description: 调用新的订单详情接口（/Order/BasicGetCouponOrder）请求参数
 * @Date: 2021/8/12 13:52
 * @Modified by:
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class NewCouponOrderDetailReq extends UserInfoNoMust {
    @ApiModelProperty(value = "接口版本号", hidden = true, required = true)
    private String Version;
    @ApiModelProperty(value = "渠道用户号", hidden = true, required = true)
    private String ChannelCode;
    @ApiModelProperty(value = "渠道工作人员号", hidden = true, required = true)
    private String UserNo;
    @ApiModelProperty(value = "IP地址", hidden = true, required = true)
    private String RequestIp;
    @NotNull(message = "订单号不能为空")
    @ApiModelProperty(value = "订单号", required = true)
    private String OrderNo;
    @ApiModelProperty(value = "渠道订单号")
    private String ChannelOrderNo;
    @ApiModelProperty(value = "查询的权益券码")
    private List<String> CouponCodeList;
}