package com.juneyaoair.oneorder.api.geetest.sdk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Map;

/**
 * sdk lib包的返回结果信息。
 *
 * <AUTHOR>
 */
@ApiModel(value = "GeetestLibResult",description = "极验初始化结果")
public class GeetestLibResult {
    /**
     * 成功失败的标识码，1表示成功，0表示失败
     */
    @ApiModelProperty(value = "成功失败的标识码",notes = "1表示成功，0表示失败")
    private int status = 0;

    /**
     * 返回数据，Map格式
     */
    @ApiModelProperty(value = "返回数据",notes = "Map格式")
    private Map<String, Object> data;

    /**
     * 备注信息，如异常信息等
     */
    @ApiModelProperty(value = "备注信息",notes = "如异常信息等")
    private String msg = "";

    public void setStatus(int status) {
        this.status = status;
    }

    public int getStatus() {
        return status;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getMsg() {
        return msg;
    }

    public void setAll(int status, Map<String, Object> data, String msg) {
        this.setStatus(status);
        this.setData(data);
        this.setMsg(msg);
    }

    @Override
    public String toString() {
        return String.format("GeetestLibResult{status=%s, data=%s, msg=%s}", this.status, this.data, this.msg);
    }
}
