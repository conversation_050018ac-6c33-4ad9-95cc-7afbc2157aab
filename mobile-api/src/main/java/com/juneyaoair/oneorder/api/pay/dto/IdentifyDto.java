package com.juneyaoair.oneorder.api.pay.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName IdentifyDto
 * @Description
 * <AUTHOR>
 * @Date 2024/5/27 10:45
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PaymentDto", description = "支付平台身份验证/签约请求参数")
public class IdentifyDto {

    @ApiModelProperty(value = "接口版本号")
    private String version;

    @ApiModelProperty(value = "渠道用户号")
    private String channelNo;

    @ApiModelProperty(value = "在业务系统中选择使用的支付网关。该字段为空时将会在支付平台进行支付网关选择")
    private String gatewayNo;

    @ApiModelProperty(value = "动态参数,JSON数据格式。可以用于传输第三方支付网关需要的特殊参数或前端才有的动态参数等等")
    protected String dynamicParameters;

    @ApiModelProperty(value = "签名")
    private String chkValue;

    @ApiModelProperty(value = "信用卡信息")
    private String cardInfo;

    public String toGeneIdentifyParaSHA1Str() {
        return version + channelNo + gatewayNo + dynamicParameters;
    }

    public Map<String, String> toGeneIdentifyMap() {
        Map<String, String> parametersMap = new HashMap<>();
        //1版本号
        parametersMap.put("Version", this.getVersion());
        //2渠道用户
        parametersMap.put("ChannelNo", this.getChannelNo());
        //3支付网关
        parametersMap.put("GatewayNo", this.getGatewayNo());
        //4动态参数设置
        parametersMap.put("DynamicParameters", this.getDynamicParameters());
        return parametersMap;
    }


}
