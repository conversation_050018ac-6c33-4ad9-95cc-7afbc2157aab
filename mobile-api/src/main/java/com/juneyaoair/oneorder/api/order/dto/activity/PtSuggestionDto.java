package com.juneyaoair.oneorder.api.order.dto.activity;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class PtSuggestionDto {

    @SerializedName("Version")
    private String version;

    @SerializedName("ChannelCode")
    private String channelCode;

    @SerializedName("UserNo")
    private String userNo;
    /**
     * 航班号
     */
    @SerializedName("FlightNo")
    private String flightNo;

    /**
     * 航班日期
     */
    @SerializedName("flightDate")
    private String flightDate;

    /**
     * 类型
     * 1:表扬,2:投诉,3:意见建议
     */
    @SerializedName("type")
    private String type;

    /**
     * 旅客姓名
     */
    @SerializedName("passName")
    private String passName;

    /**
     * 旅客证件号
     */
    @SerializedName("passIdCard")
    private String passIdCard;

    /**
     * 旅客联系方式
     */
    @SerializedName("passContact")
    private String passContact;

    /**
     * 内容
     */
    @SerializedName("content")
    private String content;

    /**
     * 附件地址
     * 多个附件地址用逗号隔开
     */
    @SerializedName("url")
    private String url;

    @SerializedName("FfpCardNo")
    private String ffpCardNo;

    @SerializedName("FfpId")
    private String ffpId;
}
