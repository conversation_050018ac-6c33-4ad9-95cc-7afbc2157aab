package com.juneyaoair.oneorder.api.crm.service;

import com.juneyaoair.flightbasic.request.crm.ModifyCustomerInfoRequest;
import com.juneyaoair.flightbasic.request.crm.PtResetLoginPasswordRequest;
import com.juneyaoair.flightbasic.response.crm.AddMemberAddressResponse;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.crm.dto.PtApiCRMRequest;
import com.juneyaoair.oneorder.crm.dto.PtCRMResponse;
import com.juneyaoair.oneorder.crm.dto.address.*;
import com.juneyaoair.oneorder.crm.dto.basedata.BaseDataQueryReqDto;
import com.juneyaoair.oneorder.crm.dto.basedata.BaseDataQueryResDto;
import com.juneyaoair.oneorder.crm.dto.common.GeneralContactInfo;
import com.juneyaoair.oneorder.crm.dto.common.MemberLevelDTOView;
import com.juneyaoair.oneorder.crm.dto.common.PtCrmMileageResponse;
import com.juneyaoair.oneorder.crm.dto.request.*;
import com.juneyaoair.oneorder.crm.dto.response.*;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.order.dto.*;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.tools.utils.dto.CrmPhoneInfo;

import java.util.List;
import java.util.Map;


/**
 * @ClassName IMemberService
 * @Description 会员服务
 * <AUTHOR>
 * @Date 2023/6/20 9:01
 * @Version 1.0
 */
public interface IMemberService {

    /**
     * 基于会员卡号查询会员信息
     * @param channelCode
     * @param ffpCardNo
     * @param items
     * @return
     */
    PtMemberDetail memberDetail(String channelCode, String ffpCardNo, String[] items);

    /**
     * @param ptApiCRMRequest
     * @param useCache        是否使用缓存
     * @return PtCRMResponse<PtMemberDetail>
     * <AUTHOR>
     * @Description 会员详情查询
     * @Date 9:03 2023/6/20
     **/
    PtCRMResponse<PtMemberDetail> memberDetail(PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest, boolean useCache);

    /**
     * 会员积分查询（无需实名）
     *
     * @param ptApiCRMRequest
     * @return 返回总积分，冻结积分
     */
    MemberRemainScoreResp queryMemberRemainScore(PtApiCRMRequest<MileageAccountQueryRequest> ptApiCRMRequest, boolean useCache);

    /**
     * 积分账户查询
     *
     * @param ptApiCRMRequest
     * @return
     */
    PtCRMResponse<MileageAccountQueryResponse> mileageAccountQuery(PtApiCRMRequest<MileageAccountQueryRequest> ptApiCRMRequest, boolean useCache);

    /**
     * @param memberId
     * @param ip
     * @param channelCode
     * @return PtCrmMileageResponse<MemberSegmentResponse>
     * <AUTHOR>
     * @Description 获取会员总航段数
     * @Date 9:51 2023/6/20
     **/
    PtCrmMileageResponse<MemberSegmentResponse> queryMemberSegment(String memberId, String ip, String channelCode, boolean useCache);

    /**
     * 查询会员星级 2021-12-27
     *
     * @param ptApiCRMRequest
     * @return
     */
    PtCRMResponse<MemberStarQueryResp> queryMemberStar(PtApiCRMRequest<MemberStarQueryRequest> ptApiCRMRequest, boolean useCache);

    /**
     * 查询会员标签
     *
     * @param memberTagQueryReqDto
     * @return
     */
    CrmMemberBaseApiResponse<MemberTagQueryResDto> queryMemberTag(CrmMemberBaseApiRequest<MemberTagQueryReqDto> memberTagQueryReqDto, boolean useCache);

    /**
     * 查询会员星级规则 2021-12-28
     *
     * @param ptApiCRMRequest
     * @return
     */
    PtCRMResponse<MemberStarQueryRulePage> queryMemberStarRule(PtApiCRMRequest<MemberStarQueryRuleReq> ptApiCRMRequest, boolean useCache);

    /**
     * @param queryCertificateRequest
     * @param useCache                是否使用缓存
     * @return com.juneyaoair.oneorder.crm.dto.response.CrmMemberBaseApiResponse<com.juneyaoair.oneorder.order.dto.QueryMemberCertificateResDto>
     * <AUTHOR>
     * @Description 查询用户证件信息
     * @Date 2023/6/26
     **/
    CrmMemberBaseApiResponse<QueryMemberCertificateResDto> toCatchMemberCertList(CrmMemberBaseApiRequest<QueryMemberCertificateReqDto> queryCertificateRequest, boolean useCache);

    /**
     * @param compensateReq
     * @return com.juneyaoair.oneorder.crm.dto.response.CrmMemberBaseApiResponse<java.lang.Boolean>
     * <AUTHOR>
     * @Description 积分补登
     * @Date 16:20 2023/6/27
     **/
    void toCompensateScore(CrmMemberBaseApiRequest<PtScoreCompensateReq> compensateReq);

    /**
     * @param ffpId
     * @return com.juneyaoair.oneorder.crm.dto.response.MileageRetroRecordQueryResDto
     * <AUTHOR>
     * @Description 积分补登列表查询
     * @Date 8:24 2023/9/12
     **/
    MileageRetroRecordQueryResDto toCatchRetroRecords(String ffpId,String channelNo);

    /**
     * @param mileageExpireActivityQueryReqDto
     * @return com.juneyaoair.oneorder.crm.dto.response.MileageExpireActivityQueryResDto
     * <AUTHOR>
     * @Description 新版本查询即将失效积分（CRM新接口）
     * @Date 14:33 2023/7/12
     **/
    MileageEffectiveResDto toCatchExpiringScoreV2(PtCrmMileageRequest<MileageEffectiveReqDto> mileageExpireActivityQueryReqDto);


    /**
     * @param queryGeneralContactRequest
     * @return com.juneyaoair.oneorder.crm.dto.response.QueryGeneralContactResponse
     * <AUTHOR>
     * @Description 查询常用乘机人
     * @Date 9:21 2023/7/3
     **/
    QueryGeneralContactResponse toCatchGeneralContacts(QueryGeneralContactRequest queryGeneralContactRequest);
    /**
     * @description  查询乘机人列表
     * <AUTHOR>
     * @date 2024/12/14 14:01
     * @param channelInfo
     * @param ffpId
     * @param ffpCardNo
     * @return List<GeneralContactInfo>
     **/
    List<GeneralContactInfo> toCatchGeneralContactList(ChannelInfo channelInfo, String ffpId, String ffpCardNo);


    /**
     * @param ptCrmMileageRequest
     * @param ip
     * @return java.util.List<com.juneyaoair.oneorder.order.dto.MemberBeneficiaryDTO>
     * <AUTHOR>
     * @Description 受益人信息查询
     * @Date 16:16 2023/7/3
     **/
    List<MemberBeneficiaryDTO> listEffectiveBeneficiaryInfoRecord(PtCrmMileageRequest ptCrmMileageRequest, String ip);

    List<MemberBeneficiaryDTO> listBeneficiaryInfoRecord(PtCrmMileageRequest ptCrmMileageRequest, String ip);

    /**
     * @param ffpCard
     * @param ip
     * @param channelCode
     * @return java.util.List<com.juneyaoair.oneorder.order.dto.CommonContactsInfo>
     * <AUTHOR>
     * @Description 查询常用联系人
     * @Date 11:08 2023/7/4
     **/
    List<CommonContactsInfo> toCatchCommonContacts(String ffpCard, String ip, String channelCode, String channelPwd);

    /**
     * @param ptCrmMileageRequest
     * @return java.util.List<com.juneyaoair.oneorder.order.dto.CommonContactsInfo>
     * <AUTHOR>
     * @Description 添加常用联系人
     * @Date 11:08 2023/7/4
     **/
    PtCrmMileageResponse<Integer> toAddCommonContacts(PtCrmMileageRequest ptCrmMileageRequest);

    /**
     * @param ptCrmMileageRequest
     * @return com.juneyaoair.oneorder.crm.dto.common.PtCrmMileageResponse<java.lang.Boolean>
     * <AUTHOR>
     * @Description 修改常用联系人
     * @Date 14:46 2023/7/4
     **/
    PtCrmMileageResponse<Boolean> toModifyCommonContacts(PtCrmMileageRequest ptCrmMileageRequest);

    /**
     * @param ptCrmMileageRequest
     * @return com.juneyaoair.oneorder.crm.dto.common.PtCrmMileageResponse<java.lang.Boolean>
     * <AUTHOR>
     * @Description 删除单个常用联系人
     * @Date 15:03 2023/7/4
     **/
    PtCrmMileageResponse<Boolean> toDeleteCommonContacts(PtCrmMileageRequest ptCrmMileageRequest);

    /**
     * @param ptCrmMileageRequest
     * @return com.juneyaoair.oneorder.crm.dto.common.PtCrmMileageResponse<java.util.Map < java.lang.String, java.lang.Integer>>
     * <AUTHOR>
     * @Description 获取草稿状态和激活状态的受益人信息数量
     * @Date 16:05 2023/7/4
     **/
    PtCrmMileageResponse<Map<String, Integer>> toCountBeneficiaryNumByMemberId(PtCrmMileageRequest ptCrmMileageRequest);

    /**
     * @param ptCrmMileageRequest
     * <AUTHOR>
     * @Description 新增受益人
     * @Date 16:19 2023/7/4
     **/
    Integer toAddBeneficiary(PtCrmMileageRequest ptCrmMileageRequest);

    /**
     * @param ptCrmMileageRequest
     * @return java.lang.Integer
     * <AUTHOR>
     * @Description 激活受益人
     * @Date 10:04 2023/9/10
     **/
    void toActiveBeneficiary(PtCrmMileageRequest ptCrmMileageRequest);

    /**
     * @param ptCrmMileageRequest
     * <AUTHOR>
     * @Description 修改受益人
     * @Date 8:44 2023/7/5
     **/
    void toModifyBeneficiary(PtCrmMileageRequest ptCrmMileageRequest);

    /**
     * @param ptCrmMileageRequest
     * <AUTHOR>
     * @Description 删除受益人
     * @Date 8:51 2023/7/5
     **/
    void toDeleteBeneficiary(PtCrmMileageRequest ptCrmMileageRequest);

    /**
     * @param ptApiCRMRequest
     * @return void
     * <AUTHOR>
     * @Description 保存用户基本信息
     * @Date 15:24 2023/7/5
     **/
    void toSaveCusBasicInfo(PtApiCRMRequest<PtModifyCustomerInfoRequest> ptApiCRMRequest);

    /**
     * @param ptApiRequest
     * @return com.juneyaoair.oneorder.crm.dto.PtCRMResponse
     * <AUTHOR>
     * @Description 会员关键信息调整(上传证件照)
     * @Date 15:31 2023/7/5
     **/
    PtCRMResponse applyMemberKeyInfo(PtApiCRMRequest<MemberKeyInfoReq> ptApiRequest);

    /**
     * @param ptApiCRMRequest
     * @return com.juneyaoair.oneorder.crm.dto.PtCRMResponse
     * <AUTHOR>
     * @Description 修改用户非关键信息
     * @Date 16:44 2023/7/5
     **/
    PtCRMResponse modifyCustomerInfo(PtApiCRMRequest<ModifyCustomerInfoRequest> ptApiCRMRequest);

    /**
     * @param ptApiRequest
     * @return com.juneyaoair.oneorder.crm.dto.PtCRMResponse
     * <AUTHOR>
     * @Description 新增会员地址
     * @Date 10:38 2023/7/6
     **/
    PtCRMResponse<AddMemberAddressResponse> addCustomerAddress(PtApiCRMRequest<MemberAddressAdd> ptApiRequest);

    /**
     * @param ptApiRequest
     * @return com.juneyaoair.oneorder.crm.dto.PtCRMResponse
     * <AUTHOR>
     * @Description 修改会员地址
     * @Date 10:42 2023/7/6
     **/
    PtCRMResponse modifyCustomerAddress(PtApiCRMRequest<PtMemberAddressReq> ptApiRequest);

    /**
     * 删除证件信息
     *
     * @param ptApiRequest
     * @return
     */
    PtCRMResponse deleteCertificate(PtApiCRMRequest<PtMemberCertificateDelRequest> ptApiRequest);

    /**
     * 添加证件
     *
     * @param addCertificateRequest
     * @return
     */
    CrmMemberBaseApiResponse<MemberCertificateResDto> addMemberCertificate(CrmMemberBaseApiRequest<MemberCertificateReqDto> addCertificateRequest);

    /**
     * 修改证件
     *
     * @param modifyCertificateRequest
     * @return
     */
    CrmMemberBaseApiResponse<MemberCertificateResDto> modifyMemberCertificate(CrmMemberBaseApiRequest<ModifyMemberCertificateReqDto> modifyCertificateRequest);

    /**
     * @param req
     * @return com.juneyaoair.oneorder.crm.dto.PtCRMResponse
     * <AUTHOR>
     * @Description 添加联系方式（邮箱/手机号登）
     * @Date 16:20 2023/7/7
     **/
    PtCRMResponse addContact(PtApiCRMRequest<PtContactInformationReq> req);


    /**
     * <AUTHOR>
     * @Description 修改用户联系方式（新版 后续推荐用此方式修改用户联系信息）
     * @Date 9:52 2024/5/10
     * @param request
     * @return void
     **/
    void toModifyMemberContactInfo(CrmMemberBaseApiRequest<ModifyMemberContactReqDto> request);


    /**
     * @param req
     * @return com.juneyaoair.oneorder.crm.dto.PtCRMResponse
     * <AUTHOR>
     * @Description 删除联系信息
     * @Date 15:52 2023/7/10
     **/
    PtCRMResponse deleteContactInformation(PtApiCRMRequest<UnbingingEmailReq> req);


    /**
     * @param ptApiCRMRequest
     * @return com.juneyaoair.oneorder.crm.dto.PtCRMResponse
     * <AUTHOR>
     * @Description 重置会员登录密码
     * @Date 16:11 2023/7/10
     **/
    PtCRMResponse resetLoginPassword(PtApiCRMRequest<PtResetLoginPasswordRequest> ptApiCRMRequest);


    /**
     * @param ptApiCRMRequest
     * @return com.juneyaoair.oneorder.crm.dto.PtCRMResponse
     * <AUTHOR>
     * @Description 修改会员登录密码
     * @Date 16:19 2023/7/10
     **/
    PtCRMResponse modifyLoginPassword(PtApiCRMRequest<ModifyLoginPwd> ptApiCRMRequest);


    /**
     * @param ptApiCRMRequest
     * @return com.juneyaoair.oneorder.crm.dto.PtCRMResponse
     * <AUTHOR>
     * @Description 设置消费密码
     * @Date 9:04 2023/7/11
     **/
    PtCRMResponse setConsumePassword(PtApiCRMRequest<PtSetConsumePasswordRequest> ptApiCRMRequest);

    /**
     * @param ptApiCRMRequest
     * @return com.juneyaoair.oneorder.crm.dto.PtCRMResponse
     * <AUTHOR>
     * @Description 修改消费密码
     * @Date 9:35 2023/7/11
     **/
    PtCRMResponse modifyConsumePassword(PtApiCRMRequest<PtResetConsumerPasswordRequest> ptApiCRMRequest);

    /**
     * @param couponQueryRequest
     * @return com.juneyaoair.oneorder.crm.dto.response.AvailCouponsResponse
     * <AUTHOR>
     * @Description 获取优惠券
     * @Date 10:00 2023/7/14
     **/
    AvailCouponsResponse toCatchCoupons(CouponQueryRequest couponQueryRequest);

    /**
     * @param ptCouponProductGetRequestDto
     * @return com.juneyaoair.oneorder.order.dto.PtCouponProductGetRequestDto
     * <AUTHOR>
     * @Description 查询权益券
     * @Date 10:19 2023/7/14
     **/
    PtCouponProductGetResponseDto toCatchRightCoupons(PtCouponProductGetRequestDto ptCouponProductGetRequestDto);

    /**
     * @param memberUpGradeRequest
     * @return com.juneyaoair.oneorder.crm.dto.response.MemberLevelInfoResp
     * <AUTHOR>
     * @Description 会员升级规则查询
     * @Date 16:01 2023/7/15
     **/
    @Deprecated
    MemberLevelInfoResp toCatchUpRule(MemberUpGradeRequest memberUpGradeRequest);

    /**
     * @param ptApiCRMRequest
     * @return com.juneyaoair.oneorder.crm.dto.PtCRMResponse<MemberRightsQueryResponse>
     * <AUTHOR>
     * @Description 查询用户权益(DataSource : CRM)
     * @Date 20:25 2023/7/15
     **/
    PtCRMResponse<MemberRightsQueryResponse> queryMemberRights(PtApiCRMRequest ptApiCRMRequest);

    /**
     * @param couponActivityReq
     * @return com.juneyaoair.oneorder.crm.dto.response.CouponActivityResp
     * <AUTHOR>
     * @Description 获取用户优惠券
     * @Date 20:50 2023/7/15
     **/
    CouponActivityResp toCatchCoupons(CouponActivityReq couponActivityReq);

    /**
     * @param couponActivityReq
     * @return com.juneyaoair.oneorder.crm.dto.response.CouponResp
     * <AUTHOR>
     * @Description 获取用户权益券
     * @Date 21:12 2023/7/15
     **/
    CouponResp toCatchRightCoupons(CouponActivityReq couponActivityReq);

    /**
     * @param companyMemberQueryInfoReq
     * @return com.juneyaoair.oneorder.crm.dto.common.PtCrmMileageResponse<com.juneyaoair.oneorder.crm.dto.response.CompanyMemberQueryInfoResDto>
     * <AUTHOR>
     * @Description 获取企业会员认证信息
     * @Date 21:46 2023/7/15
     **/
    PtCrmMileageResponse<CompanyMemberQueryInfoResDto> toCatchCompanyInfo(PtCrmMileageRequest<CompanyMemberQueryInfoReqDto> companyMemberQueryInfoReq);

    /**
     * 2023 7 27 新版的新增会员地址信息信息
     **/
    CrmMemberBaseApiResponse<AddMemberAddressResDto> memberAddressAdd(CrmMemberBaseApiRequest<AddMemberAddressReqDto> companyMemberQueryInfoReq);

    /**
     * 2023 7 27 新版的删除会员地址信息信息
     **/
    CrmMemberBaseApiResponse<DeleteMemberAddressResDto> memberAddressDelete(CrmMemberBaseApiRequest<DeleteMemberAddressReqDto> companyMemberQueryInfoReq);

    /**
     * 2023 7 27 新版的更新会员地址信息信息
     **/
    CrmMemberBaseApiResponse<ModifyMemberAddressResDto> memberAddressModify(CrmMemberBaseApiRequest<ModifyMemberAddressReqDto> req);

    /**
     * 2023 7 27 新版的查询基础信息（行政区）
     **/
    CrmMemberBaseApiResponse<BaseDataQueryResDto> baseDataQuery(CrmMemberBaseApiRequest<BaseDataQueryReqDto> req);

    /**
     * @param bizDto
     * @return java.util.List<com.juneyaoair.oneorder.crm.dto.common.MemberLevelDTO>
     * <AUTHOR>
     * @Description 获取用户权益 （flightBasic 的feign接口调用）
     * @Date 14:21 2023/8/8
     **/
    List<MemberLevelDTOView> toQueryMemberRights(BizDto bizDto);

    /**
     * @param requestData
     * @return com.juneyaoair.oneorder.crm.dto.response.AvailCouponsResponse
     * <AUTHOR>
     * @Description 获取账户中的优惠券
     * @Date 14:42 2023/9/1
     **/
    AvailCouponsResponse toCatchNormalCoupons(RequestData<QueryCouponReq> requestData);

    /**
     * @param requestData
     * @return com.juneyaoair.oneorder.crm.dto.response.PtCouponProductGetResponseDto
     * <AUTHOR>
     * @Description 获取账户中的权益券
     * @Date 17:54 2023/9/1
     **/
    PtCouponProductGetResponseDto toCatchRightCoupons(RequestData<MyCoupon> requestData);


    /**
     * @param startDate
     * @param endDate
     * @param ffpId
     * @param channelNo
     * @return com.juneyaoair.oneorder.crm.dto.response.MemberMilesResponse
     * <AUTHOR>
     * @Description 查询积分交易明细
     * @Date 19:58 2023/9/10
     **/
    PtMemberMilesResponse toCatchScoreChangeDetail(String startDate, String endDate, String ffpId, String channelNo);


    /**
     * @param startDate
     * @param endDate
     * @param ffpId
     * @param channelNo
     * @return com.juneyaoair.oneorder.crm.dto.PtCRMResponse<com.juneyaoair.oneorder.crm.dto.response.MileageDetailSegmentQueryResp>
     * <AUTHOR>
     * @Description 累计航段明细查询
     * @Date 18:30 2023/9/11
     **/
    PtCRMResponse<MileageDetailSegmentQueryResp> toCatchSegmentDetail(String startDate, String endDate, String ffpId, String channelNo);

    /**
     * @param ffpId       会员ID
     * @param authCode    支付宝授权码
     * @param channelCode
     * @param channelPwd
     * @param ip
     * @return com.juneyaoair.oneorder.crm.dto.PtCRMResponse
     * <AUTHOR>
     * @Description 支付宝实名认证
     * @Date 15:02 2023/9/20
     **/
    PtCRMResponse toTakeAliPayAuth(String ffpId, String authCode, String channelCode, String channelPwd, String ip);

    /**
     * @param ffpCardNo
     * @param channelCode
     * @return com.juneyaoair.oneorder.crm.dto.response.CrmPhoneInfo
     * <AUTHOR>
     * @Description 获取会员的手机信息
     * @Date 10:54 2024/7/24
     **/
    CrmPhoneInfo toCatchMobileInfo(String ffpCardNo, String channelCode);

    /**
     * 校验会员消费密码
     * @param ffpId
     * @param payPassword
     */
    void verifyPayPassword(String channelCode, String ffpId, String payPassword);

    /**
     * @description 检验最新的实名状态
     * <AUTHOR>
     * @date 2025/4/10 9:26
     **/
    boolean checkRealState(BizDto bizDto,String ffpNo);

    /**
     * 返回实名状态 按天缓存
     * @param ffpNo
     * @return
     */
    boolean realState(String ffpNo);

}
