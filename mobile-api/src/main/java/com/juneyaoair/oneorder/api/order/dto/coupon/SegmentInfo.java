package com.juneyaoair.oneorder.api.order.dto.coupon;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class SegmentInfo {
    /**
     * 旅行顺序
     * 第一段为从0开始，第二段为1，依次增加
     */
    private int SegNo;
    /**
     * 飞行方向
     * 去程为G，回程尉B
     */
    private String FlightDirection;
    /**
     * 航班号
     */
    private String FlightNo;
    /**
     * 航班起飞时间
     * yyyy-MM-dd HH:mm:ss
     */
    private String DepDateTime;
    /**
     * 航班到达时间
     */
    private String ArrDateTime;
    /**
     * 出发机场三字码
     */
    private String DepAirport;
    /**
     * 到达机场三字码
     */
    private String ArrAirport;
    /**
     * 舱位
     */
    private String Cabin;
    /**
     * 航程类型
     */
    private String FlightType;

    /**
     * 使用开始日期
     */
    private String UseStartDate;

    /**
     * 使用结束日期
     */
    private String UseEndDate;
}
