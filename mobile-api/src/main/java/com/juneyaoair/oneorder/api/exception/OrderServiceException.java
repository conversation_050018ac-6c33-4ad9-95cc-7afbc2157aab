package com.juneyaoair.oneorder.api.exception;

import com.juneyaoair.oneorder.restresult.enums.CommonErrorCode;
import com.juneyaoair.oneorder.restresult.exception.AppException;
import com.juneyaoair.oneorder.restresult.exception.ErrorCode;

/**
 * @ClassName OrderServiceException
 * @Description
 * <AUTHOR>
 * @Date 2024/7/3 16:01
 * @Version 1.0
 */
public class OrderServiceException extends AppException {

    protected OrderServiceException(ErrorCode error, String message) {
        super(error, message);
    }

    //在返回message里显示报错原因
    public static OrderServiceException fail(String message) {
        return fail(CommonErrorCode.BUSINESS_ERROR, message);
    }

    //在返回message里显示报错原因
    public static OrderServiceException fail(CommonErrorCode commonErrorCode, String message) {
        return new OrderServiceException(getErrorCode(commonErrorCode, message), message);
    }

    private static ErrorCode getErrorCode(CommonErrorCode commonErrorCode, String message){
        return new ErrorCode() {
            @Override
            public int getStatus() {
                return commonErrorCode.getStatus();
            }

            @Override
            public String getMessage() {
                return message;
            }

            @Override
            public String getCode() {
                return commonErrorCode.getCode();
            }

            @Override
            public void setMessage(String message) {

            }
        };
    }

}
