package com.juneyaoair.oneorder.api.passager.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LostItems {

    @ApiModelProperty("名称")
    private String leaveBehindName;
    @ApiModelProperty("类型")
    private String lbType;
    @ApiModelProperty("座位号")
    private String lbSeatNo;
    @ApiModelProperty("航班")
    private String lbFlightNo;
    @ApiModelProperty("日期")
    private String lbFlightDate;
    @ApiModelProperty("出发机场")
    private String departureAirport;
    @ApiModelProperty("到达机场")
    private String arrivalAirport;
    @ApiModelProperty("位置")
    private String lbLocation;
    @ApiModelProperty("号码")
    private String leaveBehindNo;
}
