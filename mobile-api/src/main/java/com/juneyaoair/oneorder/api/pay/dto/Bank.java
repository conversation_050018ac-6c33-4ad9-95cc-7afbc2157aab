package com.juneyaoair.oneorder.api.pay.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/28 10:10
 */
@ApiModel(value = "Bank",description = "支付方式名称")
@Data
public class Bank {
    @ApiModelProperty(value = "支付方式编码")
    private String method;
    @ApiModelProperty(value = "支付方式名称")
    private String bankName;
    @ApiModelProperty(value = "支付Logo")
    private String bankLogo;
    @ApiModelProperty(value = "支付二级Logo")
    private String subBankLogo;
    @ApiModelProperty(value = "排序")
    private int orderNo;
    @ApiModelProperty(value = "备注")
    private String remarks;
    @ApiModelProperty(value = "默认选中状态")
    private Boolean isDefault = false;
    /**
     * 返回的模式，正常情况无需处理 qrcode-返回二维码流
     */
    @ApiModelProperty(value = "支付展现模式",notes = "qrcode-返回二维码流,其余正常解析json数据")
    private String mode;
    @ApiModelProperty(value = "支付活动信息")
    private BankActivityInfo bankActivityInfo;
}
