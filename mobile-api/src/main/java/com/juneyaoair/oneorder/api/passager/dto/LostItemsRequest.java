package com.juneyaoair.oneorder.api.passager.dto;


import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "LostItemsRequest")
@XmlAccessorType(XmlAccessType.FIELD)
public class LostItemsRequest {

    private String inftCode;
    private String memberId;
    private Object data;

    public String getInftCode() {
        return inftCode;
    }

    public void setInftCode(String inftCode) {
        this.inftCode = inftCode;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
