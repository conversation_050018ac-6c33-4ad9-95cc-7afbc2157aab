package com.juneyaoair.oneorder.api.geetest.service.impl;

import com.google.common.collect.Maps;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.api.geetest.sdk.GeetestLib;
import com.juneyaoair.oneorder.api.geetest.sdk.dto.GeetestLibResult;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.DigestmodEnum;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.SceneEnum;
import com.juneyaoair.oneorder.api.geetest.service.IGeetestService;
import com.juneyaoair.oneorder.common.ServiceContext;
import com.juneyaoair.oneorder.common.dto.GeetestInterface;
import com.juneyaoair.oneorder.core.context.SecurityContextHolder;
import com.juneyaoair.oneorder.mobile.config.GeetestPropertiesConfig;
import com.juneyaoair.oneorder.mobile.dto.GeetestKey;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/13 13:02
 */
@Slf4j
@Service
public class GeetestService implements IGeetestService {
    @Autowired
    private GeetestPropertiesConfig geetestPropertiesConfig;
    @Override
    public GeetestLibResult register(String scene, DigestmodEnum digestmodEnum, Map<String, String> paramMap) {
        GeetestKey geetestKey = initGeetestKey(scene);
        //是否使用极验验证，否则执行本地模式
        GeetestLibResult result;
        GeetestLib gtLib = new GeetestLib(geetestKey.getGeetestId(), geetestKey.getGeetestKey());
        //是否使用极验验证，否则执行本地模式
        if (geetestPropertiesConfig.isGeetestOpen()) {
            result = gtLib.register(digestmodEnum, paramMap);
        } else {
            result = gtLib.localInit();
        }
        return result;
    }

    @Override
    public void validate(SceneEnum sceneEnum, GeetestInterface geetest) {
        if (StringUtils.isBlank(geetest.getGeetest_challenge())) {
            throw MultiLangServiceException.fail("流水信息不可为空");
        }
        if (StringUtils.isBlank(geetest.getGeetest_validate())) {
            throw MultiLangServiceException.fail("验证信息不可为空");
        }
        if (StringUtils.isBlank(geetest.getGeetest_seccode())) {
            throw MultiLangServiceException.fail("时间戳不可为空");
        }
        if (StringUtils.isBlank(geetest.getClient_type())) {
            throw MultiLangServiceException.fail("CLIENT_TYPE传递类型不能为空");
        }
        DigestmodEnum digestmodEnum = DigestmodEnum.MD5;
        Map<String, String> paramMap = Maps.newHashMap();
        String ip = SecurityContextHolder.getOriginIp();
        paramMap.put("digestmod", digestmodEnum.getName());
        paramMap.put("user_id", ip);
        paramMap.put("client_type", geetest.getClient_type());
        paramMap.put("ip_address", ip);
        validate(sceneEnum.getCode(), geetest, paramMap);
    }

    @Override
    public void validate(String scene, GeetestInterface geetest, Map<String, String> paramMap) {
        GeetestKey geetestKey = initGeetestKey(scene);
        GeetestLib gtLib = new GeetestLib(geetestKey.getGeetestId(), geetestKey.getGeetestKey());
        //是否使用极验验证，否则执行本地模式
        GeetestLibResult result;
        if (geetestPropertiesConfig.isGeetestOpen()) {
            result = gtLib.successValidate(geetest.getGeetest_challenge(), geetest.getGeetest_validate(), geetest.getGeetest_seccode(), paramMap);
            log.info("检验二次验证结果:{}", HoAirGsonUtil.objectToJson(result));
        } else {
            result = gtLib.failValidate(geetest.getGeetest_challenge(), geetest.getGeetest_validate(), geetest.getGeetest_seccode());
        }
        if (result.getStatus() != 1) {
            String tips = "验证未通过";
            throw new MultiLangServiceException(CommonErrorCode.QUICK_VERIFY_FAIL, tips);
        }
    }

    private GeetestKey initGeetestKey(String scene){
        //获取极验ID配置信息
        Map<String, GeetestKey> geetestKeyMap = geetestPropertiesConfig.getGeetestSetMap();
        if (ObjectUtils.isEmpty(geetestKeyMap)) {
            throw new ServiceException("请检查geetestSet配置");
        }
        GeetestKey geetestKey = geetestKeyMap.get(scene);
        if (ObjectUtils.isEmpty(geetestKey)) {
            throw new ServiceException("请检查配置:" + scene);
        }
        return geetestKey;
    }
}
