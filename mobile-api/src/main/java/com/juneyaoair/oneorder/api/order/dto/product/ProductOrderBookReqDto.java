package com.juneyaoair.oneorder.api.order.dto.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "产品下单请求")
public class ProductOrderBookReqDto {

    @ApiModelProperty(value = "渠道订单号")
    @NotBlank(message = "渠道订单号不能为空")
    private String channelOrderNo;

    @ApiModelProperty(value = "联系人")
    private String linkerName;

    @ApiModelProperty(value = "联系人手机")
    private String handPhone;

    @ApiModelProperty(value = "总金额")
    @NotNull(message = "总金额不能为空")
    private BigDecimal saleAmount;

    @ApiModelProperty(value = "积分")
    private int score;

    @ApiModelProperty(value = "优惠劵号")
    private String couponNo;

    @ApiModelProperty(value = "优惠劵抵扣金额")
    private int couponAmount;

    @ApiModelProperty(value = "邀请码")
    private String inviteCode;

    @ApiModelProperty(value = "网关号")
    private String gatewayNo;

    @ApiModelProperty(value = "虚拟支付信息")
    private String cardInfo;

    @ApiModelProperty(value = "币种")
    @NotBlank(message = "币种不能为空")
    private String currency;

    @Valid
    @ApiModelProperty(value = "产品列表")
    private List<ProductItemDto> productItemList;
}

