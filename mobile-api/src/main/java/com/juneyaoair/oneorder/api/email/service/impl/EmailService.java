package com.juneyaoair.oneorder.api.email.service.impl;

import com.juneyaoair.i18n.LocaleUtil;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.oneorder.api.email.EmailCommConfig;
import com.juneyaoair.oneorder.api.email.EmailConfigEnum;
import com.juneyaoair.oneorder.api.email.service.IMailService;
import com.juneyaoair.oneorder.common.util.SpringContextUtil;
import com.juneyaoair.oneorder.util.MailSendUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.mail.EmailAttachment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.UnsupportedEncodingException;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/30 11:27
 */
@Slf4j
@Service
public class EmailService implements IMailService {
    @Autowired
    private LocaleUtil localeUtil;

    private String initFromName() {
        String fromName = localeUtil.getTips("JUNEYAOAIR_AIR");
        if (StringUtils.isBlank(fromName)) {
            fromName = "吉祥航空";
        }
        return fromName;
    }

    @Override
    public void sendEmail(String to, String subject, String text, EmailConfigEnum emailConfig) {
        JavaMailSender javaMail = SpringContextUtil.getBean(emailConfig.getJavaMailSenderBeanName(), JavaMailSender.class);
        EmailCommConfig emailCommConfig = SpringContextUtil.getBean(emailConfig.getEmailCommConfigBeanName(), EmailCommConfig.class);
        // 创建MimeMessage
        MimeMessage mimeMessage = javaMail.createMimeMessage();
        try {
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);
            helper.setFrom(emailCommConfig.getFrom(),initFromName()); // 设置发件人邮箱地址
            helper.setTo(to); // 设置收件人邮箱地址
            helper.setSubject(subject); // 设置邮件主题
            helper.setText(text); // 设置邮件内容
            javaMail.send(mimeMessage);
            log.info("邮箱:{},发送目标:{}:内容:{}", emailCommConfig.getFrom(), to, text);
        } catch (MessagingException e) {
            throw MultiLangServiceException.fail("邮件发送失败");
        } catch (UnsupportedEncodingException e) {
            throw MultiLangServiceException.fail("邮件发送失败，编码异常");
        }
    }

    @Override
    public void sendEmailByCommonEmails(String to, String subject, String text, EmailConfigEnum emailConfig) {
        sendEmailByCommonEmails(to,subject,text,null,emailConfig);
    }

    @Override
    public void sendEmailByCommonEmails(String to, String subject, String text, List<EmailAttachment> attachments, EmailConfigEnum emailConfig) {
        EmailCommConfig emailCommConfig = SpringContextUtil.getBean(emailConfig.getEmailCommConfigBeanName(), EmailCommConfig.class);
        MailSendUtil.sendMail(subject, text, Collections.singletonList(to), attachments
                , emailCommConfig.getUsername(), emailCommConfig.getPassword(), emailCommConfig.getHost(), initFromName());
    }


    @Override
    public void sendHtmlMessage(String to, String subject, String text) {

    }
}
