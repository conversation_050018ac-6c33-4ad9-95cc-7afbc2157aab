package com.juneyaoair.oneorder.api.tinyurl;

import com.juneyaoair.horder.dto.GenTinyUrlRequest;
import com.juneyaoair.horder.dto.GenTinyUrlResponse;
import com.juneyaoair.horder.feign.TinyUrlFeignClient;
import com.juneyaoair.oneorder.api.common.FeignBaseServiceImpl;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

@Component
public class TinyUrlServiceImpl extends FeignBaseServiceImpl<GenTinyUrlRequest, GenTinyUrlResponse> implements ITinyUrlService {
    @Resource
    TinyUrlFeignClient client;

    @Override
    public String genTinyUrl(String originUrl,
                             String customTinyUrl,
                             String urlType,
                             int duration) {
        GenTinyUrlRequest urlRequest = new GenTinyUrlRequest();
        urlRequest.setOriginUrl(originUrl);
        urlRequest.setCustomTinyUrl(customTinyUrl);
        urlRequest.setUrlType(urlType);
        urlRequest.setDuration(duration);
        GenTinyUrlResponse genTinyUrl = invoke(urlRequest,
                "genTinyUrl",
                r -> client.genTinyUrl(r));
        return Optional.ofNullable(genTinyUrl).map(GenTinyUrlResponse::getTinyUrl).orElse(null);

    }
}
