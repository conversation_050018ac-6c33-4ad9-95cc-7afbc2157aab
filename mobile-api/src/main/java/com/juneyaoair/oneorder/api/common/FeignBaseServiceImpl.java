package com.juneyaoair.oneorder.api.common;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.function.Function;


@Slf4j
public abstract class FeignBaseServiceImpl<REQ, RES> {
    @Resource
    CommonService commonService;

    protected RES invoke(REQ req, String methodName, Function<REQ, RES> fun) {
        RES res = null;
        try {
            long startTimeStamp = System.currentTimeMillis();
            commonService.printReqLog("", methodName, req);
            res = fun.apply(req);
            commonService.printResultLog("", methodName, res, startTimeStamp);
        } catch (Exception e) {
            log.error("[methodName]-{},[call feign fails]：{}", methodName, e.getMessage());
        }
        return res;


    }


}
