package com.juneyaoair.oneorder.api.pay.config;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.oneorder.api.pay.dto.OrderType;
import com.juneyaoair.oneorder.api.pay.dto.OrderTypeDesc;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Type;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 支付相关配置
 * @date 2023/6/29 9:54
 */
@Data
@Configuration
@Slf4j
public class PayConfig {
    /**
     * 支付接口版本号
     */
    @Value("${payment.version:10}")
    private String version;
    /**
     * 支付货币
     */
    @Value("${payment.currency:CNY}")
    private String payCurrency;
    /**
     * 同步
     */
    @Value("${payment.url:}")
    private String netPay;
    /**
     * 异步
     */
    @Value("${payment.async.url:}")
    private String netPayAsync;

    /**
     * <AUTHOR>
     * @Description 验证码发送
     * @Date 13:23 2024/4/18
     **/
    @Value("${codeSend.url:}")
    private String codeSend;


    /**
     * <AUTHOR>
     * @Description 查询德付通签约情况
     * @Date 9:42 2024/5/27
     **/

    @Value("${deFuTong.catchSignInfo.url:}")
    private String catchDeFuTongSignInfo;

    /**
     * <AUTHOR>
     * @Description 德付通签约接口（此接口亦可用于身份验证）
     * @Date 12:05 2024/5/27
     **/
    @Value("${deFuTong.sign.url:}")
    private String deFuTongSign;

    /**
     * <AUTHOR>
     * @Description 德付通解约
     * @Date 13:49 2024/5/27
     **/
    @Value("${deFuTong.reSign.url:}")
    private String deFuTongReSign;
    /**
     * 支付通知地址
     */
    @Value("${payment.notice.url:http://127.0.0.1}")
    private String noticeUrl;
    /**
     * B2C支付通知地址
     */
    @Value("${payment.return.url:}")
    private String returnUrl;
    /**
     * B2C前端回调地址
     */
    @Value("${payment.homePage:https://www.juneyaoair.com}")
    private String homePage;
    /**
     * 国际网站网址
     */
    @Value("${gB2cUrl.url:}")
    private String gB2cUrl;
    /**
     * 国际官网前端支付回调通知地址
     */
    @Value("${payment.gb2c.return.url:https://global-new.juneyaoair.com/server/Order/redirectHomePageGlobal}")
    private String returnUrlGB2c;
    /**
     * 中文订单类型描述
     */
    @Value("${payment.orderType:}")
    private String orderTypeDesc;
    /**
     * 订单类型描述 多语言
     */
    @ApolloJsonValue("${payment.orderType.lang:}")
    private Map<String,Map<String,OrderTypeDesc>> orderTypeLangDescMap;

    /**
     * 获取渠道配置
     * @return
     */
    public Map<String, OrderTypeDesc> getOrderTypeDescMap() {
        try {
            Type type = new TypeToken<Map<String, OrderTypeDesc>>() {}.getType();
            return HoAirGsonUtil.fromJson(orderTypeDesc,type);
        } catch (Exception e) {
            log.error("{},orderTypeDesc转换异常:", orderTypeDesc, e);
            return null;
        }
    }
}
