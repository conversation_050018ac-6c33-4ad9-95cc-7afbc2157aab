package com.juneyaoair.oneorder.api.pay.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/4 10:25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PaymentDto", description = "支付平台请求参数")
public class PaymentDto {
    private String version; // 接口版本号10
    private String channelNo; // 渠道用户号B2C,CC等
    private String gatewayNo; // 在业务系统中选择使用的支付网关。该字段为空时将会在支付平台进行支付网关选择
    private String channelOrderNo; // 渠道订单编号,由业务系统生成，必须保证在业务系统的唯一性。
    /**
     * 订单类型,D为国内机票,I为国际机票,O为其他
     */
    private String orderType;
    private String orderNo; // 订单编号
    private String channelBuyDatetime; // 渠道订单生成时间,格式：yyyyMMddHHmmss
    protected String currency; // 支付币种
    private String amount; // 支付金额,一千元表示为：1000.00。
    private String channelPriInfo; //渠道用户私有域,为渠道用户自定义的字段、交易完成由支付平台原样返回。
    protected String returnUrl; // 前台返回地址
    protected String noticeUrl; // 后台返回地址
    protected String dynamicParameters; // 动态参数,JSON数据格式。可以用于传输第三方支付网关需要的特殊参数或前端才有的动态参数等等
    protected String subject; // 商品名称
    protected String body; // 商品描述
    protected String payTimeout; // 交易超时时间,交易超时时间是传送给具体的支付网关来控制,该参数对支付平台本身没有作用取值范围：1m～15d。 m-分钟，h-小时，d-天，1c-当天（无论交易何时创建，都在0点关闭）。 该参数数值不接受小数点，如1.5h，可转换为90m
    private String gatewayType; // 支付网关类型,1 － 直联银行，2 － 第三方网关，3 －预付费卡,4 – 无磁无密
    protected String dataCollectType; //数据采集方式,Web,IVR,Manual
    private String paymentChannelNo;//支付平台渠道用户号
    private String cardInfo; //支付卡信息
    private String chkValue; //签名
    private String useScore;//使用积分

    public String getPayParaSHA1Str() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(version);
        stringBuilder.append(channelNo);
        stringBuilder.append(gatewayNo);
        stringBuilder.append(channelOrderNo);
        stringBuilder.append(orderType);
        stringBuilder.append(orderNo);
        stringBuilder.append(channelBuyDatetime);
        stringBuilder.append(currency);
        stringBuilder.append(amount);
        if (channelPriInfo != null) {
            stringBuilder.append(channelPriInfo);
        }
        stringBuilder.append(returnUrl);
        stringBuilder.append(noticeUrl);
        if (dynamicParameters != null) {
            stringBuilder.append(dynamicParameters);
        }
        stringBuilder.append(subject);
        stringBuilder.append(body);
        if (payTimeout != null) {
            stringBuilder.append(payTimeout);
        }
        stringBuilder.append(gatewayType);
        stringBuilder.append(dataCollectType);
        if (paymentChannelNo != null) {
            stringBuilder.append(paymentChannelNo);
        }
        return stringBuilder.toString();
    }

    public Map<String, String> getPayPara() {
        Map<String, String> parametersMap = new HashMap<>();
        //1版本号
        parametersMap.put("Version", this.getVersion());
        //2渠道用户
        parametersMap.put("ChannelNo", this.getChannelNo());
        //3支付网关
        parametersMap.put("GatewayNo", this.getGatewayNo());
        //4渠道订单编号
        parametersMap.put("ChannelOrderNo", this.getChannelOrderNo());
        //5订单类型
        parametersMap.put("OrderType", this.getOrderType());
        //6订单编号
        parametersMap.put("OrderNo", this.getOrderNo());
        //7渠道订单生成时间
        parametersMap.put("ChannelBuyDatetime", this.getChannelBuyDatetime());
        //8支付币种
        parametersMap.put("Currency", this.getCurrency());
        //9支付金额
        parametersMap.put("Amount", this.getAmount());
        //10渠道用户私有域
        if (this.getChannelPriInfo() != null) {
            parametersMap.put("ChannelPriInfo", this.getChannelPriInfo());
        }
        //11前台返回地址
        parametersMap.put("ReturnUrl", this.getReturnUrl());
        //12后台返回地址
        parametersMap.put("NoticeUrl", this.getNoticeUrl());
        //13动态参数
        if (this.getDynamicParameters() != null) {
            parametersMap.put("DynamicParameters", this.getDynamicParameters());
        }
        //14商品名称
        parametersMap.put("Subject", this.getSubject());
        //15商品描述
        parametersMap.put("Body", this.getBody());
        //16交易超时时间
        if (this.getPayTimeout() != null) {
            parametersMap.put("PayTimeout", this.getPayTimeout());
        }
        //17网关类型
        parametersMap.put("GatewayType", this.getGatewayType());
        //18数据采集方式
        parametersMap.put("DataCollectType", this.getDataCollectType());
        //23使用积分
        parametersMap.put("UseScore", this.getUseScore());
        //
        if (this.getPaymentChannelNo() != null) {
            parametersMap.put("PaymentChannelNo", this.getPaymentChannelNo());
        }
        return parametersMap;
    }
}
