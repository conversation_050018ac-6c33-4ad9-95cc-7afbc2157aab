package com.juneyaoair.oneorder.api.order.dto.coupon;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class VisaProduct {
    /**
     * 出发时间
     */
    @JsonProperty(value = "FlightDate")
    private String FlightDate;

    /**
     * 截止收材料时间
     */
    @JsonProperty(value = "DeadlineDate")
    private String DeadlineDate;

    /**
     * 订单旅客信息
     */
    @JsonProperty(value = "PassengerInfoExt")
    private PassengerInfo PassengerInfoExt;
    /**
     * 联系人姓名
     */
    @JsonProperty(value = "LinkerName")
    private String LinkerName;
    /**
     * 联系人手机号
     */
    @JsonProperty(value = "LinkerPhone")
    private String LinkerPhone;
    /**
     * 电子邮箱
     */
    @JsonProperty(value = "LinkerEmail")
    private String LinkerEmail;
    /**
     * 收件地址
     */
    @JsonProperty(value = "LinkerAddress")
    private String LinkerAddress;
    /**
     * 证件类型
     */
    @JsonProperty(value = "CardType")
    private String CardType;
    /**
     * 客户类型
     */
    @JsonProperty(value = "CustomerType")
    private String CustomerType;
    /**
     * 证件号码
     */
    @JsonProperty(value = "CardNo")
    private String CardNo;
    /**
     * 旅客姓名
     */
    @JsonProperty(value = "Name")
    private String Name;
    /**
     * 性别
     */
    @JsonProperty(value = "Sex")
    private String Sex;
    /**
     * 退款规则
     */
    @JsonProperty(value = "RefundRules")
    private String RefundRules;
}
