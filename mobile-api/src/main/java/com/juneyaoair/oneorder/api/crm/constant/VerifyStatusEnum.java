package com.juneyaoair.oneorder.api.crm.constant;

/**
 * <AUTHOR>
 * @date 2018/7/23  17:06.
 * 实名认证状态
 */
public enum VerifyStatusEnum {

    //未知，未实名认证过
    UNKNOW("-2","UNKNOW","未知"),
    REJECT("-1","Reject","认证拒绝"),
    WAIT("0","Wait","待审核"),
    PASS("1","Pass","认证通过");

    public final String code;
    public final String eName;
    public final String desc;

    VerifyStatusEnum(String code, String eName, String desc) {
        this.code = code;
        this.eName = eName;
        this.desc = desc;
    }

    public static VerifyStatusEnum formatVerifyStatus(String code){
        for (VerifyStatusEnum c: VerifyStatusEnum.values()) {
            if (c.code.equals(code)) {
                return c;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String geteName() {
        return eName;
    }

    public String getDesc() {
        return desc;
    }
}
