package com.juneyaoair.oneorder.api.dsop.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/12 16:49
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PtFlightStatusReq {
    /**
     * 航班号
     */
    private String flightNo;
    /**
     * 航班日期 yyyy-MM-dd(中国时间 foc使用的)
     */
    private String flightDate;
    /**
     * 当地 航班日期 yyyy-MM-dd
     */
    private String flightDateLocal;
    /**
     * 起始地机场三字码
     */
    private String departureAirport;
    /**
     * 目的地机场三字码
     */
    private String arrivalAirport;
    private String departureCity;
    private String arrivalCity;
    /**
     * 出发城市三字码
     */
    private String departureCityCode;
    /**
     * 到达城市三字码
     */
    private String arrivalCityCode;
    /**
     * 加密字符串，加密方式md5(CLIENT_CODE+flightNo+CLIENT_PASSWORD)
     */
    private String signature;
    private String ip;
}
