package com.juneyaoair.oneorder.api.geetest.service;

import com.juneyaoair.oneorder.api.geetest.sdk.dto.GeetestLibResult;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.DigestmodEnum;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.SceneEnum;
import com.juneyaoair.oneorder.common.dto.GeetestInterface;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/13 13:00
 */
public interface IGeetestService {
    /**
     * 极验注册
     * @param scene 使用场景
     * @param digestmodEnum
     * @param paramMap
     * @return
     */
    GeetestLibResult register(String scene, DigestmodEnum digestmodEnum, Map<String, String> paramMap);

    /**
     * 极验二次验证
     *      2024-09-12 入参场景类型改为枚举
     * @param scene
     * @param geetest
     * @param paramMap
     * @return
     */
    @Deprecated
    void validate(String scene, GeetestInterface geetest, Map<String, String> paramMap);

    /**
     * 极验二次验证
     * @param sceneEnum
     * @param geetest
     * @return
     */
    void validate(SceneEnum sceneEnum, GeetestInterface geetest);

}
