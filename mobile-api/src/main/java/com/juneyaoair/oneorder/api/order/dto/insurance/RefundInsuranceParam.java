package com.juneyaoair.oneorder.api.order.dto.insurance;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Description 单独退保请求参数
 * @created 2025/02/13 17:24
 */
@Data
public class RefundInsuranceParam {

    @ApiModelProperty(value = "接口版本号10", hidden = true)
    private String Version;

    @ApiModelProperty(value = "渠道用户号B2C,CC等", hidden = true)
    private String ChannelCode;

    @ApiModelProperty(value = "渠道用户人员号分配给渠道用户的工作人员号", hidden = true)
    private String UserNo;

    @NotBlank(message = "币种不能为空")
    @ApiModelProperty(value = "币种代码CNY人民币")
    private String Currency;

    @NotBlank(message = "机票订单编号不能为空")
    @ApiModelProperty(value = "机票订单编号")
    private String TicketOrderNo;

    @NotBlank(message = "退保申请人不能为空")
    @ApiModelProperty(value = "退保申请人")
    private String Proposer;

    @NotBlank(message = "退保人联系电话不能为空")
    @ApiModelProperty(value = "退保人联系电话")
    private String LinkTelphone;

    @NotBlank(message = "退保类型不能为空")
    @ApiModelProperty(value = "退保类型")
    private String InsuranceRefundType;

    @NotEmpty(message = "退保人联系电话不能为空")
    @ApiModelProperty(value = "需要退保的人航段ID列表")
    private int[] PassengerSegmentIDList;

    @NotEmpty(message = "保险编码清单不能为空")
    @ApiModelProperty(value = "保险编码清单")
    private String[] InsuranceCodeList;

}
