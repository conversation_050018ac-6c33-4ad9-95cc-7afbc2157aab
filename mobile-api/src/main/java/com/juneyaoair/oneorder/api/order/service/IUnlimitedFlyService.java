package com.juneyaoair.oneorder.api.order.service;

import com.juneyaoair.oneorder.order.dto.UnlimitedFlyBindRecord;
import com.juneyaoair.oneorder.order.dto.UnlimitedFlyV2BindRecord;

import java.util.List;

/**
 * @ClassName IUnlimitedFlyService
 * @Description 畅飞卡服务
 * <AUTHOR>
 * @Date 2023/7/3 13:14
 * @Version 1.0
 */
public interface IUnlimitedFlyService {
    /**
     * 查询儿童畅飞卡绑定记录
     * @param channelCode
     * @param ffpId
     * @return
     */
    List<UnlimitedFlyBindRecord> listChdBindRecord(String channelCode, String ffpId, String ip);

    /**
     * 查询畅飞卡2.0绑定记录
     * @param channelCode
     * @param ffpId
     * @param ip
     * @return
     */
    List<UnlimitedFlyV2BindRecord> listFlyCard2BindRecord(String channelCode, String ffpId, String ip);

    /**
     * 查询主题卡绑定记录
     * @param channelCode
     * @param ffpId
     * @param ip
     * @return
     */
    List<UnlimitedFlyV2BindRecord> ThemeCouponBindRecord(String channelCode, String ffpId, String ip,List<String> cabinTypes);

}
