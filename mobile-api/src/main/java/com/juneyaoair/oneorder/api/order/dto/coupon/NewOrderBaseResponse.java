package com.juneyaoair.oneorder.api.order.dto.coupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: caolei
 * @Description: 订单基础返回信息
 * @Date: 2021/8/12 14:11
 * @Modified by:
 */
@Data
public class NewOrderBaseResponse<T> {
    @ApiModelProperty(value = "接口版本号")
    private String Version;
    @ApiModelProperty(value = "渠道用户号")
    private String ChannelCode;
    @ApiModelProperty(value = "渠道工作人员号")
    private String UserNo;
    @ApiModelProperty(value = "结果代码")
    private String ResultCode;
    @ApiModelProperty(value = "错误信息")
    private String ErrorInfo;
    @ApiModelProperty(value = "数据")
    private T Data;
}