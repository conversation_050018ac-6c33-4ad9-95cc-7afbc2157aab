package com.juneyaoair.oneorder.api.order.dto.ticket;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 客票查询返回结果
 */
public class TicketListInfoResponse {
    private String ResultCode;//结果代码 1001 － 成功，其它失败
    private String ErrorInfo;//错误信息
    private String ChannelCode;//渠道用户号
    private String ChannelOrderNo;//渠道订单编号
    private String UserNo;//渠道工作人员号
    private List<PtIBETicketInfo> IBETicketInfoList;//客票信息列表
    @SerializedName("TrrDateLimit")
    private String trrDateLimit;//TRR改签时限 json
    private TrrDateLimit trrLimit;//TRR改签时限 对象

    public TicketListInfoResponse() {
    }

    public String getResultCode() {
        return ResultCode;
    }

    public void setResultCode(String resultCode) {
        ResultCode = resultCode;
    }

    public String getErrorInfo() {
        return ErrorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        ErrorInfo = errorInfo;
    }

    public String getChannelCode() {
        return ChannelCode;
    }

    public void setChannelCode(String channelCode) {
        ChannelCode = channelCode;
    }

    public String getChannelOrderNo() {
        return ChannelOrderNo;
    }

    public void setChannelOrderNo(String channelOrderNo) {
        ChannelOrderNo = channelOrderNo;
    }

    public String getUserNo() {
        return UserNo;
    }

    public void setUserNo(String userNo) {
        UserNo = userNo;
    }

    public List<PtIBETicketInfo> getIBETicketInfoList() {
        return IBETicketInfoList;
    }

    public void setIBETicketInfoList(List<PtIBETicketInfo> IBETicketInfoList) {
        this.IBETicketInfoList = IBETicketInfoList;
    }

    public String getTrrDateLimit() {
        return trrDateLimit;
    }

    public void setTrrDateLimit(String trrDateLimit) {
        this.trrDateLimit = trrDateLimit;
    }

    public TrrDateLimit getTrrLimit() {
        return trrLimit;
    }

    public void setTrrLimit(TrrDateLimit trrLimit) {
        this.trrLimit = trrLimit;
    }
}
