package com.juneyaoair.oneorder.api.order.service;

import com.juneyaoair.oneorder.api.order.dto.coupon.BuyInsuranceInfo;
import com.juneyaoair.oneorder.api.order.dto.coupon.NewBasicOrderDetailResponse;
import com.juneyaoair.oneorder.api.order.dto.coupon.NewCouponOrderDetailReq;
import com.juneyaoair.oneorder.api.order.dto.coupon.QueryIsBuyInsuranceReq;
import com.juneyaoair.oneorder.api.order.dto.flightproof.FlightChangeProofApplyReq;
import com.juneyaoair.oneorder.api.order.dto.flightproof.FlightChangeProofResponse;
import com.juneyaoair.oneorder.api.order.dto.insurance.RefundInsuranceParam;
import com.juneyaoair.oneorder.api.order.dto.ticket.*;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.order.common.PtResponse;
import com.juneyaoair.oneorder.order.dto.*;

import java.util.List;
import java.util.Map;

/**
 * @ClassName IOrderService
 * @Description
 * <AUTHOR>
 * @Date 2023/6/27 13:58
 * @Version 1.0
 */
public interface IOrderService {
    /**
     * <AUTHOR>
     * @Description 添加常用乘机人
     * @Date  2023/7/3
     * @param generalContactRequest
     * @return com.juneyaoair.oneorder.order.dto.PtV2GeneralContactResponse
     **/
    PtV2GeneralContactResponse toAddGeneralContact(GeneralContactRequest generalContactRequest);

    /**
     * <AUTHOR>
     * @Description 添加/删除常用乘机人
     * @Date 9:32 2023/7/4
     * @param generalContactRequest
     * @return com.juneyaoair.oneorder.order.common.PtResponse
     **/
    PtResponse toModifyGeneralContact(GeneralContactRequest generalContactRequest);


    /**
     * 检查客票信息
     */
    FlightChangeProofResponse getTicketFlightCheck(FlightChangeProofApplyReq req);

    /**
     * 自营客票判断
     */
    PtOwnSaleResponseDto queryOwnSaleTicketInfo(PtOwnSaleRequestDto ptOwnSaleRequestDto, Map<String, String> headMap);

    /**
     * 查询客票信息
     */
    TicketListInfoResponse listTicketInfo(TicketInfoRequest ticketInfoRequest, Map<String, String> headMap);

    /**
     * 查询权益券订单信息接口（/Order/BasicGetCouponOrder）
     * @param couponOrderDetailReq
     * @return
     */
    NewBasicOrderDetailResponse queryCouponOrderDetail(NewCouponOrderDetailReq couponOrderDetailReq);

    /**
     * 是否购买航意险
     * @param queryIsBuyInsuranceReq
     * @return
     */
    List<BuyInsuranceInfo> QueryIsBuyInsurance(QueryIsBuyInsuranceReq queryIsBuyInsuranceReq);

    /**
     * 退保
     *
     * @param channelNo
     * @param refundInsuranceParam
     */
    void refundInsurance(String channelNo, RefundInsuranceParam refundInsuranceParam);

    QueryTicketRuleInfoResult queryTicketRuleInfo(ChannelInfo channelInfo, String ticketNo);
}
