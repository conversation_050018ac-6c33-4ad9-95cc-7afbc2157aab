package com.juneyaoair.oneorder.api.cuss.service.impl;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.oneorder.api.common.HttpBaseServiceImpl;
import com.juneyaoair.oneorder.api.cuss.dto.DetrMemberInfo;
import com.juneyaoair.oneorder.api.cuss.dto.TicketVerifyResponse;
import com.juneyaoair.oneorder.api.cuss.service.IVerifyTicketService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


@Component
public class VerifyTicketServiceImpl extends HttpBaseServiceImpl implements IVerifyTicketService {

    /** 客票验真 */
    private static final String TICKET_VERIFY_PATH = "/verify/verifyTicket";

    /** 值机选座服务customer */
    @Value("${new_checkin_select_customer_url:https://e-cuss.juneyaoair.com/customer}")
    public String newCheckInCustomerUrl;

    @Override
    public BaseResultDTO<TicketVerifyResponse> verifyTicket(BaseRequestDTO<DetrMemberInfo> req) {

        return invokeHttpClient(
                req, newCheckInCustomerUrl + TICKET_VERIFY_PATH,
                new TypeToken<BaseResultDTO<TicketVerifyResponse>>() {
                }.getType()
        );

    }
}
