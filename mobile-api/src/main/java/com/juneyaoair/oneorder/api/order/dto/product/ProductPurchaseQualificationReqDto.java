package com.juneyaoair.oneorder.api.order.dto.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@Data
@ApiModel(value = "产品购买资格查询请求")
public class ProductPurchaseQualificationReqDto {
    
    @NotBlank(message = "产品类型不能为空")
    @ApiModelProperty(value = "产品类型")
    private String productType;

    @NotBlank(message = "产品ID不能为空")
    @ApiModelProperty(value = "产品ID")
    private String productId;

    @NotBlank(message = "客票号不能为空")
    @ApiModelProperty(value = "客票号")
    private String ticketNo;

    @NotBlank(message = "航班日期不能为空")
    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @NotBlank(message = "航班号不能为空")
    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @NotBlank(message = "出发机场不能为空")
    @ApiModelProperty(value = "出发机场")
    private String depAirport;

    @NotBlank(message = "到达机场不能为空")
    @ApiModelProperty(value = "到达机场")
    private String arrAirport;

    @NotBlank(message = "乘客姓名不能为空")
    @ApiModelProperty(value = "乘客姓名")
    private String passengerName;
} 