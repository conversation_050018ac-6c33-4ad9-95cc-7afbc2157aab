package com.juneyaoair.oneorder.api.order.dto.coupon;

import com.juneyaoair.flightbasic.request.activity.coupon.PassengerItinerary;
import lombok.Data;

import java.util.List;

/**
 * @description 邮寄行程单信息
 */
@Data
public class MailTravelProduct {
    /**
     * 收件人姓名
     */
    private String RecipientsName;
    /**
     * 收件人手机号
     */
    private String RecipientsPhone;
    /**
     * 邮寄地址
     */
    private String RecipientsAddress;
    /**
     * 乘客行程信息
     */
    private List<PassengerItinerary> PassengerItineraryList;
}
