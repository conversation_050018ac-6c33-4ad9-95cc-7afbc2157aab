package com.juneyaoair.oneorder.api.pay.service;

import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.request.payMethod.ParamPayMethodDTO;
import com.juneyaoair.flightbasic.response.paynote.PayMethodDTO;
import com.juneyaoair.oneorder.api.pay.dto.*;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.mobile.dto.PayChannelDetail;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/28 11:23
 */
public interface IPayService {
    /**
     * 查询所有的支付方式
     *
     * @param baseRequestDTO
     * @return
     */
    BaseResultDTO<List<PayMethodDTO>> queryAllPayMethod(BaseRequestDTO<ParamPayMethodDTO> baseRequestDTO);

    /**
     * 唤起支付
     *
     * @param payChannelDetail
     * @param orderPayParam
     * @return
     */
    PayResult doPay(PayChannelDetail payChannelDetail, LanguageEnum languageEnum,String currency, OrderPayParam orderPayParam, String ffpCardNo, HttpServletResponse response);

    /**
     * @param payChannelDetail
     * @param verifyCodeSendParam
     * @param ffpCardNo
     * @param response
     * @return com.juneyaoair.oneorder.api.pay.dto.VerifyCodeSendResult
     * <AUTHOR>
     * @Description 验证码发送
     * @Date 11:06 2024/4/18
     **/
    VerifyCodeSendResult toSendVerifyCode(PayChannelDetail payChannelDetail, VerifyCodeSendParam verifyCodeSendParam, String ffpCardNo, HttpServletResponse response);

    /**
     * @param ffpCardNo
     * @return com.juneyaoair.oneorder.api.pay.dto.PayResult
     * <AUTHOR>
     * @Description 查询德付通签约情况
     * @Date 9:29 2024/5/27
     **/
    boolean toCatchDFTSignInfo(String ffpCardNo);


    /**
     * @param payChannelDetail
     * @param dftSignRequest
     * @param ffpCardNo
     * @return java.lang.String 验证码
     * <AUTHOR>
     * @Description 德付通进行身份验证
     * @Date 10:56 2024/5/27
     **/
    void toIdentify(PayChannelDetail payChannelDetail, DFTSignRequest dftSignRequest, String ffpCardNo);

    /**
     * @param payChannelDetail
     * @param dftSignRequest
     * @param ffpCardNo
     * @return void
     * <AUTHOR>
     * @Description 德付通签约操作
     * @Date 12:32 2024/5/27
     **/
    void toSign(PayChannelDetail payChannelDetail, DFTSignRequest dftSignRequest, String ffpCardNo);


    /**
     * <AUTHOR>
     * @Description 德付通解约
     * @Date 13:36 2024/5/27
     * @param payChannelDetail
     * @param dftSignRequest
     * @param ffpCardNo
     * @return void
     **/
    void toReSign(PayChannelDetail payChannelDetail, String ffpCardNo);

}
