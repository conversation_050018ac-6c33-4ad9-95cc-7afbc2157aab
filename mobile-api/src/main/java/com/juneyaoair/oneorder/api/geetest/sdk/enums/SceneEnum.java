package com.juneyaoair.oneorder.api.geetest.sdk.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 极验使用场景 需与geetest.map同步配置
 * @date 2023/10/13 13:21
 */
public enum SceneEnum {
    SMS("sms", "短信验证码"),
    SMS_GLOBAL("smsGlobal", "国际网站短信验证码"),
    EMAIL_GLOBAL("emailGlobal", "国际网站邮箱验证码"),
    HOCAR_SMS("hocarsms", "车载APK短信验证码"),
    SEAT_SMS("seatSms", "选座短信验证码"),
    MEMBER_SMS("memberSms", "会员中心短信验证码"),
    MEMBER_EMAIL("memberEmail", "会员中心邮箱验证码"),
    LOGIN("login", "登录"),
    //因为旧的代码里已经用了该enum包括电子行程单-客票验真、电子行程单，后面切换成TICKET_CHECK
    COMMON("common", "官网客票查询"),
    //后面客票查询都是用该enum
    //目前包含了：邮寄行程单(trip/checkTicket)、额外行李(prePayBaggage/queryFlight)、如意保险(QueryInsuranceFlightInfo)、积分补登(member/toCompensateScore)
    TICKET_CHECK("ticketCheck", "客票查询"),
    TICKET_QUERY("ticket","旧版客票查询"),
    CUSS_TRAVELLER_TRIP("cussTravellerTrip", "值机选座行程查询"),
    VERIFY_TICKET("verifyTicket", "客票验真"),
    ;

    private String code;
    private String desc;

    SceneEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public static SceneEnum getEnumByCode(String code) {
        return s_map.get(code);
    }

    private static Map<String, SceneEnum> s_map = new HashMap<>();

    static {
        for (SceneEnum value : SceneEnum.values()) {
            s_map.put(value.code, value);
        }
    }
}
