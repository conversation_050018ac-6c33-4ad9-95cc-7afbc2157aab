package com.juneyaoair.oneorder.api.b2chand.dto;

import lombok.Data;

@Data
public class FlightAbnormalityRequest {
    public String Version;
    public String ChannelCode;
    public String UserNo;
    public String FlightNo;
    public String FlightDate;
    //票号
    public String TicketNo;
    private String PassengerName;//姓名
    /**
     * 证件类型
     **/
    private String CertType;
    /**
     * 证件号
     **/
    private String CertNo;

    public FlightAbnormalityRequest(String version, String channelCode, String userNo){
        this.Version = version;
        this.ChannelCode = channelCode;
        this.UserNo = userNo;
    }
}
