package com.juneyaoair.oneorder.api.crm.service;

import com.juneyaoair.oneorder.crm.dto.PtApiCRMRequest;
import com.juneyaoair.oneorder.crm.dto.PtCRMResponse;
import com.juneyaoair.oneorder.crm.dto.login.PtSendCaptchaRequest;

import java.util.Map;

/**
 * <AUTHOR>
 * @description  CRM验证码服务
 * @date 2018/8/9  19:24.
 */
public interface ICaptchaService {
    /**
     * 基于会员系统发送验证码
     * @param ptApiCRMRequest
     * @return
     */
    PtCRMResponse send (PtApiCRMRequest<PtSendCaptchaRequest> ptApiCRMRequest);

    /**
     * 通用验证码发送（给单个手机号发送验证码）
     * @param templateCode  模板号
     * @param areaId
     * @param phone
     * @param extras
     * @return
     */
    boolean commonSmsSend(String templateCode, String areaId, String phone, Map<String, String> extras);
}
