package com.juneyaoair.oneorder.api.crm.enums;

/**
 * <AUTHOR>
 * @date 2020/7/31 11:06.
 * crm 错误信息枚举
 */
public enum CrmCommonEnum {
    SUC0(0,"SUCCESS","成功"),
    UNKNOWN(-1,"UNKNOWN","未知错误"),
    FAIL103004(103004,"103004","帐号或密码错误"),
    FAIL302017(302017,"302017","手机号已被占用，请更换"),
    FAIL302018(302018,"302018","邮箱号已被占用，请更换"),
    REQUEST_PARAMETER_EXCEPTION(999998,"Request parameter Exception","请求参数异常"),
    POST_REQUEST_ONLY(999997,"Post request only","仅支持POST请求"),
    CLIENT_AUTHENTICATION_FAILED(999996,"Client authentication failed","客户端认证失败"),
    PLEASE_WAIT(999995,"Please wait 1 minute to send","请等待一分钟之后在发生"),
    NO_WAIT(109001,"memberCardNo not null","会员卡号不可为空"),
    MEMBER_ID_ERROR(120001,"120001","会员ID无效"),
    MEMBER_COMPANY_ERROR(120002,"120002","会员与企业不匹配"),
    DUPLICATE_THIS_LINKMAN_INFORMATION(999994," Duplicate this linkman information","常用联系人已存在"),
    BIZ_EXP_999963(999963,"Beneficiary count is more", "受益人数量已达上限，请删除后再尝试新增受益人"),
    BIZ_EXP_999962(999962, " Certificates has been add ","您重复提交了同一受益人，请修改后再尝试保存操作"),
    BIZ_EXP_999961(999961, " Add beneficiary failed","新增受益人失败"),
    BIZ_EXP_999960(999960, " Add beneficiary`s certificate failed","添加受益人证件失败"),
    BIZ_EXP_999959(999959, "999959","暂无该受益人记录"),
    BIZ_EXP_999958(999958, "999958","会员与受益人不匹配"),
    BIZ_EXP_999957(999957, "999957","受益人删除失败"),
    BIZ_EXP_999956(999956, "999956","非常抱歉，本年度删除操作次数已达上限"),
    BIZ_EXP_999955(999955, "999955","修该受益人信息失败"),
    BIZ_EXP_999954(999954, "999954","清空受益人证件数据异常"),
    BIZ_EXP_999953(999953, "999953","增添受益人证件数据异常"),
    BIZ_EXP_999952(999952, "999952","激活收益人失败"),
    BIZ_EXP_999951(999951, "999951","该受益人不可修改"),
    BIZ_EXP_999950(999950, "999950","激活且未生效受益人不可删除"),
    BIZ_EXP_999949(999949, "999949","激活少于10人不可删除"),
    BIZ_EXP_999948(999948, "999948","该会员未实名认证"),
    UNKNOW999999(999999,"Unknown Exception","未知异常"),
    ;

    private int code;
    public final String eName;
    public final String desc;

    CrmCommonEnum(int code, String eName, String desc) {
        this.code = code;
        this.eName = eName;
        this.desc = desc;
    }

    public static CrmCommonEnum formatEnm(String eName){
        for (CrmCommonEnum c: CrmCommonEnum.values()) {
            if (c.eName.equals(eName)) {
                return c;
            }
        }
        return null;
    }
    public static CrmCommonEnum formatCode(int code)
    {
        for (CrmCommonEnum c: CrmCommonEnum.values()) {
            if (c.code==code) {
                return c;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }
}
