package com.juneyaoair.oneorder.api.passager;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
public class PassagerConfig {

    // 旅客服务网
    @Value("${UnitOrder.PassagerAPI.URL:}")
    public String URL_PASSAGER_API;
    //表扬,投诉,意见新增接口
    public static final String SPR_SUG_COM_B2C = "/quality/qosPsimSuggest/addPriSugComB2C";
    public static final String LOST_AND_FOUND_QUERY = "/passenger/lvbhLeaveBehind/lostAndFoundQuery";
    public static final String CHECK_IN_ITEMS_LOST = "/passenger/lvbhReportLoss/checkInitemsLost";
    // 获客配置信息
    public static final String GET_CUST_LINK = "/cart/custLink/selectByCityCode";
    // 新增免打扰记录
    public static final String ADD_CUST_NOT_DISTURB_RECORD = "/cart/custNotDisturbRecord/insertByB2C";

}
