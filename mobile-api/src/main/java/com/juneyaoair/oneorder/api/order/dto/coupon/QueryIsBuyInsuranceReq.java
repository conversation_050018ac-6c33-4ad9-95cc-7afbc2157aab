package com.juneyaoair.oneorder.api.order.dto.coupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Author: caolei
 * @Description: 调用是否已购买保险接口（/Order/QueryIsBuyInsurance）请求参数
 * @Date: 2025/01/01 13:52
 * @Modified by:
 */
@Data
public class QueryIsBuyInsuranceReq {

    @ApiModelProperty(value = "接口版本号", hidden = true)
    private String Version;

    @ApiModelProperty(value = "渠道用户号", hidden = true)
    private String ChannelCode;

    @ApiModelProperty(value = "渠道工作人员号", hidden = true)
    private String UserNo;

    @ApiModelProperty(value = "IP地址", hidden = true)
    private String RequestIp;

    @NotBlank(message = "航班号不能为空")
    @ApiModelProperty(value = "航班号", required = true)
    private String FlightNo;

    @NotBlank(message = "航班日期不能为空")
    @ApiModelProperty(value = "航班日期", required = true)
    private String FlightDate;

    @NotBlank(message = "出发机场不能为空")
    @ApiModelProperty(value = "出发机场", required = true)
    private String DepAirport;

    @NotBlank(message = "到达机场不能为空")
    @ApiModelProperty(value = "到达机场", required = true)
    private String ArrAirport;

    @Valid
    @NotEmpty(message = "客票清单不能为空")
    @ApiModelProperty(value = "客票清单", required = true)
    private List<InsuranceTicketInfo> InsuranceTicketInfoList;

}