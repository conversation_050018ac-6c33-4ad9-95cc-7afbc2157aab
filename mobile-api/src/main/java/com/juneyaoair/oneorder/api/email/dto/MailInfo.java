package com.juneyaoair.oneorder.api.email.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.mail.EmailAttachment;

import java.util.List;

/**
 * @Description 发送邮件信息
 **/
@Data
@NoArgsConstructor
public class MailInfo {

    // 收件人
    private List<String> toAddress = null;
    // 抄送人地址
    private List<String> ccAddress = null;
    // 密送人
    private List<String> bccAddress = null;
    // 附件信息
    private List<EmailAttachment> attachments = null;
    // 邮件主题
    private String subject;
    // 邮件的文本内容
    private String content;

}
