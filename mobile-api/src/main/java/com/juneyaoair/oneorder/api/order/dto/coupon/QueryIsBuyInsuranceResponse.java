package com.juneyaoair.oneorder.api.order.dto.coupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: caolei
 * @Description: 调用是否已购买保险接口（/Order/QueryIsBuyInsurance）返回参数
 * @Date: 2025/01/01 13:52
 * @Modified by:
 */
@Data
public class QueryIsBuyInsuranceResponse {

    @ApiModelProperty(value = "返回编码")
    private String ResultCode;

    @ApiModelProperty(value = "异常描述")
    private String ErrorInfo;

    @ApiModelProperty(value = "是否已购买航意险信息")
    private List<BuyInsuranceInfo> BuyInsruanceInfoList;

}