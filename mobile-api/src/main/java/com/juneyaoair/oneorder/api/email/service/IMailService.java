package com.juneyaoair.oneorder.api.email.service;

import com.juneyaoair.oneorder.api.email.EmailConfigEnum;
import org.apache.commons.mail.EmailAttachment;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/30 11:25
 */
public interface IMailService {
    /**
     * @description 发送简单邮件
     * <AUTHOR>
     * @date 2024/10/30 11:27
     * @param
     * @return void
     **/
    void sendEmail(String to, String subject, String text, EmailConfigEnum emailConfig);
    /**
     * @description 发送邮件的另外一种方式，采用commons-email
     **/
    void sendEmailByCommonEmails(String to, String subject, String text, EmailConfigEnum emailConfig);
    /**
     * 使用通用邮箱配置发送电子邮件
     *
     * @param to 收件人邮箱地址
     * @param subject 邮件主题
     * @param text 邮件正文内容
     * @param attachments 邮件附件列表，每个附件的信息封装在EmailAttachment对象中
     * @param emailConfig 邮箱配置枚举，用于指定发送邮件所使用的邮箱配置
     */
    void sendEmailByCommonEmails(String to, String subject, String text, List<EmailAttachment> attachments,EmailConfigEnum emailConfig);
    /**
     * @description 发送HTML邮件
     * <AUTHOR>
     * @date 2024/10/30 13:08
     * @param to
     * @param subject
     * @param text
     * @return void
     **/
    void sendHtmlMessage(String to, String subject, String text);
}
