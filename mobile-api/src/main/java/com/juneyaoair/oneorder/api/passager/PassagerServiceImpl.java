package com.juneyaoair.oneorder.api.passager;

import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.oneorder.api.common.HttpBaseServiceImpl;
import com.juneyaoair.oneorder.api.passager.dto.*;
import com.juneyaoair.oneorder.tools.utils.EncoderHandler;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Component
public class PassagerServiceImpl extends HttpBaseServiceImpl implements IPassagerService {

    @Resource
    private PassagerConfig config;

    private static final String PSM_PSD = "psm@B2C@001";
    private static final String SYSTEM_CODE_B2C001 = "B2C001";
    private static final String MGS_ID = "msgId";
    private static final String SYSTEM_CODE = "systemCode";
    private static final String TIME_STAMP = "timestamp";
    private static final String VERIFY_CODE = "verifyCode";

    @Override
    public PriSugComB2CResponse addPriSugComB2C(PriSugComB2CRequest req) {
        return invokeHttpClient(req, config.URL_PASSAGER_API + PassagerConfig.SPR_SUG_COM_B2C,
                new TypeToken<PriSugComB2CResponse>() {
                }.getType(),
                getHeaderMap()
        );
    }
    @Override
    public LostItemsQueryResponse lostAndFoundQuery(LostItemsRequest req){
        Map<String, String> headMap = getHeaderMap();
        return invokeHttpClient(req, config.URL_PASSAGER_API + PassagerConfig.LOST_AND_FOUND_QUERY,
                new TypeToken<LostItemsQueryResponse>() {
                }.getType(),
                headMap
        );
    }

    @NotNull
    private static Map<String, String> getHeaderMap() {
        String systemCode = SYSTEM_CODE_B2C001;
        UUID uuid = UUID.randomUUID();
        String msgId = String.valueOf(uuid);
        String cuurentTime = String.valueOf(System.currentTimeMillis());
        String pwd = EncoderHandler.encodeByMD5(PSM_PSD);
        String verifyCode = EncoderHandler.encodeByMD5(systemCode + pwd + cuurentTime + msgId);
        Map<String, String> headMap = new HashMap<>();
        headMap.put(MGS_ID, msgId);
        headMap.put("from", "B2C");
        headMap.put(SYSTEM_CODE, systemCode);
        headMap.put(TIME_STAMP, cuurentTime);
        headMap.put(VERIFY_CODE, verifyCode);
        return headMap;
    }

    @Override
    public LostItemsResponse checkInitemsLost(LostItemsRequest req){
        Map<String, String> headMap = getHeaderMap();
        return invokeHttpClient(req, config.URL_PASSAGER_API + PassagerConfig.CHECK_IN_ITEMS_LOST,
                new TypeToken<LostItemsResponse>() {
                }.getType(),
                headMap
        );
    }

    @Override
    public CustLinkInfo getCustLink(String cityCode) {
        Map<String, String> headMap = getHeaderMap();
        Map<String, String> param = Maps.newHashMap();
        param.put("airport", cityCode);
        PsmBaseRequest<Map<String, String>> baseRequest = new PsmBaseRequest<>();
        baseRequest.setData(param);
        TypeReference<PsmBaseResult<CustLinkInfo>> typeReference = new TypeReference<PsmBaseResult<CustLinkInfo>>() {
        };
        PsmBaseResult<CustLinkInfo> psmBaseResult = invokeHttp(baseRequest, config.URL_PASSAGER_API + PassagerConfig.GET_CUST_LINK, typeReference, headMap);
        if (0 != psmBaseResult.getCode()) {
            throw new MultiLangServiceException(psmBaseResult.getMsg());
        }
        return psmBaseResult.getData();
    }

    @Override
    public void addNotDisturbRecord(NotDisturbParam notDisturbParam) {
        Map<String, String> headMap = getHeaderMap();
        TypeReference<PsmBaseResult<Boolean>> typeReference = new TypeReference<PsmBaseResult<Boolean>>() {
        };
        PsmBaseRequest<NotDisturbParam> baseRequest = new PsmBaseRequest<>();
        baseRequest.setData(notDisturbParam);
        PsmBaseResult<Boolean> psmBaseResult = invokeHttp(baseRequest, config.URL_PASSAGER_API + PassagerConfig.ADD_CUST_NOT_DISTURB_RECORD, typeReference, headMap);
        if (0 != psmBaseResult.getCode()) {
            throw new MultiLangServiceException(psmBaseResult.getMsg());
        }
        if (!Boolean.TRUE.equals(psmBaseResult.getData())) {
            throw MultiLangServiceException.fail("免打扰记录添加失败");
        }
    }
}
