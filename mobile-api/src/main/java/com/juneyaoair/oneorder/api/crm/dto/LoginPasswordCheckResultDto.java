package com.juneyaoair.oneorder.api.crm.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/4 10:32
 */
@Data
public class LoginPasswordCheckResultDto {
    @ApiModelProperty(value = "登录密码验证结果:true-通过，false-失败")
    private boolean verifyResult;
    @ApiModelProperty(value = "会员卡号",notes = "存在账户时返回")
    private String MemberId;
    private Long Id;
    @ApiModelProperty(value = "是否有效账户:true-未关闭，false-关闭")
    private Boolean ValidAccount;
}
