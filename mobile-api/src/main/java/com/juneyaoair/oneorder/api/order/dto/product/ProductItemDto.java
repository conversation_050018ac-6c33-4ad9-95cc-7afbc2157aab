package com.juneyaoair.oneorder.api.order.dto.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ApiModel(value = "产品信息")
public class ProductItemDto {
    @ApiModelProperty(value = "产品ID")
    @NotBlank(message = "产品ID不能为空")
    private String productId;

    @ApiModelProperty(value = "产品类型")
    @NotBlank(message = "产品类型不能为空")
    private String productType;

    @ApiModelProperty(value = "销售价格")
    @NotNull(message = "销售价格不能为空")
    private BigDecimal salePrice;
    /*CNY*/
    @ApiModelProperty(value = "币种")
    private String currencyCode;

    @ApiModelProperty(value = "销售数量")
    @NotNull(message = "销售数量不能为空")
    private Double saleCount;

/*    @ApiModelProperty(value = "是否整售")
    @NotBlank(message = "是否整售标识不能为空")
    private String isWholeSale;*/

    @Valid
    @ApiModelProperty(value = "预定客票信息")
    private BookedTicketDto bookedTicketInfo;

}