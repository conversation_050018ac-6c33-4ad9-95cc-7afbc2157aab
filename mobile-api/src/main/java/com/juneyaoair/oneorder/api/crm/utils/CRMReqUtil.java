package com.juneyaoair.oneorder.api.crm.utils;


import com.juneyaoair.oneorder.api.crm.dto.CrmRequestDto;
import com.juneyaoair.oneorder.common.constant.ContactTypeEnum;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.crm.dto.Header;
import com.juneyaoair.oneorder.crm.dto.PtApiCRMRequest;
import com.juneyaoair.oneorder.crm.dto.common.MemberBasicInfoSoaModel;
import com.juneyaoair.oneorder.crm.dto.common.MemberCertificateSoaModelV2;
import com.juneyaoair.oneorder.crm.dto.common.MemberContactSoaModel;
import com.juneyaoair.oneorder.crm.dto.request.CrmMemberBaseApiRequest;
import com.juneyaoair.oneorder.crm.dto.request.PtCrmMileageRequest;
import com.juneyaoair.oneorder.crm.dto.request.PtMemberDetailRequest;
import com.juneyaoair.oneorder.crm.dto.response.PtMemberDetail;
import com.juneyaoair.oneorder.crm.dto.response.QueryCommonPersonInfo;
import com.juneyaoair.oneorder.order.dto.MemberCertificateResModel;
import com.juneyaoair.oneorder.tools.utils.HoAirUuidUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description CRM通用请求类
 * @Date 10:33 2023/6/20
 **/

public class CRMReqUtil {
    /**
     * 通用请求头设置  不包含登录信息
     *
     * @param channelCode
     * @return
     */
    public static <T> PtApiCRMRequest<T>  buildCommReqNoToken(String ip, String ffpId,String channelCode, T data,String pwd) {
        PtApiCRMRequest<T> ptApiCRMRequest = new PtApiCRMRequest<>();
        Header header = buildHeader(ip, ffpId, "");
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(channelCode);
        ptApiCRMRequest.setChannelPwd(pwd);
        ptApiCRMRequest.setData(data);
        return ptApiCRMRequest;
    }
    /**
     * 根据手机号查询会员详情请求类
     * 基本信息
     *
     * @param phone
     * @param channelCode
     * @return
     */
    public static PtApiCRMRequest<PtMemberDetailRequest> buildMemberDetailReq(String phone, String ip, String channelCode, String channelPwd,String[] items) {
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setMobile(phone);
        ptMemberDetailRequest.setRequestItems(items);
        Header header = buildHeader(ip, "-1", "");
        PtApiCRMRequest ptApiCRMRequest = createPtApiCRMRequest(header, channelCode, channelPwd,ptMemberDetailRequest);
        return ptApiCRMRequest;
    }

    /**
     * CRM通用会员请求
     *
     * @param header
     * @param channelCode
     * @param param       业务参数
     */
    private static PtApiCRMRequest createPtApiCRMRequest(Header header, String channelCode, String channelPwd,Object param) {
        if (ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(channelCode)) {
            channelCode = ChannelCodeEnum.MOBILE.getChannelCode();
        }
        PtApiCRMRequest ptApiCRMRequest = new PtApiCRMRequest();
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(channelCode);
        ptApiCRMRequest.setChannelPwd(channelPwd);
        ptApiCRMRequest.setData(param);
        return ptApiCRMRequest;
    }

    /**
     * 通用请求头设置
     *
     * @param ip          ip地址
     * @param channelCode 渠道号
     * @param ffpId       会员ID
     * @param token       请求token
     * @return
     */
    @SuppressWarnings("rawtypes")
    public static PtApiCRMRequest buildCommReq(String ip, String channelCode, String channelPwd,String ffpId, String token) {
        PtApiCRMRequest ptApiCRMRequest = new PtApiCRMRequest();
        Header header = buildHeader(ip, ffpId, token);
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(channelCode);
        ptApiCRMRequest.setChannelPwd(channelPwd);
        return ptApiCRMRequest;
    }

    /**
     * 通用请求头设置
     *
     * @param ip          ip地址
     * @param channelCode 渠道号
     * @return
     */
    public static PtCrmMileageRequest buildCommCrmReq(String ip, String channelCode,String channelPwd) {
        PtCrmMileageRequest ptCrmMileageRequest = new PtCrmMileageRequest();
        ptCrmMileageRequest.setChannel(channelCode);
        ptCrmMileageRequest.setChannelPwd(channelPwd);
        ptCrmMileageRequest.setClientIP(ip);
        SimpleDateFormat dateFormatGmt = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        dateFormatGmt.setTimeZone(TimeZone.getTimeZone("GMT"));
        ptCrmMileageRequest.setTimestamp(dateFormatGmt.format(new Date()));
        ptCrmMileageRequest.setVersion("V1.0");
        ptCrmMileageRequest.setOperatorUid("");
        ptCrmMileageRequest.setRandomCode("");
        return ptCrmMileageRequest;
    }


    /**
     * 会员Header设置
     *
     * @param ip ip地址
     * @return
     */
    public static Header buildHeader(String ip, String ffpId, String token) {
        Header header = new Header();
        header.setClientIP(ip);
        header.setClientVersion("");
        if (StringUtils.isBlank(ffpId)) {
            ffpId = "-1";
        }
        header.setMemberId(Long.valueOf(ffpId));
        header.setToken(token);
        header.setTimestamp(System.currentTimeMillis());
        return header;
    }

    /**
     * 展示会员姓名
     * 中文姓名>英文姓名>手机号>会员卡号
     *
     * @param memberDetail
     * @return
     */
    public static String formatMemName(PtMemberDetail memberDetail) {
        String name;
        MemberBasicInfoSoaModel basicInfo = memberDetail.getBasicInfo();
        //首选展示中文姓名
        name = basicInfo.getCLastName() + basicInfo.getCFirstName();
        //第二选择为英文姓名
        if (StringUtils.isBlank(name)
                && !StringUtils.isBlank(basicInfo.getELastName())
                && !StringUtils.isBlank(basicInfo.getEFirstName())) {
            return basicInfo.getELastName() + "/" + basicInfo.getEFirstName();
        }
        //姓名为空采用手机号
        if (StringUtils.isBlank(name)) {
            MemberContactSoaModel memberContactSoaModel = getContactInfo(memberDetail.getContactInfo(), ContactTypeEnum.MOBILE.getCode());
            if (memberContactSoaModel != null) {
                name = memberContactSoaModel.getContactNumber();
            }
        } else {
            return name;
        }
        //手机号为空采用卡号
        if (StringUtils.isBlank(name)) {
            name = basicInfo.getCardNO();
        } else {
            StringBuilder stringBuffer = new StringBuilder();
            if (null != name && name.length() > 7) {
                for (int i = 0; i < name.length() - 7; i++) {
                    stringBuffer.append("*");
                }
                name = name.replaceAll("([0-9]{3})([0-9]*)([0-9]{4})", "$1" + stringBuffer.toString() + "$3");
            }
            return name;
        }
        return name;
    }

    /**
     * 获取指定的联系方式
     *
     * @param contactSoaModelList
     * @param type
     * @return
     */
    public static MemberContactSoaModel getContactInfo(List<MemberContactSoaModel> contactSoaModelList, int type) {
        if (CollectionUtils.isEmpty(contactSoaModelList)) {
            return null;
        }
        return contactSoaModelList.stream().filter(memberContactSoaModel -> memberContactSoaModel.getContactType() == type).findFirst().orElse(null);
    }

    /**
     * 通用请求头设置  不包含登录信息
     *
     * @param channelCode 渠道号
     * @return
     */
    public static PtApiCRMRequest buildCommReqNoToken(String ip, String channelCode,String channelPwd) {
        PtApiCRMRequest ptApiCRMRequest = new PtApiCRMRequest();
        Header header = buildHeader(ip, "-1", "");
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(channelCode);
        ptApiCRMRequest.setChannelPwd(channelPwd);
        return ptApiCRMRequest;
    }

    /**
     * 筛选手机号
     *
     * @param contactSoaModelList
     * @return
     */
    public static MemberContactSoaModel toMemberContactSoaModel(List<MemberContactSoaModel> contactSoaModelList, int type) {
        if (CollectionUtils.isEmpty(contactSoaModelList)) {
            return null;
        }
        for (MemberContactSoaModel contactSoaModel : contactSoaModelList) {
            if (contactSoaModel.getContactType() == type) {
                return contactSoaModel;
            }
        }
        return null;
    }

    /**
     * 筛选证件信息
     *
     * @param certificateInfoList
     * @param type
     * @return
     */
    public static MemberCertificateSoaModelV2 filterCert(List<MemberCertificateSoaModelV2> certificateInfoList, int type) {
        if (CollectionUtils.isEmpty(certificateInfoList)) {
            return null;
        } else {

            Map<Integer, List<MemberCertificateSoaModelV2>> memberCertificateSoaModelV2Map = certificateInfoList.stream().collect(Collectors.groupingBy(MemberCertificateSoaModelV2::getCertificateType));
            for (Map.Entry<Integer, List<MemberCertificateSoaModelV2>> entry : memberCertificateSoaModelV2Map.entrySet()) {
                List<MemberCertificateSoaModelV2> memberCertificateSoaModels = entry.getValue();
                if (!CollectionUtils.isEmpty(memberCertificateSoaModels)) {
                    MemberCertificateSoaModelV2 memberCertificateResModel = memberCertificateSoaModels.stream().max(Comparator.comparing(MemberCertificateSoaModelV2::getOperateDate)).orElse(null);
                    if (memberCertificateResModel != null) {
                        if (type == (memberCertificateResModel.getCertificateType())) {
                            return memberCertificateResModel;
                        }
                    }
                }
            }
        }
        return null;
    }

    public static CrmMemberBaseApiRequest buildCommCrmMemberReq(String ip, String channelCode,String channelPwd) {
        return buildCommCrmMemberReq(ip,channelCode,channelPwd,null);
    }

    public static <T> CrmMemberBaseApiRequest<T> buildCommCrmMemberReq(String ip, String channelCode,String channelPwd,T data) {
        CrmMemberBaseApiRequest<T> crmMemberBaseApiRequest = new CrmMemberBaseApiRequest<>();
        crmMemberBaseApiRequest.setChannel(channelCode);
        crmMemberBaseApiRequest.setChannelPwd(channelPwd);
        crmMemberBaseApiRequest.setClientIp(ip);
        SimpleDateFormat dateFormatGmt = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        dateFormatGmt.setTimeZone(TimeZone.getTimeZone("GMT"));
        crmMemberBaseApiRequest.setTimestamp(dateFormatGmt.format(new Date()));
        crmMemberBaseApiRequest.setVersion("V1.0");
        crmMemberBaseApiRequest.setOperatorUid("");
        crmMemberBaseApiRequest.setRandomCode("");
        crmMemberBaseApiRequest.setData(data);
        return crmMemberBaseApiRequest;
    }

    public static PtApiCRMRequest<PtMemberDetailRequest> buildMemberDetailReq(String cardNo, String ffpId, String ip, String channelCode, String channelPwd,String[] items) {
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(cardNo);
        ptMemberDetailRequest.setRequestItems(items);
        Header header = buildHeader(ip, ffpId, null);

        PtApiCRMRequest ptApiCRMRequest = createPtApiCRMRequest(header, channelCode, channelPwd,ptMemberDetailRequest);
        return ptApiCRMRequest;
    }

    /**
     * 英文姓名匹配
     *
     * @param memberInfo
     * @param commonPersonInfo
     * @return
     */
    public static boolean matchEnName(MemberBasicInfoSoaModel memberInfo, QueryCommonPersonInfo commonPersonInfo) {
        if (StringUtils.isNotBlank(commonPersonInfo.getPassEnNameS()) && StringUtils.isNotBlank(commonPersonInfo.getPassEnNameF())) {
            return (commonPersonInfo.getPassEnNameS().equals(memberInfo.getELastName()) && commonPersonInfo.getPassEnNameF().equals(memberInfo.getEFirstName()));
        } else {
            return false;
        }
    }

    /**
     * 会员中文姓名
     *
     * @param basicInfo
     * @return
     */
    public static String getChinaName(MemberBasicInfoSoaModel basicInfo) {
        return (basicInfo.getCLastName() == null ? "" : basicInfo.getCLastName()) + (basicInfo.getCFirstName() == null ? "" : basicInfo.getCFirstName());
    }

    /**
     * 会员姓名处理
     *
     * @param basicInfo
     * @return
     */
    public static String getMemberName(MemberBasicInfoSoaModel basicInfo) {
        String name = getChinaName(basicInfo);
        if (StringUtils.isBlank(name)) {
            name = (basicInfo.getELastName() == null ? "" : basicInfo.getELastName()) + "/" + (basicInfo.getEFirstName() == null ? "" : basicInfo.getEFirstName());
        }
        return name;
    }

    /**
     * java 版会员基础请求体
     * @param channel
     * @param channelPwd
     * @param clientIp
     * @param data
     * @return
     * @param <T>
     */
    public static <T> CrmRequestDto<T> buildCrmRequestDto(String channel,String channelPwd,String clientIp,T data){
        CrmRequestDto crmRequestDto = new CrmRequestDto();
        crmRequestDto.setVersion("V1.0");
        crmRequestDto.setChannel(channel);
        crmRequestDto.setChannelPwd(channelPwd);
        crmRequestDto.setClientIp(clientIp);
        crmRequestDto.setTimestamp(System.currentTimeMillis());
        crmRequestDto.setRandomCode(HoAirUuidUtil.randomUUID8());
        crmRequestDto.setData(data);
        return crmRequestDto;
    }

}
