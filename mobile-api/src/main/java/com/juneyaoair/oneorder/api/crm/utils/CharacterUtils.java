package com.juneyaoair.oneorder.api.crm.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName CharacterUtils
 * @Description
 * <AUTHOR>
 * @Date 2023/6/27 14:50
 * @Version 1.0
 */
public class CharacterUtils {
    /**
     * 判断字符中是否包含指定的正则
     * @param str 原始字符
     * @param regex 正则表达式
     * @return
     */
    public static boolean isContainStr(String str,String regex){
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(str);
        return m.find();
    }
}
