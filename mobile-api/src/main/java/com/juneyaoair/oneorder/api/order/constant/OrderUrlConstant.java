package com.juneyaoair.oneorder.api.order.constant;

/**
 * @ClassName OrderUrlConstant
 * @Description 订单/运价 系统常用请求地址
 * <AUTHOR>
 * @Date 2023/6/27 14:21
 * @Version 1.0
 */
public interface OrderUrlConstant {
    String ORDER_VERSION = "10";
    /**
     * 客票信息查询
     */
    String QUERY_TICKET_INFO = "/GetTicketInfo";

    /**
     * <AUTHOR>
     * @Description 增加常用旅客信息
     * @Date 10:49 2023/7/3
     * @param null
     * @return
     * @return null
     **/
    String ADD_COMMON_PERSON_V20 = "/AddGeneralContact";

    String MODIFY_COMMON_PERSON_V20= "/ModifyGeneralContact"; // 修改/删除常用旅客信息

    String QUERY_UNLIMITED_FLY_BING_RECORD = "/UnlimitedFly/QueryFlyCardBindingRecord";//查询儿童畅飞卡绑定记录

    String QUERY_UNLIMITED_FLY_BING_RECORD_V2 = "/UnlimitedFly/QueryFlyCardBindingRecordV2";//查询畅飞卡绑定记录 2020-11-17

    String QUERY_OWN_SALE= "/QueryOwnSale"; //自营客票判断

    String SUB_QUERY_COUPON_ACTIVITY = "/QueryCouponActivity"; // 优惠券活动查询

    String COUPON_QUERY= "/QueryCouponProduct/queryProduct"; //权益券

    /** 新权益券订单详情查询接口 */
    String BASIC_GET_COUPON_ORDER = "/BasicGetCouponOrder";

    /** 是否购买航意险接口 */
    String QUERY_IS_BUY_INSURANCE = "/QueryIsBuyInsurance";

    String BASIC_CREATE_COUPON_ORDER = "/BasicCreateCouponOrder";
    /** 单独退保 */
    String SUB_REFUND_INSURE = "/RefundInsurance";
    /**机票退改规则查询**/
    String QUERY_TICKET_RULE_INFO = "/QueryTicketRuleInfo";
    /**日本接送机查询报价接口 */
    String BUS_ORDER_OFFER_INFO = "/busOrder/offerInfo";
    /**日本接送机查询地址接口 */
    String BUS_ORDER_ADDRESS_QUERY= "/busOrder/addressQuery";
    /**获取会员行程信息*/
    String  GET_MEMBER_TRAVELLER_TRIP="/travellerTrip/getMemberTravellerTrip";
}
