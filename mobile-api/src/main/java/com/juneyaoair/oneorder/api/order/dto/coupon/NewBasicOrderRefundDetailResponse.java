package com.juneyaoair.oneorder.api.order.dto.coupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: caolei
 * @Description: 调用新的订单退单详情（/Order/BasicGetCouponRefund）返回结果
 * @Date: 2021/8/12 14:07
 * @Modified by:
 */
@Data
public class NewBasicOrderRefundDetailResponse {
    @ApiModelProperty(value = "退款编号")
    private String RebateNo;
    @ApiModelProperty(value = "退款状态")
    private String RebateState;
    @ApiModelProperty(value = "退款前端状态")
    private String RefundState;
    @ApiModelProperty(value = "退款状态名")
    private String OrderStateName;
    @ApiModelProperty(value = "新退款状态")
    private String CouponState;
    @ApiModelProperty(value = "描述信息")
    private String TextDescription;
    @ApiModelProperty(value = "申请时间")
    private String RefundApplyDatetime;
    @ApiModelProperty(value = "是否自愿退票 Y：自愿 N：非自愿")
    private String IsVoluntaryRefund;
    @ApiModelProperty(value = "国际码")
    private String PhoneCountryCode;
    @ApiModelProperty(value = "联系电话")
    private String LinkTelphone;
    @ApiModelProperty(value = "创建订单时自定义jsonString")
    private String Remark;
    @ApiModelProperty(value = "乘客姓名")
    private String PassengerName;
    @ApiModelProperty(value = "优惠券销售金额")
    private String CouponSaleAmount;
    @ApiModelProperty(value = "现充积分")
    private String BoughtScore;
    @ApiModelProperty(value = "使用积分")
    private String FfpUseScore;
    @ApiModelProperty(value = "航班号")
    private String FlightNo;
    @ApiModelProperty(value = "起飞机场三字码")
    private String DepAirport;
    @ApiModelProperty(value = "到达机场三字码")
    private String ArrAirport;
    @ApiModelProperty(value = "航班日期")
    private String FlightDate;
    @ApiModelProperty(value = "支付方式")
    private String PayMethod;
    @ApiModelProperty(value = "支付时间")
    private String PaidDateTime;
    @ApiModelProperty(value = "支付超时时间/单位（分钟）")
    private Integer PayDatetimeLimit;
}