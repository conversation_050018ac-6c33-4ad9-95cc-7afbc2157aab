package com.juneyaoair.oneorder.util;

import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.common.dto.RequestInterface;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;

/**
 * <AUTHOR>
 */
public class LoginCheckUtil {
    private LoginCheckUtil(){}

    /**
     * 检查接口是否登录 未登录情况只支持国际官网渠道访问
     * @param requestInterface
     * @param canUnLoginEnums
     */
    public static void checkLogin(RequestInterface requestInterface, ChannelCodeEnum ... canUnLoginEnums) {
        // 已登录直接返回
        if (requestInterface.isLoginFlag()) {
            return;
        }
        // 不存在 许可未登录访问的渠道 返回未登录
        if (null == canUnLoginEnums) {
            throw new MultiLangServiceException(CommonErrorCode.INVALID_TOKEN, "暂不支持未登录访问");
        }
        for (ChannelCodeEnum channelCodeEnum : canUnLoginEnums) {
            if (channelCodeEnum.getChannelCode().equals(requestInterface.getChannelNo())) {
                return;
            }
        }
        // 非许可未登录访问的渠道 返回未登录
        throw new MultiLangServiceException(CommonErrorCode.INVALID_TOKEN, "暂不支持未登录访问");
    }
}
