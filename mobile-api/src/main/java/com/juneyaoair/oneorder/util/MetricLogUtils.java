package com.juneyaoair.oneorder.util;

import com.alibaba.fastjson2.JSONObject;
import com.juneyaoair.oneorder.common.dto.RequestInterface;
import com.juneyaoair.oneorder.core.context.SecurityContextHolder;
import com.juneyaoair.bff.util.HoLogUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class MetricLogUtils {
    private MetricLogUtils(){
    }

    /**
     * 打印日志
     * @param name
     * @param requestData
     */
    public static void saveMetricLog(String name, RequestInterface requestData) {
        saveMetricLog(name, null, requestData);
    }

    /**
     * 打印日志
     * @param name
     * @param requestData
     */
    public static void saveMetricLog(String name, JSONObject tags, RequestInterface requestData) {
        if (null == tags) {
            tags = new JSONObject();
        }
        tags.put("IP" ,requestData.getOriginIp());
        tags.put("FfpCardNo", requestData.getFfpNo());
        String channelCode = SecurityContextHolder.getChannelCode();
        tags.put("ChannelCode", StringUtils.isBlank(channelCode) ? requestData.getChannelNo() : channelCode);
        HoLogUtil.saveMetricLog(name, tags, BigDecimal.ONE);
    }
}
