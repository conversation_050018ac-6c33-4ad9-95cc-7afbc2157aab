package com.juneyaoair.oneorder.util;

import java.net.InetAddress;
import java.net.UnknownHostException;

public class CIDRUtils {

    private final int prefix;
    private final int mask;
    private final int baseIp;

    public CIDRUtils(String cidr) throws UnknownHostException {
        String[] parts = cidr.split("/");
        prefix = ipToInt(InetAddress.getByName(parts[0]));
        int subnetBits = Integer.parseInt(parts[1]);
        mask = -1 << (32 - subnetBits);
        baseIp = prefix & mask;
    }

    public boolean contains(String ipStr) throws UnknownHostException {
        int ip = ipToInt(InetAddress.getByName(ipStr));
        return (ip & mask) == baseIp;
    }

    public int ipToInt(InetAddress address) {
        byte[] bytes = address.getAddress();
        return ((bytes[0] & 0xFF) << 24) |
                ((bytes[1] & 0xFF) << 16) |
                ((bytes[2] & 0xFF) << 8)  |
                (bytes[3] & 0xFF);
    }
}
