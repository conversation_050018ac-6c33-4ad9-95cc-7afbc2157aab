package com.juneyaoair.oneorder.util;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.flightbasic.response.api.ApiAirPortInfoDto;
import com.juneyaoair.oneorder.common.dto.enums.InterFlagEnum;
import com.juneyaoair.oneorder.ticket.ChangeAndRefundRule;
import com.juneyaoair.oneorder.api.order.dto.ticket.TicketRule;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import com.juneyaoair.oneorder.tools.utils.NumberUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 航班展示相关处理类
 */
public class FlightUtil {
    /**
     * 航站楼展示格式化
     *
     * @param terminal
     * @return
     */
    public static String formatTerminal(String terminal) {
        if (StringUtils.isBlank(terminal)) {
            return "";
        }
        if ("--".equals(terminal)) {
            return "";
        }
        return terminal;
    }

    //夏令时时区调整
    public static String convertSummerOrWinterTime(String zone, String date, ApiAirPortInfoDto airPortInfo) {
        if (airPortInfo != null) {
            //对于信息不全的不做任何计算
            if (StringUtils.isBlank(airPortInfo.getDstMsg()) || StringUtils.isBlank(airPortInfo.getDstOffset())) {
                return zone;
            } else {
                try {
                    if (StringUtils.isNotBlank(airPortInfo.getDstMsg())) {
                        Date depDate = DateUtil.toDate(date, DateUtil.YYYY_MM_DD_HH_MM_PATTERN);
                        List<Map<String, String>> list = (List<Map<String, String>>) HoAirGsonUtil.jsonToList(airPortInfo.getDstMsg(), new TypeToken<List<Map<String, String>>>() {
                        }.getType());
                        if (CollectionUtils.isNotEmpty(list)) {
                            for (Map<String, String> map : list) {
                                Date startDate = DateUtil.toDate(map.get("dstStart") + " 00:00", DateUtil.YYYY_MM_DD_HH_MM_PATTERN);
                                Date endDate = DateUtil.toDate(map.get("dstEnd") + " 23:59", DateUtil.YYYY_MM_DD_HH_MM_PATTERN);
                                if (startDate != null && endDate != null && depDate.getTime() >= startDate.getTime() && depDate.getTime() <= endDate.getTime()) {
                                    return String.valueOf(Double.parseDouble(zone) + Double.parseDouble(airPortInfo.getDstOffset()));
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    return zone;
                }
            }
        }
        return zone;
    }

    /**
     * 将统一订单的退票规则转换为前端需要展示的改期规则
     *
     * @param ptRefundRuleInfoList
     * @param interFlag            国内国际标识
     * @return
     */
    public static List<ChangeAndRefundRule> toRefundRules(List<TicketRule> ptRefundRuleInfoList, String interFlag) {
        List<ChangeAndRefundRule> changeAndRefundRuleList = new ArrayList<>();
        if (CollectionUtils.isEmpty(ptRefundRuleInfoList)) {
            return changeAndRefundRuleList;
        }
        //过滤为空的条件
        List<TicketRule> ptRefundRuleInfos = ptRefundRuleInfoList.stream().filter(r -> StringUtils.isNotBlank(r.getFlightTimeCondition())
                || StringUtils.isNotBlank(r.getTicketUsage())).collect(Collectors.toList());
        //按照起飞前，起飞后分组
        Map<String, List<TicketRule>> ptRefundRuleInfoMap = ptRefundRuleInfos.stream().collect((Collectors.groupingBy(ptRefundRuleInfo -> ptRefundRuleInfo.getFlightTimeCondition())));
        ptRefundRuleInfos.clear();
        List<TicketRule> ptRefundRuleInfoList0 = new ArrayList<>();
        List<TicketRule> ptRefundRuleInfoList1 = new ArrayList<>();
        for (Map.Entry<String, List<TicketRule>> entry : ptRefundRuleInfoMap.entrySet()) {
            String key = entry.getKey();
            //按照时间条件分组
            Map<String, List<TicketRule>> ptRefundRuleInfoMapTemp = entry.getValue().stream().collect((Collectors.groupingBy(ptRefundRuleInfo -> ptRefundRuleInfo.getTimeConditionStart().split("/")[1])));
            for (Map.Entry<String, List<TicketRule>> tmp : ptRefundRuleInfoMapTemp.entrySet()) {
                if (key.equals("0")) {
                    tmp.getValue().sort(Comparator.comparing((TicketRule ptRefundRuleInfo) -> null != ptRefundRuleInfo.getTimeConditionStart() ? Integer.parseInt(ptRefundRuleInfo.getTimeConditionStart().split("/")[0]) : 1).reversed());
                } else {
                    tmp.getValue().sort(Comparator.comparing((TicketRule ptRefundRuleInfo) -> null != ptRefundRuleInfo.getTimeConditionStart() ? Integer.parseInt(ptRefundRuleInfo.getTimeConditionStart().split("/")[0]) : 1));
                }
            }
            if (key.equals("0")) {
                addRefundInfo(ptRefundRuleInfoList0, ptRefundRuleInfoMapTemp);
            } else {
                addRefundInfo(ptRefundRuleInfoList1, ptRefundRuleInfoMapTemp);
            }
        }
        //规则重新整合
        ptRefundRuleInfos.addAll(ptRefundRuleInfoList0);
        ptRefundRuleInfos.addAll(ptRefundRuleInfoList1);
        int i = 0;
        for (TicketRule ptRefundRuleInfo : ptRefundRuleInfos) {
            ChangeAndRefundRule changeRule = null;
            //起飞前
            if ("0".equals(ptRefundRuleInfo.getFlightTimeCondition())) {
                String leftTime = "0";
                String rightTime = "0";
                String originalLeftTimeUnit = "";
                String originalRightTimeUnit = "";
                String leftTimeUnit = "";
                String rightTimeUnit = "";
                if (StringUtils.isNotBlank(ptRefundRuleInfo.getTimeConditionEnd())) {
                    String[] timeConditionArray = splitTimeCondition(ptRefundRuleInfo.getTimeConditionEnd());
                    originalLeftTimeUnit = timeConditionArray[1];
                    leftTimeUnit = convertTimeUnitName(timeConditionArray[1]);//时间单位
                    leftTime = timeConditionArray[0];
                }
                if (StringUtils.isNotBlank(ptRefundRuleInfo.getTimeConditionStart())) {
                    String[] timeConditionArray = splitTimeCondition(ptRefundRuleInfo.getTimeConditionStart());
                    originalRightTimeUnit = timeConditionArray[1];
                    rightTimeUnit = convertTimeUnitName(timeConditionArray[1]);//时间单位
                    rightTime = timeConditionArray[0];
                }
                changeRule = new ChangeAndRefundRule();
                changeRule.setFlightFlag(-1);
                changeRule.setTimeConditionStart(ptRefundRuleInfo.getTimeConditionStart());
                changeRule.setTimeConditionEnd(ptRefundRuleInfo.getTimeConditionEnd());
                changeRule.setLeftTime(leftTime);
                changeRule.setLeftTimeUnit(leftTimeUnit);
                changeRule.setOriginalLeftTimeUnit(originalLeftTimeUnit);
                changeRule.setRightTime(rightTime);
                changeRule.setRightTimeUnit(rightTimeUnit);
                changeRule.setOriginalRightTimeUnit(originalRightTimeUnit);
                if (StringUtils.isBlank(leftTimeUnit) && StringUtils.isNotBlank(rightTimeUnit)) {
                    changeRule.setDesc("起飞前" + rightTime + rightTimeUnit + toHoursDesc(rightTime, originalRightTimeUnit) + "之前");
                    if (InterFlagEnum.I.getCode().equals(interFlag) && "0".equals(rightTime)) {
                        changeRule.setDesc("起飞前");
                    }
                }
                if (StringUtils.isNotBlank(leftTimeUnit) && StringUtils.isNotBlank(rightTimeUnit)) {
                    if (leftTimeUnit.equals(rightTimeUnit)) {
                        changeRule.setDesc("起飞前" + rightTime + toHoursDesc(rightTime, originalRightTimeUnit) + "-" + leftTime + rightTimeUnit + toHoursDesc(leftTime, originalRightTimeUnit) + "以内");
                    } else {
                        changeRule.setDesc("起飞前" + rightTime + rightTimeUnit + toHoursDesc(rightTime, originalRightTimeUnit) + "-" + leftTime + leftTimeUnit + toHoursDesc(leftTime, originalLeftTimeUnit) + "以内");
                    }
                    if ("0".equals(rightTime)) {
                        changeRule.setDesc("起飞前" + leftTime + leftTimeUnit + toHoursDesc(leftTime, originalLeftTimeUnit) + "以内");
                    }
                    if (i == 0) {
                        changeRule.setDesc("起飞前" + rightTime + rightTimeUnit + toHoursDesc(rightTime, originalRightTimeUnit) + "之前");
                    }
                }
                changeRule.setChangeFee(ptRefundRuleInfo.getFee());
                convertFreeCabinRule(ptRefundRuleInfo, changeRule);
            }
            //起飞后
            if ("1".equals(ptRefundRuleInfo.getFlightTimeCondition())) {
                String leftTime = "0";
                String rightTime = "0";
                String originalLeftTimeUnit = "";
                String originalRightTimeUnit = "";
                String leftTimeUnit = "";
                String rightTimeUnit = "";
                if (StringUtils.isNotBlank(ptRefundRuleInfo.getTimeConditionStart())) {
                    String[] timeConditionArray = splitTimeCondition(ptRefundRuleInfo.getTimeConditionStart());
                    originalLeftTimeUnit = timeConditionArray[1];
                    leftTimeUnit = convertTimeUnitName(timeConditionArray[1]);
                    leftTime = timeConditionArray[0];
                }
                if (StringUtils.isNotBlank(ptRefundRuleInfo.getTimeConditionEnd())) {
                    String[] timeConditionArray = splitTimeCondition(ptRefundRuleInfo.getTimeConditionEnd());
                    originalRightTimeUnit = timeConditionArray[1];
                    rightTimeUnit = convertTimeUnitName(timeConditionArray[1]);
                    rightTime = timeConditionArray[0];
                }
                changeRule = new ChangeAndRefundRule();
                changeRule.setFlightFlag(1);
                changeRule.setTimeConditionStart(ptRefundRuleInfo.getTimeConditionStart());
                changeRule.setTimeConditionEnd(ptRefundRuleInfo.getTimeConditionEnd());
                changeRule.setLeftTime(leftTime);
                changeRule.setLeftTimeUnit(leftTimeUnit);
                changeRule.setOriginalLeftTimeUnit(originalLeftTimeUnit);
                changeRule.setRightTime(rightTime);
                changeRule.setRightTimeUnit(rightTimeUnit);
                changeRule.setOriginalRightTimeUnit(originalRightTimeUnit);
                //注意TimeConditionStart的区间方向  TimeConditionEnd有值时TimeConditionStart代表右区间，TimeConditionEnd无值时TimeConditionStart代表左区间
                if (StringUtils.isBlank(rightTimeUnit) && StringUtils.isNotBlank(leftTimeUnit)) {
                    changeRule.setDesc("起飞后" + leftTime + leftTimeUnit + toHoursDesc(leftTime, originalLeftTimeUnit) + "之后");
                    if (InterFlagEnum.I.getCode().equals(interFlag) && "0".equals(leftTime)) {
                        changeRule.setDesc("起飞后");
                    }
                }
                //(TimeConditionEnd[rightTime]-TimeConditionStart[leftTime])
                if (StringUtils.isNotBlank(leftTimeUnit) && StringUtils.isNotBlank(rightTimeUnit)) {
                    if (leftTimeUnit.equals(rightTimeUnit)) {//时间单位合并
                        changeRule.setDesc("起飞后" + rightTime + toHoursDesc(rightTime, originalRightTimeUnit) + "-" + leftTime + rightTimeUnit + toHoursDesc(leftTime, originalRightTimeUnit) + "以内");
                    } else {
                        changeRule.setDesc("起飞后" + rightTime + rightTimeUnit + toHoursDesc(rightTime, originalRightTimeUnit) + "-" + leftTime + leftTimeUnit + toHoursDesc(leftTime, originalLeftTimeUnit) + "以内");
                    }
                }
                changeRule.setChangeFee(ptRefundRuleInfo.getFee());
                convertFreeCabinRule(ptRefundRuleInfo, changeRule);
            }
            if ("0".equals(ptRefundRuleInfo.getTicketUsage())) {
                changeRule = new ChangeAndRefundRule();
                if ("0".equals(ptRefundRuleInfo.getFlightTimeCondition())) {
                    changeRule.setDesc("全部未使用(起飞前)");
                } else if ("1".equals(ptRefundRuleInfo.getFlightTimeCondition())) {
                    changeRule.setDesc("全部未使用(起飞后)");
                } else {
                    changeRule.setDesc("全部未使用");
                }
                changeRule.setChangeFee(ptRefundRuleInfo.getFee());
            } else if ("1".equals(ptRefundRuleInfo.getTicketUsage())) {
                changeRule = new ChangeAndRefundRule();
                if ("0".equals(ptRefundRuleInfo.getFlightTimeCondition())) {
                    changeRule.setDesc("部分已使用(起飞前)");
                } else if ("1".equals(ptRefundRuleInfo.getFlightTimeCondition())) {
                    changeRule.setDesc("部分已使用(起飞后)");
                } else {
                    changeRule.setDesc("部分已使用");
                }
                changeRule.setChangeFee(ptRefundRuleInfo.getFee());
            }
            //遍历至最后一个规则时更改描述
            if (changeRule != null) {
                changeAndRefundRuleList.add(changeRule);
                //遍历至最后的规则时
                if ((i == ptRefundRuleInfos.size() - 1) && CollectionUtils.isNotEmpty(changeAndRefundRuleList)) {
                    int size = changeAndRefundRuleList.size();
                    if (size > 2) {
                        ChangeAndRefundRule preRule = changeAndRefundRuleList.get(size - 2);
                        //处于状态变换分界点时
                        if (preRule.getFlightFlag() != changeRule.getFlightFlag() && preRule.getChangeFee().doubleValue() == changeRule.getChangeFee().doubleValue()) {
                            if (preRule.getTimeConditionStart().equals(changeRule.getTimeConditionStart())) {
                                changeRule.setDesc("起飞前" + preRule.getLeftTime() + preRule.getLeftTimeUnit() + toHoursDesc(preRule.getLeftTime(), preRule.getOriginalLeftTimeUnit()) + "之后");//更改描述
                            }
                            changeAndRefundRuleList.remove(size - 2);
                        }
                    }
                }
            }

            i++;
        }
        return changeAndRefundRuleList;
    }

    /**
     * 将统一订单的改期规则转换为前端需要展示的改期规则
     *
     * @param ptChangeRuleInfoList
     * @param interFlag            国内国际标识
     */
    public static List<ChangeAndRefundRule> toChangeRules(List<TicketRule> ptChangeRuleInfoList, String interFlag) {
        List<ChangeAndRefundRule> changeAndRefundRuleList = new ArrayList<>();
        if (CollectionUtils.isEmpty(ptChangeRuleInfoList)) {
            return changeAndRefundRuleList;
        }
        List<TicketRule> ptChangeRuleInfoListnew = ptChangeRuleInfoList.stream().filter(c -> StringUtils.isNotBlank(c.getFlightTimeCondition())).sorted(Comparator.comparing(TicketRule::getFlightTimeCondition).thenComparingDouble(TicketRule::getFee)).collect(Collectors.toList());
        //按照起飞前，起飞后分组
        Map<String, List<TicketRule>> ptChangeRuleInfoMap = ptChangeRuleInfoListnew.stream().collect((Collectors.groupingBy(ptChangeRuleInfo -> ptChangeRuleInfo.getFlightTimeCondition())));
        ptChangeRuleInfoListnew.clear();
        List<TicketRule> ptChangeRuleInfoList0 = new ArrayList<>();
        List<TicketRule> ptChangeRuleInfoList1 = new ArrayList<>();
        for (Map.Entry<String, List<TicketRule>> entry : ptChangeRuleInfoMap.entrySet()) {
            String key = entry.getKey();
            //按照时间条件分组
            Map<String, List<TicketRule>> ptChangeRuleInfoMapTemp = entry.getValue().stream().collect((Collectors.groupingBy(ptChangeRuleInfo -> ptChangeRuleInfo.getTimeConditionStart().split("/")[1])));
            for (Map.Entry<String, List<TicketRule>> tmp : ptChangeRuleInfoMapTemp.entrySet()) {
                if (key.equals("0")) {
                    tmp.getValue().sort(Comparator.comparing((TicketRule ptChangeRuleInfo) -> null != ptChangeRuleInfo.getTimeConditionStart() ? Integer.parseInt(ptChangeRuleInfo.getTimeConditionStart().split("/")[0]) : 1).reversed());
                } else {
                    tmp.getValue().sort(Comparator.comparing((TicketRule ptChangeRuleInfo) -> null != ptChangeRuleInfo.getTimeConditionStart() ? Integer.parseInt(ptChangeRuleInfo.getTimeConditionStart().split("/")[0]) : 1));
                }
            }
            if (key.equals("0")) {
                addChangeInfo(ptChangeRuleInfoList0, ptChangeRuleInfoMapTemp);
            } else {
                addChangeInfo(ptChangeRuleInfoList1, ptChangeRuleInfoMapTemp);
            }
        }
        //规则重新整合
        ptChangeRuleInfoListnew.addAll(ptChangeRuleInfoList0);
        ptChangeRuleInfoListnew.addAll(ptChangeRuleInfoList1);
        int i = 0;
        for (TicketRule ptChangeRuleInfo : ptChangeRuleInfoListnew) {
            ChangeAndRefundRule changeRule = null;
            //起飞前
            if ("0".equals(ptChangeRuleInfo.getFlightTimeCondition())) {
                String leftTime = "0";
                String rightTime = "0";
                String originalLeftTimeUnit = "";
                String originalRightTimeUnit = "";
                String leftTimeUnit = "";
                String rightTimeUnit = "";
                if (StringUtils.isNotBlank(ptChangeRuleInfo.getTimeConditionEnd())) {//左区间
                    String[] timeConditionArray = splitTimeCondition(ptChangeRuleInfo.getTimeConditionEnd());
                    originalLeftTimeUnit = timeConditionArray[1];
                    leftTimeUnit = convertTimeUnitName(timeConditionArray[1]);//时间单位
                    leftTime = timeConditionArray[0];
                }
                if (StringUtils.isNotBlank(ptChangeRuleInfo.getTimeConditionStart())) {//右区间
                    String[] timeConditionArray = splitTimeCondition(ptChangeRuleInfo.getTimeConditionStart());
                    originalRightTimeUnit = timeConditionArray[1];
                    rightTimeUnit = convertTimeUnitName(timeConditionArray[1]);//时间单位
                    rightTime = timeConditionArray[0];
                }
                changeRule = new ChangeAndRefundRule();
                changeRule.setFlightFlag(-1);
                changeRule.setTimeConditionStart(ptChangeRuleInfo.getTimeConditionStart());
                changeRule.setTimeConditionEnd(ptChangeRuleInfo.getTimeConditionEnd());
                changeRule.setLeftTime(leftTime);
                changeRule.setLeftTimeUnit(leftTimeUnit);
                changeRule.setOriginalLeftTimeUnit(originalLeftTimeUnit);
                changeRule.setRightTime(rightTime);
                changeRule.setRightTimeUnit(rightTimeUnit);
                changeRule.setOriginalRightTimeUnit(originalRightTimeUnit);
                if (StringUtils.isBlank(leftTimeUnit) && StringUtils.isNotBlank(rightTimeUnit)) {
                    changeRule.setDesc("起飞前" + rightTime + rightTimeUnit + toHoursDesc(rightTime, originalRightTimeUnit) + "之前");
                    if (InterFlagEnum.I.getCode().equals(interFlag) && "0".equals(rightTime)) {
                        changeRule.setDesc("起飞前");
                    }
                }
                if (StringUtils.isNotBlank(leftTimeUnit) && StringUtils.isNotBlank(rightTimeUnit)) {
                    if (leftTimeUnit.equals(rightTimeUnit)) {
                        changeRule.setDesc("起飞前" + rightTime + toHoursDesc(rightTime, originalRightTimeUnit) + "-" + leftTime + rightTimeUnit + toHoursDesc(leftTime, originalRightTimeUnit) + "以内");
                    } else {
                        changeRule.setDesc("起飞前" + rightTime + rightTimeUnit + toHoursDesc(rightTime, originalRightTimeUnit) + "-" + leftTime + leftTimeUnit + toHoursDesc(leftTime, originalLeftTimeUnit) + "以内");
                    }
                    if ("0".equals(rightTime)) {
                        changeRule.setDesc("起飞前" + leftTime + leftTimeUnit + toHoursDesc(leftTime, originalLeftTimeUnit) + "以内");
                    }
                    //第一条规则默认只展示右区间数值
                    if (i == 0) {
                        changeRule.setDesc("起飞前" + rightTime + rightTimeUnit + toHoursDesc(rightTime, originalRightTimeUnit) + "之前");
                    }
                }
                changeRule.setChangeFee(ptChangeRuleInfo.getFee());
            }
            //起飞后
            if ("1".equals(ptChangeRuleInfo.getFlightTimeCondition())) {
                String leftTime = "0";
                String rightTime = "0";
                String originalLeftTimeUnit = "";
                String originalRightTimeUnit = "";
                String leftTimeUnit = "";
                String rightTimeUnit = "";
                if (StringUtils.isNotBlank(ptChangeRuleInfo.getTimeConditionStart())) {
                    String[] timeConditionArray = splitTimeCondition(ptChangeRuleInfo.getTimeConditionStart());
                    originalLeftTimeUnit = timeConditionArray[1];
                    leftTimeUnit = convertTimeUnitName(timeConditionArray[1]);
                    leftTime = timeConditionArray[0];
                }
                if (StringUtils.isNotBlank(ptChangeRuleInfo.getTimeConditionEnd())) {
                    String[] timeConditionArray = splitTimeCondition(ptChangeRuleInfo.getTimeConditionEnd());
                    originalRightTimeUnit = timeConditionArray[1];
                    rightTimeUnit = convertTimeUnitName(timeConditionArray[1]);
                    rightTime = timeConditionArray[0];
                }
                changeRule = new ChangeAndRefundRule();
                changeRule.setFlightFlag(1);
                changeRule.setTimeConditionStart(ptChangeRuleInfo.getTimeConditionStart());
                changeRule.setTimeConditionEnd(ptChangeRuleInfo.getTimeConditionEnd());
                changeRule.setLeftTime(leftTime);
                changeRule.setLeftTimeUnit(leftTimeUnit);
                changeRule.setOriginalLeftTimeUnit(originalLeftTimeUnit);
                changeRule.setRightTime(rightTime);
                changeRule.setRightTimeUnit(rightTimeUnit);
                changeRule.setOriginalRightTimeUnit(originalRightTimeUnit);
                //两者都有值时leftTime表示的是右区间
                if (StringUtils.isNotBlank(leftTimeUnit) && StringUtils.isNotBlank(rightTimeUnit)) {
                    if (leftTimeUnit.equals(rightTimeUnit)) {//时间单位合并
                        changeRule.setDesc("起飞后" + rightTime + toHoursDesc(rightTime, originalRightTimeUnit) + "-" + leftTime + rightTimeUnit + toHoursDesc(leftTime, originalRightTimeUnit) + "以内");
                    } else {
                        changeRule.setDesc("起飞后" + rightTime + rightTimeUnit + toHoursDesc(rightTime, originalRightTimeUnit) + "-" + leftTime + leftTimeUnit + toHoursDesc(leftTime, originalLeftTimeUnit) + "以内");
                    }
                }
                if (StringUtils.isBlank(rightTimeUnit) && StringUtils.isNotBlank(leftTimeUnit)) {
                    changeRule.setDesc("起飞后" + leftTime + leftTimeUnit + toHoursDesc(leftTime, originalLeftTimeUnit) + "之后");
                    if (InterFlagEnum.I.getCode().equals(interFlag) && "0".equals(leftTime)) {
                        changeRule.setDesc("起飞后");
                    }
                }
                changeRule.setChangeFee(ptChangeRuleInfo.getFee());
            }
            if ("0".equals(ptChangeRuleInfo.getTicketUsage())) {
                changeRule = new ChangeAndRefundRule();
                if ("0".equals(ptChangeRuleInfo.getFlightTimeCondition())) {
                    changeRule.setDesc("全部未使用(起飞前)");
                } else if ("1".equals(ptChangeRuleInfo.getFlightTimeCondition())) {
                    changeRule.setDesc("全部未使用(起飞后)");
                } else {
                    changeRule.setDesc("全部未使用");
                }
                changeRule.setChangeFee(ptChangeRuleInfo.getFee());
            } else if ("1".equals(ptChangeRuleInfo.getTicketUsage())) {
                changeRule = new ChangeAndRefundRule();
                if ("0".equals(ptChangeRuleInfo.getFlightTimeCondition())) {
                    changeRule.setDesc("部分已使用(起飞前)");
                } else if ("1".equals(ptChangeRuleInfo.getFlightTimeCondition())) {
                    changeRule.setDesc("部分已使用(起飞后)");
                } else {
                    changeRule.setDesc("部分已使用");
                }
                changeRule.setChangeFee(ptChangeRuleInfo.getFee());
            }
            //遍历至最后一个规则时更改描述
            if (changeRule != null) {
                changeAndRefundRuleList.add(changeRule);
                if ((i == ptChangeRuleInfoListnew.size() - 1) && CollectionUtils.isNotEmpty(changeAndRefundRuleList)) {
                    int size = changeAndRefundRuleList.size();
                    if (size > 2) {
                        ChangeAndRefundRule preRule = changeAndRefundRuleList.get(size - 2);
                        //处于状态变换分界点时preRule = {ChangeAndRefundRule@16951} "ChangeAndRefundRule(desc=起飞前4小时以内, changeFee=292.0, ruleDesc=null, flightFlag=-1, timeConditionStart=0/MI, timeConditionEnd=4/H, leftTime=4, leftTimeUnit=小时, originalLeftTimeUnit=H, rightTime=0, rightTimeUnit=分钟, originalRightTimeUnit=MI)"
                        if (preRule.getFlightFlag() != changeRule.getFlightFlag()) {
                            if (preRule.getTimeConditionStart().equals(changeRule.getTimeConditionStart())) {
                                changeRule.setDesc("起飞前" + preRule.getLeftTime() + preRule.getLeftTimeUnit() + toHoursDesc(preRule.getLeftTime(), preRule.getOriginalLeftTimeUnit()) + "之后");//更改描述
                            }
                            changeAndRefundRuleList.remove(size - 2);
                        }
                    }
                }
            }
            i++;
        }
        return changeAndRefundRuleList;
    }

    /**
     * 几天的描述转换为 小时
     *
     * @param time
     * @param timeUnit
     * @return
     */
    private static String toHoursDesc(String time, String timeUnit) {
        Integer intTime = NumberUtil.stringToInt(time);
        if (intTime != null && intTime > 0 && "D".equals(timeUnit)) {
            return "(" + intTime * 24 + "小时)";
        }
        return "";
    }

    private static void addChangeInfo(List<TicketRule> ptChangeRuleInfoList, Map<String, List<TicketRule>> ptChangeRuleInfoMapTemp) {
        if (CollectionUtils.isNotEmpty(ptChangeRuleInfoMapTemp.get("Y"))) {
            ptChangeRuleInfoList.addAll(ptChangeRuleInfoMapTemp.get("Y"));
        }
        if (CollectionUtils.isNotEmpty(ptChangeRuleInfoMapTemp.get("M"))) {
            ptChangeRuleInfoList.addAll(ptChangeRuleInfoMapTemp.get("M"));
        }
        if (CollectionUtils.isNotEmpty(ptChangeRuleInfoMapTemp.get("D"))) {
            ptChangeRuleInfoList.addAll(ptChangeRuleInfoMapTemp.get("D"));
        }
        if (CollectionUtils.isNotEmpty(ptChangeRuleInfoMapTemp.get("H"))) {
            ptChangeRuleInfoList.addAll(ptChangeRuleInfoMapTemp.get("H"));
        }
        if (CollectionUtils.isNotEmpty(ptChangeRuleInfoMapTemp.get("MI"))) {
            ptChangeRuleInfoList.addAll(ptChangeRuleInfoMapTemp.get("MI"));
        }
    }

    private static void addRefundInfo(List<TicketRule> ptRefundRuleInfoList, Map<String, List<TicketRule>> ptRefundRuleInfoMapTemp) {
        if (CollectionUtils.isNotEmpty(ptRefundRuleInfoMapTemp.get("Y"))) {
            ptRefundRuleInfoList.addAll(ptRefundRuleInfoMapTemp.get("Y"));
        }
        if (CollectionUtils.isNotEmpty(ptRefundRuleInfoMapTemp.get("M"))) {
            ptRefundRuleInfoList.addAll(ptRefundRuleInfoMapTemp.get("M"));
        }
        if (CollectionUtils.isNotEmpty(ptRefundRuleInfoMapTemp.get("D"))) {
            ptRefundRuleInfoList.addAll(ptRefundRuleInfoMapTemp.get("D"));
        }
        if (CollectionUtils.isNotEmpty(ptRefundRuleInfoMapTemp.get("H"))) {
            ptRefundRuleInfoList.addAll(ptRefundRuleInfoMapTemp.get("H"));
        }
        if (CollectionUtils.isNotEmpty(ptRefundRuleInfoMapTemp.get("MI"))) {
            ptRefundRuleInfoList.addAll(ptRefundRuleInfoMapTemp.get("MI"));
        }
    }

    /**
     * 将2/h格式拆分为数组
     *
     * @param timeCondition
     * @return 第一位表示数值  第二位表示单位
     */
    private static String[] splitTimeCondition(String timeCondition) {
        return timeCondition.split("/");
    }

    /**
     * 时间单位转为对应的时间描述
     *
     * @param timeUnit
     * @return
     */
    private static String convertTimeUnitName(String timeUnit) {
        String desc = "";
        switch (timeUnit) {
            case "Y":
                desc = "年";
                break;
            case "M":
                desc = "月";
                break;
            case "D":
                desc = "天";
                break;
            case "H":
                desc = "小时";
                break;
            case "MI":
                desc = "分钟";
                break;
            default:
                break;
        }
        return desc;
    }

    private static void convertFreeCabinRule(TicketRule ptRefundRuleInfo, ChangeAndRefundRule changeAndRefundRule) {
        //如果未使用退票费金额等于0且费率不为0 表示票价是为0的，更改描述文案
        if (ptRefundRuleInfo.getFee() == 0 && StringUtils.isNotBlank(ptRefundRuleInfo.getShowFeeRate()) &&!"0%".equals(ptRefundRuleInfo.getShowFeeRate())
                &&!"0.0%".equals(ptRefundRuleInfo.getShowFeeRate())) {
            changeAndRefundRule.setChangeFee(-1D);
            changeAndRefundRule.setRuleDesc("按规定执行");
        }
    }
}
