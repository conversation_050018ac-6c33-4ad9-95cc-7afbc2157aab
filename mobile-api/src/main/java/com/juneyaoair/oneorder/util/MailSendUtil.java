package com.juneyaoair.oneorder.util;


import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.oneorder.api.email.dto.MailInfo;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.mail.EmailAttachment;
import org.apache.commons.mail.EmailException;
import org.apache.commons.mail.HtmlEmail;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class MailSendUtil {

    private static final String CHARSET = "utf-8";
    private static Logger log = LoggerFactory.getLogger(MailSendUtil.class);

    /**
     * @Description 发送不带附件的电子邮件
     * @Param 主题，HTML内容，收件人
     **/
    public static void sendMail(String subject, String tempHtml, List<String> toAddresses, List<EmailAttachment> attachments,
                                String userName, String password, String EMAIL_HOST_NAME, String EMAIL_SENDER_NAME) {
        MailInfo mailInfo = new MailInfo();
        mailInfo.setSubject(subject);
        mailInfo.setContent(tempHtml);
        mailInfo.setToAddress(toAddresses);
        if (CollectionUtils.isNotEmpty(attachments)) {
            mailInfo.setAttachments(attachments);
        }
        sendMail(mailInfo, EMAIL_HOST_NAME, userName, password, EMAIL_SENDER_NAME);
    }

    /**
     * @Description 发送电子邮件工具方法
     **/
    public static void sendMail(MailInfo mailInfo, String hostName, String userName, String password, String senderName) {
        try {
            HtmlEmail email = new HtmlEmail();
            // 配置信息
            email.setHostName(hostName);
            email.setFrom(userName, senderName);
            if (StringUtils.isNotBlank(password)) {
                email.setAuthentication(userName, password);
            }
            email.setCharset(CHARSET);
            email.setSubject(mailInfo.getSubject());
            email.setHtmlMsg(mailInfo.getContent());
            // 添加附件
            List<EmailAttachment> attachments = mailInfo.getAttachments();
            if (CollectionUtils.isNotEmpty(attachments)) {
                for (int i = 0; i < attachments.size(); i++) {
                    email.attach(attachments.get(i));
                }
            }
            // 收件人
            List<String> toAddress = mailInfo.getToAddress();
            if (CollectionUtils.isNotEmpty(toAddress)) {
                for (int i = 0; i < toAddress.size(); i++) {
                    email.addTo(toAddress.get(i));
                }
            }
            // 抄送人
            List<String> ccAddress = mailInfo.getCcAddress();
            if (CollectionUtils.isNotEmpty(ccAddress)) {
                for (int i = 0; i < ccAddress.size(); i++) {
                    email.addCc(ccAddress.get(i));
                }
            }
            //邮件模板 密送人
            List<String> bccAddress = mailInfo.getBccAddress();
            if (CollectionUtils.isNotEmpty(bccAddress)) {
                for (int i = 0; i < bccAddress.size(); i++) {
                    email.addBcc(ccAddress.get(i));
                }
            }
            email.send();
            log.info("邮件配置:{},目标邮箱:{},邮件发送成功", userName,HoAirGsonUtil.objectToJson(mailInfo.getToAddress()));
        } catch (EmailException e) {
            log.error("邮件配置:{},目标邮箱:{},发送失败,异常信息:", userName, HoAirGsonUtil.objectToJson(mailInfo.getToAddress()), e);
            throw MultiLangServiceException.fail("邮件发送失败！");
        }
    }

}
