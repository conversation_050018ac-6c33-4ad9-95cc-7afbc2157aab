package com.juneyaoair.oneorder.util;

import com.juneyaoair.oneorder.api.crm.dto.CrmContactDto;
import com.juneyaoair.oneorder.common.constant.ContactTypeEnum;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/6 17:33
 */
public class ContactUtil {

    /**
     * @description 过滤指定类型的联系方式
     * <AUTHOR>
     * @date 2024/11/6 17:36
     * @param memberContactSoaModelList
     * @param contactTypeEnum
     * @return MemberContactSoaModel
     **/
    public static CrmContactDto filterMemberContact(List<CrmContactDto> memberContactSoaModelList, ContactTypeEnum contactTypeEnum){
        if(CollectionUtils.isEmpty(memberContactSoaModelList)){
            return null;
        }
        return memberContactSoaModelList.stream().filter(memberContactSoaModel -> memberContactSoaModel.getContactType().equals(contactTypeEnum.name())).findFirst().orElse(null);
    }
}
