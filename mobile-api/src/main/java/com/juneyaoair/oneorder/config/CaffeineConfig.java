package com.juneyaoair.oneorder.config;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/20 10:45
 */
@Configuration
public class CaffeineConfig {
    @Bean(name = "airportCache")
    public Cache<String, Object> airportCache() {
        return Caffeine.newBuilder()
                // 创建或者最近一次被更新之后的一段时间后被认定为过期项
                .expireAfterWrite(5, TimeUnit.MINUTES)
                // 初始的缓存空间大小
                .initialCapacity(10)
                // 缓存的最大条数
                .maximumSize(100)
                .build();
    }

    @Bean(name = "cityCache")
    public Cache<String, Object> cityCache() {
        return Caffeine.newBuilder()
                // 创建或者最近一次被更新之后的一段时间后被认定为过期项
                .expireAfterWrite(5, TimeUnit.MINUTES)
                // 初始的缓存空间大小
                .initialCapacity(10)
                // 缓存的最大条数
                .maximumSize(100)
                .build();
    }
}
