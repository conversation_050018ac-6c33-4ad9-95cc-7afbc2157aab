package com.juneyaoair.oneorder.config;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.juneyaoair.oneorder.common.request.LimitCount;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * @ClassName SmsConfig
 * @Description 短信验证码发送相关配置
 * <AUTHOR>
 * @Date 2023/7/10 9:47
 * @Version 1.0
 */
@Data
@Configuration
@NoArgsConstructor
public class SmsConfig {
    /**
     * 发送短信次数（总发送次数非操作错误次数）
     */
    @ApolloJsonValue("${sendSmsLimit:{\"limitDay\":{\"frequency\":86400,\"accessLimit\":30},\"limitForever\":{\"frequency\":0,\"accessLimit\":60},\"limitMin\":{\"frequency\":180,\"accessLimit\":10}}}")
    private LimitCount sendSmsLimit;

    /**
     * <AUTHOR>
     * @Description 发送短信是否使用同盾
     * @Date 10:08 2023/7/10
     **/
    @Value("${smsUseTongDun:Y}")
    private String smsUseTongDun;


    /**
     * <AUTHOR>
     * @Description 短信验证码编码
     * @Date 14:33 2023/8/23
     **/
    @Value("${smsCode:SMS000047}")
    private String smsCode;

    /**
     * <AUTHOR>
     * @Description 通用短信验证码
     * @Date 14:33 2023/8/23
     **/
    @Value("${smsCommonSendCode:SMS000194}")
    private String smsCommonSendCode;

    /**
     * <AUTHOR>
     * @Description 默认的短信发送模板
     * @Date 14:27 2024/7/24
     **/
    @Value("${defaultTemplateCode:APP00005}")
    private String defaultTemplateCode;

    @Value("${com.juneyaoair.messagePlatform.push:https://hopush.juneyaoair.com}")
    private String messagePlatformPush;
    @Value("${com.juneyaoair.messagePlatform.channelCode:QD000010}")
    private String messagePlatformChannelCode;
    @Value("${com.juneyaoair.messagePlatform.channelSecret:PLCALMCPVWQWIS3J}")
    private String messagePlatformChannelSecret;
    @Value("${com.juneyaoair.messagePlatform.smsAisleNumber:HOYXTYSMS10305}")
    private String messagePlatformSmsAisleNumber;
    @Value("${com.juneyaoair.messagePlatform.appAisleNumber:APPJGTSPUSH01}")
    private String messagePlatformAppAisleNumber;

    @Value("${Juneyao.Sms.Type:Verification}")
    public String SMS_TYPE;
    /**
     * 航延证明发送短信模板
     */
    @Value("${flightChangeSms:尊敬的旅客，请点击链接领取您的航延证明#SHORTURL#， 感谢您的理解和支持，如有疑问请致电吉祥客服95520咨询。}")
    public String flightChangeSms;

    /**
     * <AUTHOR>
     * @Description 基于会员发送短信次数校验
     * @Date 13:20 2024/7/24
     **/
    @ApolloJsonValue("${sendCheckVerifyCodeByFfp:{\"electronicsItinerary\":5,\"setConsumptionPwd\":5,\"resetSalePwd\":5,\"modifySalePwd\":5,\"useSale\":100,\"addBeneficiary\":10}}")
    private Map<String, Integer> sendCheckVerifyCodeByFfp;
}
