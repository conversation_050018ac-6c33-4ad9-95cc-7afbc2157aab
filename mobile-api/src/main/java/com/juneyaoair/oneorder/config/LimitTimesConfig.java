package com.juneyaoair.oneorder.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
public class LimitTimesConfig {
    @Value("${Juneyao.Visit.Cnt.Forever.Av:5000}")
    public int AV_LIMIT_FOREVER_TIMES;
    @Value("${Juneyao.Visit.Cnt.Forever.Sms:10}")
    public int VERYCODE_LIMIT_FOREVER_TIMES;
    @Value("${Juneyao.Visit.Cnt.Forever.comm:5000}")
    public int COMM_LIMIT_FOREVER_TIMES;
    @Value("${Juneyao.Visit.Cnt.Forever.Mobile:10}")
    public int MOBILE_LIMIT_FOREVER_TIMES;
    @Value("${Juneyao.Visit.Cnt.Forever.CheckIn:40}")
    public int CHECKIN_SOURCE_FOREVER_CNT;
    @Value("${Juneyao.Visit.Cnt.Forever.Activity:50}")
    public int ACTIVITY_SOURCE_FOREVER_CNT;
    @Value("${Juneyao.Visit.Cnt.Forever.NameAuth:20}")
    public int NAMEAUTH_MOBILE_SOURCE_FOREVER_CNT;
    @Value("${Juneyao.Visit.Cnt.Forever.NameAuthIP:100}")
    public int NAMEAUTH_IP_SOURCE_FOREVER_CNT;
    @Value("${Juneyao.Visit.Cnt.Forever.GiveCoupon:50}")
    public int GIVECOUPON_SOURCE_FOREVER_CNT;
    @Value("${Juneyao.Visit.Cnt.Forever.CheckIn_Select:40}")
    public int CHECKIN_SELECT_SOURCE_FOREVER_CNT;
    @Value("${Juneyao.Visit.Cnt.Forever.CheckIn_Select_Not_Login_Cnt:30}")
    public int CHECKIN_SELECT_SOURCE_FOREVER_NOT_LOGIN_CNT;
    @Value("${Juneyao.Visit.Cnt.Forever.Ticket_Verify:30}")
    public int TICKET_VERIFY_SOURCE_FOREVER_CNT;
    @Value("${Juneyao.Visit.Cnt.TicketVerify:10}")
    public int TICKET_VERIFY_SOURCE_CNT;
    @Value("${Juneyao.Visit.Cnt.Forever.PhotoAuthIP:50}")
    public int PHOTOAUTH_IP_SOURCE_FOREVER_CNT;
    @Value("${Juneyao.Visit.Cnt.Forever.PhotoAuthDevice:50}")
    public int PHOTOAUTH_DEVICE_FOREVER_CNT;
    @Value("${Juneyao.Visit.Cnt.Forever.PhotoAuth:60}")
    public int PHOTOAUTH_MOBILE_SOURCE_FOREVER_CNT;
    @Value("${Juneyao.Visit.Cnt.Forever.Sms.Common:15}")
    public int SMS_COMMON_SOURCE_FOREVER_CNT;


    @Value("${Juneyao.Visit.Cnt.Av:1000}")
    public int AV_LIMIT_TIMES;
    @Value("${Juneyao.Visit.Cnt.Reg:10}")
    public int REG_LIMIT_TIMES;
    @Value("${Juneyao.Visit.Cnt.Email:10}")
    public int EMAIL_VERYCODE_LIMIT_TIMES;
    @Value("${Juneyao.Visit.Cnt.Sms:5}")
    public int VERYCODE_LIMIT_TIMES;
    @Value("${Juneyao.Visit.Cnt.comm:1000}")
    public int COMM_LIMIT_TIMES;
    @Value("${Juneyao.Visit.Cnt.LoginErr:5}")
    public int LOGINERR_LIMIT_SOURCE;
    @Value("${Juneyao.Visit.Cnt.LoginCode_IP:100}")
    public int LOGINCODE_IP_LIMIT_SOURCE;
    @Value("${Juneyao.Visit.Cnt.LoginCode_MOBILE:10}")
    public int LOGINCODE_MOBILE_LIMIT_SOURCE;
    @Value("${Juneyao.Visit.Cnt.Mobile:5}")
    public int MOBILE_LIMIT_TIMES;
    @Value("${Juneyao.Visit.Cnt.Consume:5}")
    public int CONSUME_LIMIT_TIMES;
    @Value("${Juneyao.Visit.Cnt.ForgetPwd:5}")
    public int FORGETPWD_LIMIT_TIMES;
    @Value("${Juneyao.Visit.Cnt.UpdatePwd:5}")
    public int UPDATEPWD_LIMIT_TIMES;
    @Value("${Juneyao.Visit.Cnt.Total:10}")
    public int TOTAL_LIMIT_TIMES;
    @Value("${Juneyao.Visit.Cnt.CheckIn:20}")
    public int CHECKIN_SOURCE_CNT;
    @Value("${Juneyao.Visit.Cnt.Activity:20}")
    public int ACTIVITY_SOURCE_CNT;
    @Value("${Juneyao.Visit.Cnt.Login:50}")
    public int LOGIN_LIMIT_TIMES;
    @Value("${Juneyao.Visit.Cnt.NameAuth:10}")
    public int NAMEAUTH_MOBILE_SOURCE_CNT;
    @Value("${Juneyao.Visit.Cnt.NameAuthIP:8}")
    public int NAMEAUTH_IP_SOURCE_CNT;
    @Value("${Juneyao.Visit.Cnt.NameAuthErr:5}")
    public int NAMEAUTH_MOBILEERR_SOURCE_CNT;
    @Value("${Juneyao.Visit.Cnt.GiveCoupon:15}")
    public int GIVECOUPON_SOURCE_CNT;
    @Value("${Juneyao.Visit.Cnt.CheckIn_Select:20}")
    public int CHECKIN_SELECT_SOURCE_CNT;
    @Value("${Juneyao.Visit.Cnt.CheckIn_Select:20}")
    public int CHECKIN_SELECT_SOURCE_NOT_LOGIN_CNT;
    @Value("${Juneyao.Visit.Cnt.PhotoAuthIP:6}")
    public int PHOTOAUTH_IP_SOURCE_CNT;
    @Value("${Juneyao.Visit.Cnt.PhotoAuthDevice:6}")
    public int PHOTOAUTH_DEVICE_SOURCE_CNT;
    @Value("${Juneyao.Visit.Cnt.PhotoAuth:10}")
    public int PHOTOAUTH_MOBILE_SOURCE_CNT;
    @Value("${Juneyao.Visit.Cnt.Goods_Lost:3}")
    public int GOODS_LOST_SOURCE_CNT;
    @Value("${Juneyao.Visit.Cnt.Sms.Common:6}")
    public int SMS_COMMON_SOURCE_CNT;


    public static final String GOODS_LOST_SOURCE = "GoodsLostVerify";

    public static final String AV_SOURCE = "av";


    public static final String VERYCODE_SOURCE = "verycode";

    public static final String MOBILE_SOURCE = "mb";

    public static final String COMM_SOURCE = "comm";

    public static final String CHECKIN_SOURCE = "CheckIn";

    public static final String ACTIVITY_SOURCE = "Activity";

    public static final String NAMEAUTH_MOBILE_SOURCE = "nameauth";

    public static final String NAMEAUTH_IP_SOURCE = "nameauthip";

    public static final String GIVECOUPON_SOURCE = "giveCoupon";

    public static final String TICKET_VERIFY_SOURCE = "TicketVerify";

    public static final String CHECKIN_SELECT_SOURCE_NOT_LOGIN = "CheckInSeatNotLogin";
    public static final String PHOTOAUTH_IP_SOURCE = "photoauthip";
    public static final String PHOTOAUTH_DEVICE_SOURCE = "photoauthdevice";
    public static final String PHOTOAUTH_MOBILE_SOURCE = "photoauth";
    public static final String SMS_COMMON_SOURCE = "sms_common";
    //每日登录出错次数
    public static String LOGINERR_SOURCE = "loginErr";//操作源
    //IP快捷登录验证码获取次数
    public static String LOGINCODE_IP_SOURCE = "logincodeip";//操作源
    //账号快捷登录验证码获取次数
    public static String LOGINCODE_MOBILE_SOURCE = "logincodemobile";//操作源

    //每日登录频次调用
    public static String LOGIN_SOURCE = "LOGIN";

    public static String NAMEAUTH_MOBILEERR_SOURCE = "nameautherr";

    public static String CHECKIN_SELECT_SOURCE = "CheckInSeat";

}
