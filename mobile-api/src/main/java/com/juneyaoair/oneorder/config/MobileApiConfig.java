package com.juneyaoair.oneorder.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
public class MobileApiConfig {

    //内部IP正则表达式
    @Value("${IpPattern:((^(172)\\\\.(19|20)\\\\.(\\\\d|[1-9]\\\\d|1\\\\d{2}|2[0-4]\\\\d||25[0-5])\\\\.(\\\\d|[1-9]\\\\d|1\\\\d{2}|2[0-4]\\\\d||25[0-5]))||127.0.0.1)}")
    public String IP_PATTERN;

    /**
     * 是否开启ip限制
     */
    @Value("${foreverLimitSwitch: Y}")
    private String foreverLimitSwitch;
}
