package com.juneyaoair.oneorder.config;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Configuration
@Data
public class TongDunConfig {
    /**
     * 合作方标识
     */
    @Value("${tongdun_partner_code:jxhk}")
    public String TONGDUN_PARTNER_CODE;
    /**
     * 安卓端
     */
    @Value("${android_secret_key:8574f22aac7048ceb0ceb22853fa2128}")
    public String TONGDUN_ANDROID_SECRET_KEY;
    /**
     * ios端
     */
    @Value("${ios_secret_key:df2484a20f79408fab0a10b9d66f9b4b}")
    public String TONGDUN_IOS_SECRET_KEY;
    /**
     * 鸿蒙端
     */
    @Value("${harmony_secret_key:f75e85fe7e354c3ea8cdc1f6aa7d67b9}")
    public String TONGDUN_HARMONY_SECRET_KEY;
    /**
     * 微信小程序端
     */
    @Value("${xcx_secret_key:55afbe078196461c8f1aadc75d377987}")
    public String TONGDUN_XCX_SECRET_KEY;
    /**
     * H5端
     */
    @Value("${web_secret_key:f8a137abc05440c1b3aa7ca34d5885a3}")
    public String TONGDUN_WEB_SECRET_KEY;
    /**
     * 中文官网
     */
    @Value("${b2c_secret_key:b08926306f304850bd90cd30816966ab}")
    public String TONGDUN_B2C_SECRET_KEY;
    /**
     * 国际官网
     */
    @Value("${global_secret_key:41f4a46fc8ee439f9c6a7e8bf1cdd67f}")
    public String TONGDUN_GLOBAL_SECRET_KEY;
    /**
     * 支付宝小程序
     */
    @Value("${zfb_secret_key:3c3ea7666e9e4c378d8da19b4aa60830}")
    public String TONGDUN_ZFB_SECRET_KEY;

    //以下为安卓的事件标识ID
    @Value("${tongdun_android_register_event_id:Register_android_20190116}")
    public String TONGDUN_ANDROID_REGISTER_EVENT_ID;
    @Value("${tongdun_android_login_event_id:Login_android_20190116}")
    public String TONGDUN_ANDROID_LOGIN_EVENT_ID;
    @Value("${tongdun_android_trade_event_id:Trade_android_20190116}")
    public String TONGDUN_ANDROID_TRADE_EVENT_ID;
    @Value("${tongdun_android_checkin_lookup_event_id:Checkin_android_20200817}")
    public String TONGDUN_ANDROID_CHECKIN_LOOKUP_EVENT_ID;
    @Value("${tongdun_android_lookup_event_id:lookup_android_20191211}")
    public String TONGDUN_ANDROID_LOOKUP_EVENT_ID;
    @Value("${tongdun_android_marketing_event_id:Marketing_android_20200331}")
    public String TONGDUN_ANDROID_MARKETING_EVENT_ID;
    @Value("${tongdun_android_use_score_event_id:Click_android_20231120}")
    public String TONGDUN_ANDROID_USE_SCORE_EVENT_ID;
    @Value("${tongdun_android_reserve_event_id:Reserve_android_20200707}")
    public String TONGDUN_ANDROID_RESERVE_EVENT_ID;
    @Value("${tongdun_android_payment_event_id:Payment_android_20200707}")
    public String TONGDUN_ANDROID_PAYMENT_EVENT_ID;
    @Value("${tongdun_android_sms_event_id:SMS_android_20200929}")
    public String TONGDUN_ANDROID_SMS_EVENT_ID;
    @Value("${tongdun_android_modify_salepwd_event_id:Modify_android_20231120}")
    public String TONGDUN_ANDROID_MODIFY_SALEPWD_EVENT_ID;

    //以下为IOS的事件ID
    @Value("${tongdun_ios_register_event_id:Register_ios_20190116}")
    public String TONGDUN_IOS_REGISTER_EVENT_ID;
    @Value("${tongdun_ios_login_event_id:Login_ios_20190116}")
    public String TONGDUN_IOS_LOGIN_EVENT_ID;
    @Value("${tongdun_ios_trade_event_id:Trade_ios_20190116}")
    public String TONGDUN_IOS_TRADE_EVENT_ID;
    @Value("${tongdun_ios_checkin_lookup_event_id:Checkin_ios_20200817}")
    public String TONGDUN_IOS_CHECKIN_LOOKUP_EVENT_ID;
    @Value("${tongdun_ios_lookup_event_id:lookup_ios_20191211}")
    public String TONGDUN_IOS_LOOKUP_EVENT_ID;
    @Value("${tongdun_ios_marketing_event_id:Marketing_ios_20200331}")
    public String TONGDUN_IOS_MARKETING_EVENT_ID;
    @Value("${tongdun_ios_use_score_event_id:Click_ios_20231120}")
    public String TONGDUN_IOS_USE_SCORE_EVENT_ID;
    @Value("${tongdun_ios_reserve_event_id:Reserve_IOS_20200707}")
    public String TONGDUN_IOS_RESERVE_EVENT_ID;
    @Value("${tongdun_ios_payment_event_id:Payment_IOS_20200707}")
    public String TONGDUN_IOS_PAYMENT_EVENT_ID;
    @Value("${tongdun_ios_sms_event_id:SMS_ios_20200929}")
    public String TONGDUN_IOS_SMS_EVENT_ID;
    @Value("${tongdun_ios_modify_salepwd_event_id:Modify_ios_20231120}")
    public String TONGDUN_IOS_MODIFY_SALEPWD_EVENT_ID;

    //以下为微信小程序的事件ID
    @Value("${tongdun_xcx_login_event_id:Login_web_20191029}")
    public String TONGDUN_XCX_LOGIN_EVENT_ID;
    @Value("${tongdun_xcx_trade_event_id:Trade_web_20191029}")
    public String TONGDUN_XCX_TRADE_EVENT_ID;
    @Value("${tongdun_xcx_checkin_lookup_event_id:Checkin_xcx_20200817}")
    public String TONGDUN_XCX_CHECKIN_LOOKUP_EVENT_ID;
    @Value("${tongdun_xcx_lookup_event_id:lookup_web_20191216}")
    public String TONGDUN_XCX_LOOKUP_EVENT_ID;
    @Value("${tongdun_xcx_marketing_event_id:Marketing_web_20200331}")
    public String TONGDUN_XCX_MARKETING_EVENT_ID;
    @Value("${tongdun_xcx_use_score_event_id:Click_xcx_20231202}")
    public String TONGDUN_XCX_USE_SCORE_EVENT_ID;
    @Value("${tongdun_xcx_reserve_event_id:Reserve_xcx_20200707}")
    public String TONGDUN_XCX_RESERVE_EVENT_ID;
    @Value("${tongdun_xcx_payment_event_id:Payment_xcx_20200707}")
    public String TONGDUN_XCX_PAYMENT_EVENT_ID;
    @Value("${tongdun_xcx_sms_event_id:SMS_web_20200929}")
    public String TONGDUN_XCX_SMS_EVENT_ID;
    @Value("${tongdun_xcx_modify_salepwd_event_id:Modify_xcx_20231202}")
    public String TONGDUN_XCX_MODIFY_SALEPWD_EVENT_ID;

    //以下为H5端的事件ID
    @Value("${tongdun_web_register_event_id:Register_web_20190425}")
    public String TONGDUN_WEB_REGISTER_EVENT_ID;
    @Value("${tongdun_web_login_event_id:Login_web_20190425}")
    public String TONGDUN_WEB_LOGIN_EVENT_ID;
    @Value("${tongdun_web_trade_event_id:Trade_web_20190425}")
    public String TONGDUN_WEB_TRADE_EVENT_ID;
    @Value("${tongdun_h5_checkin_lookup_event_id:Checkin_h5_20200817}")
    public String TONGDUN_H5_CHECKIN_LOOKUP_EVENT_ID;
    @Value("${tongdun_h5_lookup_event_id:Lookup_h5_20191206}")
    public String TONGDUN_H5_LOOKUP_EVENT_ID;
    @Value("${tongdun_h5_marketing_event_id:Marketing_web_20200331}")
    public String TONGDUN_H5_MARKETING_EVENT_ID;
    @Value("${tongdun_h5_reserve_event_id:Reserve_H5_20200707}")
    public String TONGDUN_H5_RESERVE_EVENT_ID;
    @Value("${tongdun_h5_payment_event_id:Payment_H5_20200707}")
    public String TONGDUN_H5_PAYMENT_EVENT_ID;
    @Value("${tongdun_h5_sms_event_id:SMS_web_20200929}")
    public String TONGDUN_H5_SMS_EVENT_ID;
    @Value("${tongdun_h5_modify_salepwd_event_id:Modify_h5_20231202}")
    public String TONGDUN_H5_MODIFY_SALEPWD_EVENT_ID;
    @Value("${tongdun_h5_use_score_event_id:Click_h5_20231202}")
    public String TONGDUN_H5_USE_SCORE_EVENT_ID;

    //以下为官网B2C的事件ID
    @Value("${tongdun_b2c_modify_event_id:Modify_web_20230927}")
    public String TONGDUN_B2C_MODIFY_EVENT_ID;
    //客票验证事件
    @Value("${tongdun_b2c_verification_event_id:Verification_web_20210422}")
    public String TONGDUN_B2C_VERIFICATION_EVENT_ID;
//    交易事件
    @Value("${tongdun_b2c_trade_event_id:Trade_web_20190329}")
    public String TONGDUN_B2C_TRADE_EVENT_ID;
//    短信事件
    @Value("${tongdun_b2c_sms_event_id:SMS_web_20240810}")
    public String TONGDUN_B2C_SMS_EVENT_ID;
//    注册事件
    @Value("${tongdun_b2c_register_event_id:register_professional_web}")
    public String TONGDUN_B2C_REGISTER_EVENT_ID;
//    支付事件
    @Value("${tongdun_b2c_payment_event_id:Payment_web_20211018}")
    public String TONGDUN_B2C_PAYMENT_EVENT_ID;
//    查询事件
    @Value("${tongdun_b2c_lookup_event_id:lookup_web_20191129}")
    public String TONGDUN_B2C_LOOKUP_EVENT_ID;
//    登录事件
    @Value("${tongdun_b2c_login_event_id:login_professional_web}")
    public String TONGDUN_B2C_LOGIN_EVENT_ID;
//    邀请事件
    @Value("${tongdun_b2c_invite_event_id:Invite_web_20201028}")
    public String TONGDUN_B2C_INVITE_EVENT_ID;
//    值机事件
    @Value("${tongdun_b2c_invite_event_id:Checkin_web_20200817_1}")
    public String TONGDUN_B2C_CHECKIN_EVENT_ID;
//    下单购券事件
    @Value("${tongdun_b2c_order_coupon_event_id:Click_web_20240820}")
    public String TONGDUN_B2C_ORDER_COUPON_EVENT_ID;


    //以下为支付宝小程序端事件标识ID
    @Value("${tongdun_zfb_sms_event_id:SMS_zfb_20240821}")
    public String TONGDUN_ZFB_SMS_EVENT_ID;
    @Value("${tongdun_b2c_invite_event_id:lookup_zfb_20240820}")
    public String TONGDUN_ZFB_CHECKIN_LOOKUP_EVENT_ID;
    @Value("${tongdun_zfb_modify_salepwd_event_id:Modify_zfb_20231202}")
    public String TONGDUN_ZFB_MODIFY_SALEPWD_EVENT_ID;
    @Value("${tongdun_zfb_use_score_event_id:Click_zfb_20231202}")
    public String TONGDUN_ZFB_USE_SCORE_EVENT_ID;

    //以下为鸿蒙端事件标识ID
    @Value("${tongdun_harmony_register_event_id:Register_hm_20241011}")
    public String TONGDUN_HARMONY_REGISTER_EVENT_ID;
    @Value("${tongdun_harmony_login_event_id:Login_hm_20241011}")
    public String TONGDUN_HARMONY_LOGIN_EVENT_ID;
    @Value("${tongdun_harmony_trade_event_id:Trade_hm_20241011}")
    public String TONGDUN_HARMONY_TRADE_EVENT_ID;
    @Value("${tongdun_harmony_checkin_lookup_event_id:lookup_hm_20241011zj}")
    public String TONGDUN_HARMONY_CHECKIN_LOOKUP_EVENT_ID;
    @Value("${tongdun_harmony_lookup_event_id:lookup_hm_20241011}")
    public String TONGDUN_HARMONY_LOOKUP_EVENT_ID;
    @Value("${tongdun_harmony_marketing_event_id:Marketing_hm_20241011}")
    public String TONGDUN_HARMONY_MARKETING_EVENT_ID;
    @Value("${tongdun_harmony_use_score_event_id:Click_hm_20241011}")
    public String TONGDUN_HARMONY_USE_SCORE_EVENT_ID;
    @Value("${tongdun_harmony_reserve_event_id:Booking_hm_20241011}")
    public String TONGDUN_HARMONY_RESERVE_EVENT_ID;
    @Value("${tongdun_harmony_payment_event_id:Payment_hm_20241011}")
    public String TONGDUN_HARMONY_PAYMENT_EVENT_ID;
    @Value("${tongdun_harmony_sms_event_id:SMS_hm_20241011}")
    public String TONGDUN_HARMONY_SMS_EVENT_ID;
    @Value("${tongdun_harmony_modify_salepwd_event_id:Modify_hm_20241011}")
    public String TONGDUN_HARMONY_MODIFY_SALEPWD_EVENT_ID;

    //b2c官网同盾事件
    @ApolloJsonValue("${b2c_event_id:{}}")
    private Map<String, String> b2cEventIdMap;

    //以下为国际网站同盾事件
    @ApolloJsonValue("${global_event_id:{}}")
    private Map<String, String> globalEventIdMap;

    @Value("${oneorder.tongDunFinalScoreLimit:1}")
    public Integer tongDunFinalScoreLimit;

}
