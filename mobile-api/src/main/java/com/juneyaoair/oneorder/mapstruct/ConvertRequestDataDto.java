package com.juneyaoair.oneorder.mapstruct;

import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/19 15:50
 */
@Mapper
public interface ConvertRequestDataDto {
    ConvertRequestDataDto MAPPER = Mappers.getMapper(ConvertRequestDataDto.class);
    RequestData toRequestData(RequestDataDto requestDataDto);
}
