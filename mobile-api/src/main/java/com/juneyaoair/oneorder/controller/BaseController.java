package com.juneyaoair.oneorder.controller;

import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.constant.SecurityConstants;
import com.juneyaoair.mobile.exception.ArgumentCheckFailException;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.security.utils.ServletUtils;
import com.juneyaoair.oneorder.tools.utils.HoAirUuidUtil;
import com.juneyaoair.mobile.exception.util.HoAirIpUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.BindingResult;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @description 基础公用controller
 * @date 2023/7/14 13:17
 */
public class BaseController {

    public void checkObjNotNUll(Object o){
        if (o == null) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
    }
    /**
     * 基础参数检验
     *
     * @param bindingResult
     */
    public void checkBaseParam(BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ArgumentCheckFailException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
    }

    /**
     * 参数检验
     *
     * @param requestData
     * @param bindingResult
     * @param <T>
     */
    public <T> void checkParam(RequestDataDto<T> requestData, BindingResult bindingResult) {
        if (requestData.getData() == null) {
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        if (bindingResult.hasErrors()) {
            throw new ArgumentCheckFailException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
    }

    /**
     * 基础请求头参数初始化
     *
     * @param dto
     * @param request
     */
    public void initBizDto(BizDto dto, HttpServletRequest request) {
        String channelCode = ServletUtils.getChannelCode(request);
        String versionCode = ServletUtils.getHeader(request, SecurityConstants.HEAD_VERSION_CODE);
        String clientVersion = ServletUtils.getHeader(request, SecurityConstants.HEAD_CLIENT_VERSION);
        String platformInfo = ServletUtils.getHeader(request, SecurityConstants.PLATFORM_INFO);
        String blackboxFrom = ServletUtils.getHeader(request, SecurityConstants.BLACKBOX_FROM);
        dto.setHeadChannelCode(channelCode);
        dto.setHeadVersionCode(versionCode);
        dto.setHeadClientVersion(clientVersion);
        dto.setPlatformInfo(platformInfo);
        dto.setFrom(null == blackboxFrom ? "" : blackboxFrom);
        dto.setIp(HoAirIpUtil.getIpAddr(request));
        if (StringUtils.isBlank(dto.getBizSeq())) {
            dto.setBizSeq(HoAirUuidUtil.randomUUID8());
        }
    }

    /**
     * 基础参数初始化
     *
     * @param request
     */
    public BizDto initBizDto(HttpServletRequest request) {
        BizDto bizDto = new BizDto();
        initBizDto(bizDto, request);
        return bizDto;
    }
}
