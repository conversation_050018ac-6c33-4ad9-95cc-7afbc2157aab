package com.juneyaoair.oneorder.common.request;

import lombok.Builder;
import lombok.Data;

import javax.servlet.http.HttpServletRequest;

@Data
public class HORequestBuilder<T> {

    public T request;
    public HttpServletRequest httpServletRequest;

    HORequestBuilder(Builder<T> builder) {
        this.request = builder.request;
        this.httpServletRequest = builder.httpServletRequest;
    }

    public static class Builder<T> {
        private T request;
        private HttpServletRequest httpServletRequest;

        public Builder<T> addRequest(T t) {

            this.request = t;
            return this;
        }

        public Builder<T> addHttpServletRequest(HttpServletRequest httpServletRequest) {
            this.httpServletRequest = httpServletRequest;
            return this;
        }

        public HORequestBuilder<T> build() {
            return new HORequestBuilder<>(this);
        }
    }
}
