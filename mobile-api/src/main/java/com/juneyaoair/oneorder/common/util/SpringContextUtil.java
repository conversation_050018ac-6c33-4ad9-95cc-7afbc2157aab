package com.juneyaoair.oneorder.common.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;


@Component
public class SpringContextUtil implements ApplicationContextAware {

	//Spring应用上下文环境
	private static ApplicationContext applicationContext;
	/**
	 * @描述:  实现ApplicationContextAware接口的回调方法，设置上下文环境
	 * @方法名: setApplicationContext
	 */
	@Override
	public void setApplicationContext(ApplicationContext applicationContext)
			throws BeansException {
		SpringContextUtil.applicationContext = applicationContext;
	}


	public static ApplicationContext getApplicationContext() {
	    return applicationContext;
	}
	/**
	 *
	 * @描述: 获取对象
	 */
	public static Object getBean(String name) throws BeansException {
	    return applicationContext.getBean(name);
	}

	/**
	 *
	 * @描述: 获取对象
	 */
	public static <T> T getBean(Class<T> clazz) throws BeansException {
		return applicationContext.getBean(clazz);
	}
	/**
	 *
	 * @描述: 获取类型为requiredType的对象
	 *       如果bean不能被类型转换，相应的异常将会被抛出
	 */
	public static <T> T getBean(String name, Class<T> requiredType) throws BeansException {
	    return applicationContext.getBean(name, requiredType);
	}
}
