package com.juneyaoair.oneorder.common.util;


import com.google.gson.Gson;
import com.juneyaoair.flightbasic.antifraud.FinalDecisionEnum;
import com.juneyaoair.flightbasic.antifraud.request.Antifraud;
import com.juneyaoair.flightbasic.antifraud.response.AntiResult;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResponseDTO;
import com.juneyaoair.flightbasic.feign.FlightBasicConsumerClient;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.common.constant.TongDunEventType;
import com.juneyaoair.oneorder.common.dto.enums.SensitiveOperationEnum;
import com.juneyaoair.oneorder.config.TongDunConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;


@Slf4j
@Service
public class FraudApiInvoker {

    @Resource
    TongDunConfig tongDunConfig;
    @Resource
    FlightBasicConsumerClient flightBasicConsumerClient;
    @Resource
    private CommonService commonService;

    private static final String B2CWEB_ORDER_EVENT_HIGH_RISK = "您的账户处于高风险状态，无法继续使用该功能，有疑问请联系客服95520";


    /**
     * 发送验证码同盾
     * scene 注册=1；登录=2；修改信息=3
     *
     * @return
     */
    public void sendSMSRiskControl(String headChannelCode, String platForm, String userAgent, String areaCode, String mobile, String ip, String scene, String blackBox, String ffpCardNo, String ffpId) {
        Map<String, String> params = TongDunUtil.createCommonParam(headChannelCode, platForm, "", TongDunEventType.SMS.getEventType(), SensitiveOperationEnum.SEND_SMS_RISK_CTRL.name(), tongDunConfig);
        if (params == null) {//说明没有对应的事件ID
            throw new ServiceException("安全校验出错");
        }
        //设备指纹信息
        if (StringUtils.isNotEmpty(blackBox)) {
            params.put("black_box", blackBox);
        }
        params.put("user_agent", userAgent);
        params.put("ip_address", ip);
        params.put("mobile", mobile);
        params.put("account_mobile", mobile);
        params.put("ext_sms_scene", scene);
        params.put("area_code", areaCode);
        if (StringUtils.isNotEmpty(ffpCardNo)) {
            params.put("account_login", ffpCardNo);
        }
        //业务参数
        Antifraud antifraud = new Antifraud();
        antifraud.setIp(ip);
        antifraud.setFfpCardNo(ffpCardNo);
        antifraud.setFfpId(ffpId);
        antifraud.setParamMap(params);
        BaseRequestDTO<Antifraud> antifraudBaseRequestDTO = commonService.createBaseRequestDTO(ip, headChannelCode, antifraud);
        antifraudBaseRequestDTO.setFfpCardNo(ffpCardNo);
        antifraudBaseRequestDTO.setFfpId(ffpId);
        BaseResponseDTO<AntiResult> antiFraudResult = flightBasicConsumerClient.antifraud(antifraudBaseRequestDTO);
        if (antiFraudResult == null || !"10001".equals(antiFraudResult.getResultCode())) {
            throw new ServiceException("您的账户存在风险，无法继续操作，如有疑问请咨询官方客服");
        }
        if (null != antiFraudResult.getObjData() && FinalDecisionEnum.REJECT.getCode().equalsIgnoreCase(antiFraudResult.getObjData().getFinal_decision())) {
            throw new ServiceException("您的账户存在风险，无法继续操作，如有疑问请咨询官方客服");
        }
    }


    /**
     * 网页下单购券事件
     *
     * @param headChannelCode
     * @param platForm
     * @param mobile
     * @param ip
     * @param blackBox
     * @param ffpCardNo
     * @param ffpId
     */
    public void b2cWebOrderEvent(String headChannelCode, String platForm, String email, String mobile, String ip, String blackBox, String ffpCardNo, String ffpId) {
        Map<String, String> params = TongDunUtil.createCommonParam(headChannelCode, platForm, "", TongDunEventType.TRADE.getEventType(), SensitiveOperationEnum.B2C_WEB_ORDER_EVENT.name(), tongDunConfig);


        if (params == null) {//说明没有对应的事件ID
            throw new ServiceException("安全校验出错");
        }

        //业务参数
        //设备指纹信息
        if (StringUtils.isNotEmpty(blackBox)) {
            params.put("black_box", blackBox);
        }
        // 账户ID,业务平台分配给用户的唯一标识（会员卡号）
        if (StringUtils.isNotEmpty(ffpCardNo)) {
            params.put("account_login", ffpCardNo);
        }
        // 邮箱
        params.put("account_email", email);
        // 手机号
        params.put("account_mobile", mobile);
        // 用户侧IP地址
        params.put("ip_address", ip);
        // 具体场景 如有区分具体下单的场景，则传入，无则不传
        // params.put("sceneTypeDetail",areaCode);


        Antifraud antifraud = new Antifraud();
        antifraud.setIp(ip);
        antifraud.setFfpCardNo(ffpCardNo);
        antifraud.setFfpId(ffpId);
        antifraud.setParamMap(params);
        BaseRequestDTO<Antifraud> antifraudBaseRequestDTO = commonService.createBaseRequestDTO(ip, headChannelCode, antifraud);
        antifraudBaseRequestDTO.setFfpCardNo(ffpCardNo);
        antifraudBaseRequestDTO.setFfpId(ffpId);
        //
        log.info("tongdun-b2cWebOrderEvent:request:{}", new Gson().toJson(antifraudBaseRequestDTO));
        //
        BaseResponseDTO<AntiResult> antiFraudResult = flightBasicConsumerClient.antifraud(antifraudBaseRequestDTO);
        //
        log.info("tongdun-b2cWebOrderEvent:response:{}", new Gson().toJson(antiFraudResult));
        //
        if (antiFraudResult == null || !"10001".equals(antiFraudResult.getResultCode())) {
            throw ServiceException.fail(B2CWEB_ORDER_EVENT_HIGH_RISK);
        }
        if (null != antiFraudResult.getObjData() && FinalDecisionEnum.REJECT.getCode().equalsIgnoreCase(antiFraudResult.getObjData().getFinal_decision())) {
            throw ServiceException.fail(B2CWEB_ORDER_EVENT_HIGH_RISK);
        }
    }

}
