package com.juneyaoair.oneorder.common.service.impl;

import com.google.common.collect.Maps;
import com.juneyaoair.flightbasic.utils.IpUtils;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.mobile.exception.util.HoAirIpUtil;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.service.ICaptchaService;
import com.juneyaoair.oneorder.api.crm.service.IMemberService;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.DigestmodEnum;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.SceneEnum;
import com.juneyaoair.oneorder.api.geetest.service.IGeetestService;
import com.juneyaoair.oneorder.common.ServiceContext;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.GeetestDto;
import com.juneyaoair.oneorder.common.service.SmsCodeSendService;
import com.juneyaoair.oneorder.config.SmsConfig;
import com.juneyaoair.oneorder.crm.dto.common.SourceType;
import com.juneyaoair.oneorder.crm.dto.request.VerifyCodeCheckRequest;
import com.juneyaoair.oneorder.crm.dto.request.VerifyCodeSendRequest;
import com.juneyaoair.oneorder.mobile.config.RedisConstantConfig;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import com.juneyaoair.oneorder.tools.utils.dto.CrmPhoneInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName SmsCodeSendServiceImpl
 * @Description 短信验证码发送-目前仅用于电子行程单的短信验证码发送
 * <AUTHOR>
 * @Date 2024/7/23 14:49
 * @Version 1.0
 */

@Slf4j
@Service
public class SmsCodeSendServiceImpl extends CommonService implements SmsCodeSendService {

    @Resource
    private SmsConfig smsConfig;
    @Autowired
    private RedisConstantConfig redisConstantConfig;
    @Autowired
    private RedisUtil redisUtils;

    @Resource
    private IGeetestService geetestService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private ICaptchaService captchaService;

    @Override
    public void sendVerifyCodeByFfp(String ffpCardNo, GeetestDto geetestDto, String channelCode, HttpServletRequest request) {
        //极验校验
        if (SceneEnum.getEnumByCode(
                geetestDto.getScene()) != SceneEnum.SMS) {
            ServiceException.fail("验证失败");
        }
        DigestmodEnum digestmodEnum = DigestmodEnum.MD5;
        HashMap<String, String> param = new HashMap<>();
        param.put("digestmod", digestmodEnum.getName());
        param.put("user_id", HoAirIpUtil.getIpAddr(request)); //网站用户id
        param.put("client_type", geetestDto.getClient_type()); //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
        param.put("ip_address", ServiceContext.getHead().clientIp); //传输用户请求验证时所携带的IP
        geetestService.validate(geetestDto.getScene(), geetestDto, param);

        //获取手机号
        CrmPhoneInfo catchMobileInfo = memberService.toCatchMobileInfo(ffpCardNo, channelCode);
        //次数校验
        sendCheckVerifyCodeByFfp(SourceType.EC_ITINERARY.value, catchMobileInfo.getCrmPhone());
        String sendCode = this.getChkCode();
        String redisKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.SMS_REDIS + catchMobileInfo.getPhone() + ":" + SourceType.EC_ITINERARY.value);
        redisUtil.set(redisKey, sendCode, 300);
        Map<String, String> extras = Maps.newHashMap();
        extras.put("sendCode", sendCode);
        extras.put("function", SourceType.EC_ITINERARY.getFunction());
        captchaService.commonSmsSend(SourceType.EC_ITINERARY.getTemplateCode(), catchMobileInfo.getAreaId(), catchMobileInfo.getPhone(), extras);
    }

    @Override
    public void sendVerifyCodeByCardNo(String ffpCardNo, VerifyCodeSendRequest verifyCodeSendRequest, String channelCode, HttpServletRequest request) {
        SourceType sourceType = SourceType.toCatchSourceType(verifyCodeSendRequest.getType());
        if (null == sourceType) {
            throw ServiceException.fail("短信类型非法");
        }
        //极验
        DigestmodEnum digestmodEnum = DigestmodEnum.MD5;
        HashMap<String, String> param = new HashMap<>();
        param.put("digestmod", digestmodEnum.getName());
        //网站用户id
        param.put("user_id", IpUtils.getIpAddr(request));
        //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
        param.put("client_type", verifyCodeSendRequest.getClient_type());
        //传输用户请求验证时所携带的IP
        param.put("ip_address", ServiceContext.getHead().clientIp);
        geetestService.validate(verifyCodeSendRequest.getScene(), verifyCodeSendRequest, param);
        //获取手机号
        CrmPhoneInfo catchMobileInfo = memberService.toCatchMobileInfo(ffpCardNo, channelCode);
        //校验短信发送次数
        sendCheckVerifyCodeByFfp(verifyCodeSendRequest.getType(), catchMobileInfo.getCrmPhone());
        //实际发送操作
        String sendCode = this.getChkCode();
        String redisKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.SMS_REDIS + catchMobileInfo.getPhone() + ":" + sourceType.getValue());
        redisUtil.set(redisKey, sendCode, 300);
        Map<String, String> extras = Maps.newHashMap();
        extras.put("sendCode", sendCode);
        extras.put("function", sourceType.getFunction());
        captchaService.commonSmsSend(sourceType.getTemplateCode(), catchMobileInfo.getAreaId(), catchMobileInfo.getPhone(), extras);
    }

    @Override
    public void checkVerifyCodeByCardNo(String ffpCardNo, VerifyCodeCheckRequest verifyCodeCheckRequest, String channelCode, HttpServletRequest request) {
        SourceType sourceType = SourceType.toCatchSourceType(verifyCodeCheckRequest.getType());
        if (null == sourceType) {
            throw ServiceException.fail("短信类型非法");
        }
        //获取手机号
        CrmPhoneInfo mobileInfo = memberService.toCatchMobileInfo(ffpCardNo, channelCode);
        checkVerifyCodeByFfp(sourceType.value, mobileInfo.getPhone());
        String redisKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.SMS_REDIS + mobileInfo.getPhone() + ":" + verifyCodeCheckRequest.getType());
        String redisCode = (String) redisUtil.get(redisKey);
        if (StringUtils.isEmpty(redisCode) || !verifyCodeCheckRequest.getVerifyCode().equals(redisCode)) {
            throw ServiceException.fail("验证码错误或已失效");
        }
        redisUtil.del(redisKey);
    }

    @Override
    public void checkSmsCommonCodeByFfp(String ffpCardNo, String verifyCode, String channelCode, BizDto bizDto, boolean check) {
        //获取手机号
        CrmPhoneInfo catchMobileInfo = memberService.toCatchMobileInfo(ffpCardNo, channelCode);
        if (check) {
            checkVerifyCodeByFfp(SourceType.EC_ITINERARY.value, catchMobileInfo.getPhone());
        }
        String redisKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.SMS_REDIS + catchMobileInfo.getPhone() + ":" + SourceType.EC_ITINERARY.value);
        checkVeryCode(redisKey,verifyCode);
    }

    @Override
    public void checkVeryCode(String redisKey, String code) {
        String check = redisUtil.getStr(redisKey);
        if (StringUtils.isBlank(check)) {
            throw MultiLangServiceException.fail(CommonErrorCode.VERIFY_CODE_INVALID);
        }
        if (!check.equals(code)) {
            throw MultiLangServiceException.fail(CommonErrorCode.VERIFY_CODE_ERROR);
        }
        //验证成功清除
        redisUtil.del(redisKey);
    }


    /**
     * @param type
     * @param phone
     * @return void
     * <AUTHOR>
     * @Description 基于会员发送短信校验
     * @Date 14:05 2024/7/24
     **/
    private void sendCheckVerifyCodeByFfp(String type, String phone) {
        Map<String, Integer> sendCheckVerifyCodeByFfp = smsConfig.getSendCheckVerifyCodeByFfp();
        Integer maxCount = sendCheckVerifyCodeByFfp.get(type);
        if (null == maxCount || maxCount < 0) {
            ServiceException.fail("未配置发送规则，请稍后再试！");
        }
        String dataStr = DateUtil.dateToString(new Date(), DateUtil.YYYY_MM_DD_PATTERN);
        String redisKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.SEND_VERIFY_CODE_BY_CARDNO + type + ":" + dataStr + ":" + phone);
        if (null == redisUtils.get(redisKey)) {
            redisUtils.set(redisKey, 1L, 24 * 60 * 60L);
        } else {
            Integer count = (Integer) redisUtils.get(redisKey);
            if (count >= maxCount) {
                ServiceException.fail("发送过于频繁，请明日再试！");
            } else {
                long nowCount = redisUtils.increment(redisKey, 1L, 25 * 60 * 60L);
                if (nowCount > maxCount) {
                    ServiceException.fail("发送过于频繁，请明日再试！");
                }
            }
        }
    }

    /**
     * @param type
     * @param phone
     * @return void
     * <AUTHOR>
     * @Description 短信验证码发送次数校验
     * @Date 9:54 2024/7/25
     **/

    private void checkVerifyCodeByFfp(String type, String phone) {
        Map<String, Integer> sendCheckVerifyCodeByFfp = smsConfig.getSendCheckVerifyCodeByFfp();
        Integer maxCount = sendCheckVerifyCodeByFfp.get(type);
        if (null == maxCount || maxCount < 0) {
            ServiceException.fail("未配置发送规则，请稍后再试！");
        }
        String dataStr = DateUtil.dateToString(new Date(), DateUtil.YYYY_MM_DD_PATTERN);
        String redisKey = redisConstantConfig.getLocalRedisKey(redisConstantConfig.SEND_VERIFY_CODE_BY_CARDNO + type + ":" + dataStr + ":" + phone);
        Integer nowCount = (Integer) redisUtils.get(redisKey);
        if (null != nowCount && nowCount > maxCount) {
            ServiceException.fail("发送过于频繁，请明日再试！");
        }
    }
}
