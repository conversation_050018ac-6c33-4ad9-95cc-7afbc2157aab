package com.juneyaoair.oneorder.common;

import com.juneyaoair.oneorder.common.request.HORequestBuilder;

public abstract class ServiceBase<REQ, RES> implements Processor<REQ, RES> {

    ServiceBase(HORequestBuilder<REQ> req) {
        this._request = req;
    }

    @Override
    public RES process() {
        return processCore();
    }

    public abstract RES processCore();

    @Override
    public HORequestBuilder<REQ> getRequest() {
        return _request;
    }

    private final HORequestBuilder<REQ> _request;
}
