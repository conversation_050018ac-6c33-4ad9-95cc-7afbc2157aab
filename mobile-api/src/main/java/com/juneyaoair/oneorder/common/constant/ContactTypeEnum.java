package com.juneyaoair.oneorder.common.constant;

/**
 * <AUTHOR>
 * @description  联系方式类型
 * @date 2018/8/10  8:46.
 */
public enum ContactTypeEnum {
    UNKNOW(0,"UNKNOW","未知"),
    MOBILE(1,"<PERSON><PERSON><PERSON><PERSON>","手机号"),
    HOME_TEL(2,"HOME_TEL","家庭电话"),
    OFFICE_TEL(3,"UNKNOW","单位电话"),
    FAX(4,"FAX","传真"),
    EMAIL(5,"EMAIL","邮箱"),
    B2CUSERNAME(6,"B2CUSERNAME","B2C用户名"),
    WEIXIN(7,"WEIXIN","微信号"),
    OTHER(8,"OTHER","其他");
    private  int code;
    private  String eName;
    private  String desc;

    ContactTypeEnum(int code, String eName, String desc) {
        this.code = code;
        this.eName = eName;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String geteName() {
        return eName;
    }

    public String getDesc() {
        return desc;
    }
}
