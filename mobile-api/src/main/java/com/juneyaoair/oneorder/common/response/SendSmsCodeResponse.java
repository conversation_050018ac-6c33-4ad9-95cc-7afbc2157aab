package com.juneyaoair.oneorder.common.response;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


@XmlRootElement(name = "SendSmsCodeResponse")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"ResultCode","ErrorInfo"})
public class SendSmsCodeResponse<T> {
	private String ResultCode; //结果代码1001 － 成功，其它失败
	private String ErrorInfo; //错误信息
	private String ResultInfo;
	private T objData;
	public String getResultCode() {
		return ResultCode;
	}
	public void setResultCode(String resultCode) {
		ResultCode = resultCode;
	}
	public String getErrorInfo() {
		return ErrorInfo;
	}
	public void setErrorInfo(String errorInfo) {
		ErrorInfo = errorInfo;
	}

	public String getResultInfo() {
		return ResultInfo;
	}

	public void setResultInfo(String resultInfo) {
		ResultInfo = resultInfo;
	}

	public T getObjData() {
		return objData;
	}

	public void setObjData(T objData) {
		this.objData = objData;
	}
}