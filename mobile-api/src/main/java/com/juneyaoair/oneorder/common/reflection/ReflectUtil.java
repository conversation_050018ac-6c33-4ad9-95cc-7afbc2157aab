package com.juneyaoair.oneorder.common.reflection;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;

@Slf4j
public class ReflectUtil {

    public static Field findField(Class<?> headClazz, String fieldName) {

        try {
            return headClazz.getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
//            log.warn(e.getMessage());
        }
        return null;
    }

}
