package com.juneyaoair.oneorder.common.util;


import brave.Tracing;
import brave.propagation.TraceContext;

public class TraceUtil {

    public static String getTraceId() {
        Tracing tracing = SpringContextUtil.getBean("tracing", Tracing.class);
        if (tracing != null) {
            TraceContext parent = tracing.currentTraceContext().get();
            return parent.traceIdString();
        }
        return null;
    }
}