package com.juneyaoair.oneorder.common.constant;

/**
 * <AUTHOR>
 * @description 同盾事件类型
 * @date 2019/12/9  10:08.
 */
public enum TongDunEventType {
    LOOKUP("lookup","航班查询"),
    BROWSE("Browse","浏览"),
    REGISTER("Register","注册"),
    LOGIN("Login","登录"),
    TRADE("Trade","交易"),
    MARKETING("Marketing","领券"),
    CLICK("Click", "预约"),
    PAYMENT("Payment", "抢购"),
    SMS("SMS", "发送短信验证码"),
    MODIFY("Modify","修改事件（如:修改消费密码）"),
    VERIFICATION("VERIFICATION","客票验证"),
    ;
    private String eventType;
    private String eventTypeName;

    TongDunEventType(String eventType, String eventTypeName) {
        this.eventType = eventType;
        this.eventTypeName = eventTypeName;
    }

    public String getEventType() {
        return eventType;
    }

    public String getEventTypeName() {
        return eventTypeName;
    }
}
