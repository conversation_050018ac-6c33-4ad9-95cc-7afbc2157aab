package com.juneyaoair.oneorder.common.common;

/**
 * <AUTHOR>
 * @date 2019/1/16 15:51
 */
public enum LoginTypeEnum {
    SMS("1"),
    PASSWORD("2"),
    Alipay("3"),
    WeChat("4"),
    QQ("5"),
    APPLE("6"),
    ;
    private  String name;

    LoginTypeEnum(String name){
        this.name = name;
    }

    /**
     * 检验枚举类型
     * @param v
     * @return
     */
    public static boolean checkEnum(String v){
        for (LoginTypeEnum c: LoginTypeEnum.values()) {
            if (c.name.equals(v)) {
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param v
     * @return
     */
    public static LoginTypeEnum initEnum(String v){
        for (LoginTypeEnum c: LoginTypeEnum.values()) {
            if (c.name.equals(v)) {
                return c;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }
}
