package com.juneyaoair.oneorder.common.concurrency;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;

@Slf4j
public class ThreadPoolImpl implements ThreadPool {

    private final ExecutorService _executor;
    private final HOTaskDecorator _taskDecorator;

    public ThreadPoolImpl(int corePoolSize, int maxPoolSize, int waitingQSize, HOTaskDecorator decorator) {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                1000,/* keepAliveTime */
                TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<Runnable>(waitingQSize));
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());//throw exception
        this._executor = executor;
        this._taskDecorator = decorator;
    }

    @Override
    public void execute(Runnable runnable) {
        if (_taskDecorator != null) {
            runnable = _taskDecorator.decorate(runnable);
        }
        _executor.execute(runnable);
    }

    @Override
    public <T> TaskFuture<T> submit(String taskName, ParaTask<T> futureTask) {
        if (_taskDecorator != null) {
            futureTask = _taskDecorator.decorate(futureTask);
        }
        Future<T> future = _executor.submit(
                futureTask
        );
        return new TaskFutureImpl<T>(taskName, future);
    }
}
