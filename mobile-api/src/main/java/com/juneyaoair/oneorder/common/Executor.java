package com.juneyaoair.oneorder.common;

import com.juneyaoair.oneorder.common.concurrency.HOContext;
import com.juneyaoair.oneorder.common.response.ResponseContract;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class Executor {
    public final static String CHANNEL_NO = "ChannelNo";
    public final static String CHANNEL_CODE = "Channelcode";
    public final static String channel_code = "channel_code";
    public final static String CHANNEL_NO_SMALL = "channelNo";
    public static final String FFP_ID = "ffpId";
    public static final String FFP_NO = "ffpNo";
    public static final String CLIENT_IP = "clientIp";
    public static final String BLACK_BOX = "BlackBox";
    public static final String LANG = "language";


    @Deprecated
    public static <REQ, RES> RES execute(String methodName, Processor<REQ, RES> processor) {
        RES ret = null;
        try {
//            initContext(processor);
            ret = processor.process();
            if (ret instanceof ResponseContract) {
                ((ResponseContract) ret).toContract();
            }
        } catch (Exception e) {
            throw e;
        } finally {
            HOContext.remove();
        }
        return ret;
    }

}
