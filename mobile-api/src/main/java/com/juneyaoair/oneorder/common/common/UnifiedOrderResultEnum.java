package com.juneyaoair.oneorder.common.common;

/**
 * <AUTHOR>
 * @description 统一订单结果错误代码地址
 * @date 2018/9/11  16:44.
 */
public enum UnifiedOrderResultEnum {
    /** code message */
    SUCCESS("1001","成功"),
    R1003("1003","未查询到相关数据"),
    R1005("1005","购买数量已达每人限购上限！"),
    ERROR_PNR("2022","PNR异常"),
    PASSENGER_NAME_LENGTH_ERROR("5116","乘客姓名长度错误，需大于0小于等于25个字符"),
    OUT_OF_STOCK("10203","库存不足"),
    REPEATED_PASS("90008","常用乘机人重复"),
    NO_PASS("90009","常用乘机人信息不存在"),
    CHECK_90010("90010","常用乘机人证件信息重复"),
    CHECK_90406("90406", "接口异常"),// 统一订单接口出错，此异常不可展示给前端用户
    CHECK_5026("5026", "无可升舱舱位或运价"),
    CHECK_90022("90022","常用报销凭证信息不存在"),
    CHECK_85001("85001", ""),// 订单系统可视化异常，此异常可直接展示给前端用户
    CHECK_2041("2041", "不符合活动规则"),
    CHECK_9999("9999","同航班WIFI产品只允许购买一次"),
    CHECK_90023("90023","修改操作证件号码重复"),
    FAIL("-9999","异常");
    /**
     * 结果代码
     */
    private String resultCode;
    /**
     * 结果描述
     */
    private String resultInfo;

    UnifiedOrderResultEnum(String resultCode, String resultInfo) {
        this.resultCode = resultCode;
        this.resultInfo = resultInfo;
    }

    public String getResultCode() {
        return resultCode;
    }

    public String getResultInfo() {
        return resultInfo;
    }

}
