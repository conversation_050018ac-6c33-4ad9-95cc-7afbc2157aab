package com.juneyaoair.oneorder.common.concurrency;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Slf4j
public class TaskFutureImpl<T> implements TaskFuture<T> {

    public TaskFutureImpl(String taskName, Future<T> future) {
        this._future = future;
        this._taskName = taskName;
    }

    @Override
    public T getSimple() {
        try {
            return _future.get(TIMEOUT, TIMEUNIT);
        } catch (ExecutionException e) {
            log.error("[traceId]-{}[taskName]-{}[ExecutionException]-{}", HOContext.getContext().getId(), this._taskName, e.getMessage());
        } catch (InterruptedException e) {
            log.error("[traceId]-{}[taskName]-{}[InterruptedException]-{}", HOContext.getContext().getId(), this._taskName, e.getMessage());
            Thread.currentThread().interrupt();
        } catch (TimeoutException e) {
            log.error("[traceId]-{}[taskName]-{}[TimeoutException]-{}", HOContext.getContext().getId(), this._taskName, e.getMessage());
        }
        return null;
    }

    @Override
    public boolean cancel(boolean mayInterruptIfRunning) {
        return _future.cancel(mayInterruptIfRunning);
    }

    @Override
    public boolean isCancelled() {
        return _future.isCancelled();
    }

    @Override
    public boolean isDone() {
        return _future.isDone();
    }

    @Override
    public T get() throws InterruptedException, ExecutionException {
        return _future.get();
    }

    @Override
    public T get(long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
        return _future.get(timeout, unit);
    }

    private final Future<T> _future;
    private final String _taskName;
    private static int TIMEOUT = 10000;
    private static TimeUnit TIMEUNIT = TimeUnit.MILLISECONDS;
}
