package com.juneyaoair.oneorder.common.constant;

public enum EmdCouponStatusEnum {
    /** */
    O("OPEN FOR USE"),
    C("CHECK IN"),
    L("LIFT"),
    F("FLOWN"),
    R("REFUND"),
    V("VOID"),
    E("EXCHANGE"),
            ;

    /** 描述 */
    private final String description;

    EmdCouponStatusEnum(String description) {
        this.description = description;
    }

    //航司类型检验
    public static EmdCouponStatusEnum checkEmdCouponStatusEnum(String v){
        for (EmdCouponStatusEnum c: EmdCouponStatusEnum.values()) {
            if (c.name().equals(v)) {
                return c;
            }
        }
        return null;
    }

    public String getDescription() {
        return description;
    }
}
