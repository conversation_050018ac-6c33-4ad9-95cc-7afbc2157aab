package com.juneyaoair.oneorder.common.concurrency;

import com.juneyaoair.oneorder.common.request.RequestHead;

import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 用于装取线程内数据
 * 支持多线程: 使用ThreadPoolImpl线程池
 */
public class HOContext {

    public HOContext() {
        this(UUID.randomUUID().toString());
    }

    public HOContext(String id) {
        _id = id;
        _attr = new ConcurrentHashMap<>();
    }

    public static void setContext(HOContext context) {
        s_current.set(context);
    }

    public static void remove(){
        s_current.remove();
    }

    public static HOContext getContext() {
        return s_current.get();
    }

    public static void setHead(RequestHead req) {
        s_current.get()._attr.put(Head, req);
    }

    public String getId() {
        return _id;
    }

    public RequestHead getHead() {
        return (RequestHead) s_current.get()._attr.get(Head);
    }

    private final static String Head = "RequestHead";
    private final String _id;
    private static final ThreadLocal<HOContext> s_current = new ThreadLocal<>();
    private ConcurrentHashMap<String, Object> _attr;
}
