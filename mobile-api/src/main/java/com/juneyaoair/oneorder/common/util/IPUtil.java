package com.juneyaoair.oneorder.common.util;

import io.netty.util.internal.StringUtil;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by felix on 2016/3/14.
 */
public class IPUtil {

    public static String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_CLIENT_IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_X_FORWARDED_FOR");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
            }
        } else if (ip.length() > 15) {
            String[] ips = ip.split(",");
            for (int index = 0; index < ips.length; index++) {
                String strIp = (String) ips[index];
                if (!("unknown".equalsIgnoreCase(strIp))) {
                    ip = strIp;
                    break;
                }
            }
        }
        return ip;
    }

    public static String getIpAddrStr(HttpServletRequest request) {
        String ipStr = "";
        String xRealIp = request.getHeader("X-Real-IP");
        ipStr += "/X-Real-IP:" + xRealIp;
        String ip = request.getHeader("x-forwarded-for");
        ipStr += "/x-forwarded-for:" + ip;
        String ipProxy = request.getHeader("Proxy-Client-IP");
        ipStr += "/Proxy-Client-IP:" + ipProxy;
        String ipWlProxy = request.getHeader("WL-Proxy-Client-IP");
        ipStr += "/WL-Proxy-Client-IP:" + ipWlProxy;
        String ipHttpIp = request.getHeader("HTTP_CLIENT_IP");
        ipStr += "/HTTP_CLIENT_IP:" + ipHttpIp;
        String ipHttpXIp = request.getHeader("HTTP_X_FORWARDED_FOR");
        ipStr += "/HTTP_X_FORWARDED_FOR:" + ipHttpXIp;
        String ipOther = request.getRemoteAddr();
        ipStr += "/Other:" + ipOther;
        return ipStr;
    }

    public static String getLocalIp() {
        try {
            InetAddress addr = InetAddress.getLocalHost();
            return addr.getHostAddress();
        } catch (UnknownHostException e) {
            return "127.0.0.1";
        }
    }

    /**
     * 校验是否指定格式IP
     *
     * @param ip
     * @param ipPattern
     * @return
     */
    public static boolean filterIP(String ip, String ipPattern) {
        if (StringUtil.isNullOrEmpty(ip) || StringUtil.isNullOrEmpty(ipPattern)) {
            return false;
        }
        //内部IP不计入次数限制
        Pattern pattern = Pattern.compile(ipPattern);
        Matcher matcher = pattern.matcher(ip);
        if (matcher.matches()) {//ip是一个正确的内网ip格式
            return true;
        }
        return false;
    }

    /**
     * 判断是否多级代理访问
     *
     * @param request
     * @return
     */
    public static boolean isMultyProxy(HttpServletRequest request) {
        //String ip = request.getHeader("x-forwarded-for");
        return false;
    }

    public static void main(String args[]) {
        double dou = Double.parseDouble("");
        System.out.println(dou);
    }

    /**
     * 获取自定义的Map头部信息
     *
     * @param clientIp 用户真实的IP
     * @param type     不同的类型设置不同的头部信息
     * @return
     */
    public static Map<String, String> getHeaderMap(String clientIp, String type) {
        return getHeaderMap(clientIp, null, type);
    }

    /**
     * 获取自定义的Map头部信息
     *
     * @param clientIp 用户真实的IP
     * @param type     不同的类型设置不同的头部信息 默认为空
     * @return
     */
    public static Map<String, String> getHeaderMap(String clientIp, Map<String, String> paramMap, String type) {
        Map<String, String> headMap = new HashMap<>();
        if (paramMap != null && !paramMap.isEmpty()) {
            headMap.putAll(paramMap);
        }
        //默认的配置选项
        headMap.put("X-HO-Client-Host", clientIp);
        return headMap;
    }

}
