package com.juneyaoair.oneorder.common.service;

import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.GeetestDto;
import com.juneyaoair.oneorder.crm.dto.request.CheckSmsCode;
import com.juneyaoair.oneorder.crm.dto.request.SendSmsCodeFfpRequest;
import com.juneyaoair.oneorder.crm.dto.request.VerifyCodeCheckRequest;
import com.juneyaoair.oneorder.crm.dto.request.VerifyCodeSendRequest;
import com.juneyaoair.oneorder.crm.dto.common.SourceType;

import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName SmsCodeSendService
 * @Description 短信验证码发送-目前只用于电子行程单的短信验证码发送，如需做其他用，需要根据业务逻辑做适当调整
 * <AUTHOR>
 * @Date 2024/7/23 14:43
 * @Version 1.0
 */
public interface SmsCodeSendService {


    /**
     * @param ffpCardNo   会员卡号
     * @param geetestDto  极验请求体
     * @param channelCode 渠道号
     * @param request     网络请求体
     * @return void
     * <AUTHOR>
     * @Description 发送短信验证码 电子行程单专用
     * @Date 13:58 2024/7/25
     **/
    void sendVerifyCodeByFfp(String ffpCardNo, GeetestDto geetestDto, String channelCode, HttpServletRequest request);

    /**
     * <AUTHOR>
     * @Description 通过会员卡号发送验证码
     * @Date 21:08 2024/8/6
     * @param ffpCardNo 会员卡号
     * @param verifyCodeSendRequest
     * @param channelCode
     * @param request
     * @return void
     **/

    void sendVerifyCodeByCardNo(String ffpCardNo, VerifyCodeSendRequest verifyCodeSendRequest, String channelCode, HttpServletRequest request);

    /**
     * <AUTHOR>
     * @Description 校验验证码
     * @Date 21:37 2024/8/6
     * @param ffpCardNo
     * @param verifyCodeCheckRequest
     * @param channelCode
     * @param request
     * @return void
     **/

    void checkVerifyCodeByCardNo(String ffpCardNo, VerifyCodeCheckRequest verifyCodeCheckRequest, String channelCode, HttpServletRequest request);


    /**
     * @param ffpCardNo   会员卡号
     * @param verifyCode  验证码
     * @param channelCode 渠道号
     * @return void
     * <AUTHOR>
     * @Description 校验手机验证码
     * @Date 14:08 2024/7/25
     **/
    void checkSmsCommonCodeByFfp(String ffpCardNo, String verifyCode, String channelCode, BizDto bizDto,boolean check);

    /**
     * 检查验证码是否正确
     *
     * @param redisKey 存储验证码的Redis键该键用于从Redis中获取预期的验证码值
     * @param code 用户输入的验证码字符串此方法将此代码与Redis中存储的预期值进行比较以验证用户输入的正确性
     */
    void checkVeryCode(String redisKey,String code);
}
