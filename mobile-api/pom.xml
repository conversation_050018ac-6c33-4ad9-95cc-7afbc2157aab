<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.juneyaoair.oneorder</groupId>
        <artifactId>apiMobile</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>mobile-api</artifactId>
    <description>第三方服务</description>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>mobile-config-apollo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>component-logback</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>component-exception</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>checker-qual</artifactId>
                    <groupId>org.checkerframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>error_prone_annotations</artifactId>
                    <groupId>com.google.errorprone</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>component-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>component-dto</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair</groupId>
            <artifactId>flightbasic-consumer-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jsr305</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
                <exclusion>
                    <groupId>commons-configuration</groupId>
                    <artifactId>commons-configuration</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20230227</version>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>mobile-config-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair</groupId>
            <artifactId>flightbasic-provider-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair</groupId>
            <artifactId>flightbasic-dto</artifactId>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair</groupId>
            <artifactId>message-client</artifactId>
            <version>1.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.fastjson</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>component-core</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair</groupId>
            <artifactId>smsModel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.horder</groupId>
            <artifactId>tinyurl-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>component-restresult</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-configuration</artifactId>
                    <groupId>commons-configuration</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>woodstox-core</artifactId>
                    <groupId>com.fasterxml.woodstox</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>component-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>fare-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.ow2.asm</groupId>
                    <artifactId>asm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.maven</groupId>
                    <artifactId>maven-plugin-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.maven</groupId>
                    <artifactId>maven-artifact-manager</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.maven</groupId>
                    <artifactId>maven-settings</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.maven.doxia</groupId>
                    <artifactId>doxia-sink-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>xml-apis</groupId>
                    <artifactId>xml-apis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.maven</groupId>
                    <artifactId>maven-artifact</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.plexus</groupId>
                    <artifactId>plexus-interpolation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.plexus</groupId>
                    <artifactId>plexus-component-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.plexus</groupId>
                    <artifactId>plexus-utils</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.maven</groupId>
                    <artifactId>maven-model</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.plexus</groupId>
                    <artifactId>plexus-container-default</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.oneorder</groupId>
            <artifactId>order-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-beanutils</artifactId>
                    <groupId>commons-beanutils</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>doxia-sink-api</artifactId>
                    <groupId>org.apache.maven.doxia</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsqlparser</artifactId>
                    <groupId>com.github.jsqlparser</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-artifact</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-artifact-manager</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-model</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-plugin-api</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-repository-metadata</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-settings</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>plexus-component-annotations</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>plexus-interpolation</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>plexus-utils</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>xml-apis</artifactId>
                    <groupId>xml-apis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>plexus-container-default</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair.dsop</groupId>
            <artifactId>sdk-java</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
<!--        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
            <version>1.4.7</version>
        </dependency>-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-email</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>${maven-enforcer-plugin_version}</version>
                <executions>
                    <execution>
                        <id>enforce-jar-dependencies</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <rules>
                        <evaluateBeanshell>
                            <condition>
                                print("[INFO] [BaseComponent] mobile-api enforcer");1==1
                            </condition>
                        </evaluateBeanshell>
                        <bannedDependencies>
                            <!--用于控制是否检查传递依赖-->
                            <searchTransitive>true</searchTransitive>
                            <!--禁止引入的jar包-->
                            <excludes>
                                <exclude>com.juneyaoair.oneorder:component-tools</exclude>
                            </excludes>
                        </bannedDependencies>
                    </rules>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>